package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.GeneratedDocumentDto;

import java.util.List;

public interface GeneratedDocumentService {
    List<GeneratedDocumentDto> getAllGeneratedDocuments();
    GeneratedDocumentDto getGeneratedDocumentById(Long id);
    List<GeneratedDocumentDto> getGeneratedDocumentsByTemplateId(Long templateId);
    List<GeneratedDocumentDto> getGeneratedDocumentsByClientId(Long clientId);
    List<GeneratedDocumentDto> getGeneratedDocumentsByDealId(Long dealId);
    List<GeneratedDocumentDto> getGeneratedDocumentsByStatus(String status);
    GeneratedDocumentDto createGeneratedDocument(GeneratedDocumentDto generatedDocumentDto);
    GeneratedDocumentDto updateGeneratedDocument(Long id, GeneratedDocumentDto generatedDocumentDto);
    GeneratedDocumentDto updateGeneratedDocumentStatus(Long id, String status);
    void deleteGeneratedDocument(Long id);
}
