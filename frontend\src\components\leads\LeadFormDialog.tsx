import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { Lead, leadService } from "@/services/leadService";

export type LeadFormData = Lead;

interface LeadFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (lead: LeadFormData) => void;
  initialData?: LeadFormData;
  title?: string;
}

const defaultLead: LeadFormData = {
  name: "",
  company: "",
  email: "",
  phone: "",
  status: "New",
  source: "Website",
};

const LeadFormDialog: React.FC<LeadFormDialogProps> = ({
  open,
  onO<PERSON>Change,
  onSave,
  initialData,
  title = "Add Lead",
}) => {
  const [formData, setFormData] = useState<LeadFormData>(initialData || defaultLead);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Function to check if email already exists
  const checkEmailExists = async (email: string): Promise<boolean> => {
    try {
      // Make a GET request through the proxy to check if the email exists
      const response = await fetch(`/api/leads/check-email?email=${encodeURIComponent(email)}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        },
        credentials: 'include'
      });

      // If the endpoint doesn't exist, we'll try an alternative approach
      if (response.status === 404) {
        // Get all leads and check manually
        const allLeadsResponse = await fetch('http://localhost:8091/api/leads', {
          method: 'GET',
          headers: {
            'Accept': 'application/json'
          }
        });

        if (!allLeadsResponse.ok) {
          return false; // Assume email doesn't exist if we can't check
        }

        const allLeads = await allLeadsResponse.json();
        return Array.isArray(allLeads) && allLeads.some(lead =>
          lead.email && lead.email.toLowerCase() === email.toLowerCase()
        );
      }

      // If the endpoint exists, use its response
      if (response.ok) {
        const result = await response.json();
        return result.exists === true;
      }

      return false;
    } catch (error) {
      console.error("Error checking email existence:", error);
      return false; // Assume email doesn't exist if check fails
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validate form
    if (!formData.name.trim()) {
      toast.error("Name is required");
      setIsSubmitting(false);
      return;
    }

    if (!formData.email.trim()) {
      toast.error("Email is required");
      setIsSubmitting(false);
      return;
    }

    // Check for duplicate email if creating a new lead
    if (!initialData?.id) {
      const emailExists = await checkEmailExists(formData.email);
      if (emailExists) {
        toast.error("Email already exists", {
          description: "A lead with this email address already exists in the database."
        });
        setIsSubmitting(false);
        return;
      }
    }

    // Show loading toast
    const loadingToast = toast.loading(
      initialData?.id ? "Updating lead..." : "Creating new lead..."
    );

    try {
      // Prepare the lead data with current date if not provided
      const leadData = {
        ...formData,
        dateAdded: formData.dateAdded || new Date().toISOString().split("T")[0],
        // Ensure these fields are not empty
        company: formData.company || "",
        phone: formData.phone || "",
        status: formData.status || "New",
        source: formData.source || "Website"
      };

      console.log("Submitting lead data:", leadData);

      // Determine if we're creating or updating
      let savedLead: Lead;

      if (initialData?.id) {
        // Update existing lead
        console.log(`Updating lead with ID ${initialData.id}`);
        savedLead = await leadService.updateLead(initialData.id, leadData);
        toast.success("Lead updated successfully", {
          description: `Lead "${savedLead.name}" has been updated.`,
          id: loadingToast
        });
      } else {
        // Create new lead
        console.log("Creating new lead");

        try {
          // Direct API call to ensure we get a proper response
          const response = await fetch('http://localhost:8091/api/leads', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            body: JSON.stringify(leadData),
          });

          console.log('Response status:', response.status);

          if (!response.ok) {
            const errorText = await response.text();
            console.error('API error response:', errorText);

            // Handle specific error codes
            if (response.status === 701 || errorText.includes("701") ||
                errorText.includes("unique") || errorText.includes("duplicate") ||
                errorText.includes("already exists")) {
              throw new Error("A lead with this email already exists in the database.");
            } else {
              throw new Error(`API error: ${response.status} ${response.statusText}`);
            }
          }

          const responseText = await response.text();
          console.log('Raw API response:', responseText);

          if (!responseText.trim()) {
            throw new Error('Empty response from API - data not saved to database');
          }

          try {
            savedLead = JSON.parse(responseText);
            console.log('Created lead from API:', savedLead);

            if (savedLead && savedLead.id) {
              toast.success("Lead created successfully", {
                description: `Lead "${savedLead.name}" has been created with ID: ${savedLead.id}`,
                id: loadingToast
              });
            } else {
              throw new Error('Invalid lead data returned from API');
            }
          } catch (parseError) {
            console.error('Error parsing JSON response:', parseError);
            throw new Error('Invalid JSON response from API - data not saved to database');
          }
        } catch (createError) {
          console.error("Error creating lead:", createError);

          // Show a more user-friendly error message for duplicate emails
          if (createError instanceof Error &&
              (createError.message.includes("email already exists") ||
               createError.message.includes("701") ||
               createError.message.includes("unique") ||
               createError.message.includes("duplicate"))) {
            toast.error("Duplicate Email", {
              description: "A lead with this email address already exists in the database.",
              id: loadingToast
            });
          } else {
            toast.error("Failed to create lead", {
              description: createError instanceof Error ? createError.message : "Please check your input and try again.",
              id: loadingToast
            });
          }

          setIsSubmitting(false);
          return; // Exit early on error
        }
      }

      console.log("Saved lead:", savedLead);

      // Pass the saved lead back to the parent component
      onSave(savedLead);

      // Close the dialog
      onOpenChange(false);
    } catch (error) {
      console.error("Error saving lead:", error);

      // Dismiss the loading toast and show error
      toast.error("Failed to save lead", {
        description: error instanceof Error ? error.message : "Please try again.",
        id: loadingToast
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange} modal={true}>
      <DialogContent className="sm:max-w-[500px] p-0 overflow-visible" style={{ zIndex: 9999 }}>
        <div className="p-6 pb-2">
          <DialogTitle className="text-xl font-semibold">{title}</DialogTitle>
        </div>

        <form onSubmit={handleSubmit} className="p-6 pt-2 overflow-visible">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 mb-4">
            <div>
              <Label htmlFor="name" className="block text-sm font-medium mb-1">
                Name
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="John Smith"
                required
                className="w-full"
              />
            </div>

            <div>
              <Label htmlFor="company" className="block text-sm font-medium mb-1">
                Company
              </Label>
              <Input
                id="company"
                name="company"
                value={formData.company}
                onChange={handleChange}
                placeholder="Tech Solutions Inc."
                className="w-full"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 mb-4">
            <div>
              <Label htmlFor="email" className="block text-sm font-medium mb-1">
                Email
              </Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
                required
                className="w-full"
              />
            </div>

            <div>
              <Label htmlFor="phone" className="block text-sm font-medium mb-1">
                Phone
              </Label>
              <Input
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="+****************"
                className="w-full"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 mb-6">
            <div>
              <Label htmlFor="status" className="block text-sm font-medium mb-1">
                Status
              </Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleSelectChange("status", value)}
              >
                <SelectTrigger id="status" className="w-full">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent position="popper" className="z-[10000]">
                  <SelectItem value="New">New</SelectItem>
                  <SelectItem value="Contacted">Contacted</SelectItem>
                  <SelectItem value="Qualified">Qualified</SelectItem>
                  <SelectItem value="Not Interested">Not Interested</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="source" className="block text-sm font-medium mb-1">
                Source
              </Label>
              <Select
                value={formData.source}
                onValueChange={(value) => handleSelectChange("source", value)}
              >
                <SelectTrigger id="source" className="w-full">
                  <SelectValue placeholder="Select source" />
                </SelectTrigger>
                <SelectContent position="popper" className="z-[10001]">
                  <SelectItem value="Website">Website</SelectItem>
                  <SelectItem value="Referral">Referral</SelectItem>
                  <SelectItem value="LinkedIn">LinkedIn</SelectItem>
                  <SelectItem value="Trade Show">Trade Show</SelectItem>
                  <SelectItem value="Email Campaign">Email Campaign</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
              className="px-4"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4"
            >
              {isSubmitting ? "Saving..." : "Save Lead"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default LeadFormDialog;
