package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.InvoiceTypeDto;
import com.redberyl.invoiceapp.entity.InvoiceType;
import com.redberyl.invoiceapp.repository.InvoiceTypeRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Public controller for invoice types with no security constraints
 * This ensures frontend can always access invoice type data
 */
@RestController
@CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060"})
@Tag(name = "Public Invoice Types", description = "Public API for invoice types with no security")
public class PublicInvoiceTypeController {

    @Autowired
    private InvoiceTypeRepository invoiceTypeRepository;

    /**
     * Get all invoice types with no security constraints
     * @return List of invoice types
     */
    @GetMapping("/public/invoice-types")
    @Operation(summary = "Get all invoice types (public)", description = "Get all invoice types with no security constraints")
    public ResponseEntity<List<InvoiceTypeDto>> getAllInvoiceTypesPublic() {
        try {
            List<InvoiceType> invoiceTypes = invoiceTypeRepository.findAll();

            if (invoiceTypes.isEmpty()) {
                // If no data in database, return sample data
                List<InvoiceTypeDto> sampleTypes = createSampleInvoiceTypes();
                return new ResponseEntity<>(sampleTypes, HttpStatus.OK);
            }

            // Convert entities to DTOs
            List<InvoiceTypeDto> invoiceTypeDtos = invoiceTypes.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());

            return new ResponseEntity<>(invoiceTypeDtos, HttpStatus.OK);
        } catch (Exception e) {
            // If any error occurs, return sample data
            List<InvoiceTypeDto> sampleTypes = createSampleInvoiceTypes();
            return new ResponseEntity<>(sampleTypes, HttpStatus.OK);
        }
    }

    /**
     * Convert entity to DTO
     * @param invoiceType Entity
     * @return DTO
     */
    private InvoiceTypeDto convertToDto(InvoiceType invoiceType) {
        return InvoiceTypeDto.builder()
                .id(invoiceType.getId())
                .invoiceType(invoiceType.getInvoiceType())
                .typeDesc(invoiceType.getTypeDesc())
                .build();
    }

    /**
     * Create sample invoice types
     * @return List of sample invoice types
     */
    private List<InvoiceTypeDto> createSampleInvoiceTypes() {
        List<InvoiceTypeDto> sampleTypes = new ArrayList<>();

        sampleTypes.add(InvoiceTypeDto.builder()
                .id(1L)
                .invoiceType("Standard")
                .typeDesc("Regular invoice for services or products")
                .build());

        sampleTypes.add(InvoiceTypeDto.builder()
                .id(2L)
                .invoiceType("Proforma")
                .typeDesc("Preliminary bill of sale sent to buyers in advance of a shipment or delivery")
                .build());

        sampleTypes.add(InvoiceTypeDto.builder()
                .id(3L)
                .invoiceType("Credit Note")
                .typeDesc("Document issued to indicate a return of funds")
                .build());

        sampleTypes.add(InvoiceTypeDto.builder()
                .id(4L)
                .invoiceType("Debit Note")
                .typeDesc("Document issued to request additional payment")
                .build());

        sampleTypes.add(InvoiceTypeDto.builder()
                .id(5L)
                .invoiceType("Tax Invoice")
                .typeDesc("Invoice that includes tax information")
                .build());

        return sampleTypes;
    }
}
