import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  Divider,
  Code,
  Alert,
  AlertIcon,
  Spinner,
  useToast,
  Select,
  FormControl,
  FormLabel,
  Card,
  CardHeader,
  CardBody,
  CardFooter,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td
} from '@chakra-ui/react';

const BdmDebugger = () => {
  const toast = useToast();
  const [bdms, setBdms] = useState([]);
  const [rawResponse, setRawResponse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedBdmId, setSelectedBdmId] = useState(null);
  const [endpoint, setEndpoint] = useState('http://localhost:8091/api/v1/bdms');

  const fetchBdms = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log(`Fetching BDMs from ${endpoint}...`);

      // Create basic auth header if needed
      const authHeader = 'Basic ' + btoa('admin:admin123');

      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': authHeader
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch BDMs: ${response.status}`);
      }

      const responseData = await response.json();
      console.log('BDM API response:', responseData);
      setRawResponse(responseData);

      // Extract BDMs from the nested structure based on the API response format
      let bdmsData = [];

      // Check for the specific format from BdmController.java
      if (responseData && responseData.success && responseData.data && responseData.data.content) {
        // This is the format from /api/v1/bdms endpoint
        console.log('Found BDMs in data.data.content:', responseData.data.content);
        bdmsData = responseData.data.content;
      } else if (Array.isArray(responseData)) {
        // Direct array format
        bdmsData = responseData;
      } else if (responseData && responseData.data && Array.isArray(responseData.data)) {
        // Data in data property
        bdmsData = responseData.data;
      } else if (responseData && responseData.content && Array.isArray(responseData.content)) {
        // Data in content property
        bdmsData = responseData.content;
      } else {
        console.error('Unexpected BDM data format:', responseData);
        throw new Error('Unexpected data format from BDM API');
      }

      // Sort BDMs by name for better usability
      bdmsData.sort((a, b) => {
        if (!a.name) return 1;
        if (!b.name) return -1;
        return a.name.localeCompare(b.name);
      });

      console.log(`Found ${bdmsData.length} BDMs:`, bdmsData);
      setBdms(bdmsData);
    } catch (err) {
      console.error('Error fetching BDMs:', err);
      setError(err.message);
      toast({
        title: 'Error fetching BDMs',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBdms();
  }, [endpoint]);

  const handleEndpointChange = (e) => {
    setEndpoint(e.target.value);
  };

  const handleBdmChange = (e) => {
    const value = e.target.value;
    setSelectedBdmId(value ? Number(value) : null);
  };

  const getSelectedBdm = () => {
    return bdms.find(bdm => bdm.id === selectedBdmId);
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box>
          <Heading size="lg">BDM Debugger</Heading>
          <Text mt={2} color="gray.600">
            This tool helps debug issues with BDM data fetching and display
          </Text>
        </Box>

        <Card>
          <CardHeader>
            <Heading size="md">API Endpoint</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              <FormControl>
                <FormLabel>Select Endpoint</FormLabel>
                <Select value={endpoint} onChange={handleEndpointChange}>
                  <option value="http://localhost:8091/api/v1/bdms">
                    /api/v1/bdms (Primary)
                  </option>
                  <option value="http://localhost:8091/api/v1/bdms?size=100">
                    /api/v1/bdms?size=100 (With pagination)
                  </option>
                  <option value="http://localhost:8091/api/bdms">
                    /api/bdms (Alternative)
                  </option>
                </Select>
              </FormControl>

              <Button
                colorScheme="blue"
                onClick={fetchBdms}
                isLoading={loading}
              >
                Fetch BDMs
              </Button>
            </VStack>
          </CardBody>
        </Card>

        {error && (
          <Alert status="error">
            <AlertIcon />
            <Text>{error}</Text>
          </Alert>
        )}

        {loading ? (
          <Box textAlign="center" py={10}>
            <Spinner size="xl" />
            <Text mt={4}>Loading BDMs...</Text>
          </Box>
        ) : (
          <>
            <Card>
              <CardHeader>
                <Heading size="md">BDM Data ({bdms.length} BDMs found)</Heading>
              </CardHeader>
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <FormControl>
                    <FormLabel>Test BDM Dropdown</FormLabel>
                    <Select
                      placeholder="Select BDM"
                      value={selectedBdmId || ''}
                      onChange={handleBdmChange}
                    >
                      {bdms.map((bdm) => (
                        <option key={bdm.id} value={bdm.id}>
                          {bdm.name || '[No Name]'} 
                          {bdm.email ? ` (${bdm.email})` : ''}
                        </option>
                      ))}
                    </Select>
                  </FormControl>

                  {selectedBdmId && (
                    <Box p={4} bg="blue.50" borderRadius="md">
                      <Heading size="sm" mb={2}>Selected BDM Details:</Heading>
                      <Code p={2} display="block" whiteSpace="pre">
                        {JSON.stringify(getSelectedBdm(), null, 2)}
                      </Code>
                    </Box>
                  )}

                  <Box overflowX="auto">
                    <Table variant="simple" size="sm">
                      <Thead>
                        <Tr>
                          <Th>ID</Th>
                          <Th>Name</Th>
                          <Th>Email</Th>
                          <Th>Phone</Th>
                          <Th>Properties</Th>
                        </Tr>
                      </Thead>
                      <Tbody>
                        {bdms.map((bdm, index) => (
                          <Tr key={bdm.id || index}>
                            <Td>{bdm.id}</Td>
                            <Td>{bdm.name || <Text color="red.500">[No Name]</Text>}</Td>
                            <Td>{bdm.email || '-'}</Td>
                            <Td>{bdm.phone || '-'}</Td>
                            <Td>
                              <Code fontSize="xs">
                                {Object.keys(bdm).join(', ')}
                              </Code>
                            </Td>
                          </Tr>
                        ))}
                      </Tbody>
                    </Table>
                  </Box>
                </VStack>
              </CardBody>
            </Card>

            <Card>
              <CardHeader>
                <Heading size="md">Raw API Response</Heading>
              </CardHeader>
              <CardBody>
                <Box overflowX="auto" maxH="400px" overflowY="auto">
                  <Code p={2} display="block" whiteSpace="pre">
                    {JSON.stringify(rawResponse, null, 2)}
                  </Code>
                </Box>
              </CardBody>
            </Card>
          </>
        )}
      </VStack>
    </Container>
  );
};

export default BdmDebugger;
