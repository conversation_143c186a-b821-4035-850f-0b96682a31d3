package com.redberyl.invoiceapp.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/examples")
public class ExampleController {

    @GetMapping("/tax-rate")
    public ResponseEntity<Map<String, Object>> getTaxRateExample() {
        Map<String, Object> example = new HashMap<>();
        example.put("taxTypeId", 1);
        example.put("rate", new BigDecimal("18.0"));
        example.put("effectiveFrom", LocalDate.of(2025, 4, 17));
        example.put("effectiveTo", LocalDate.of(2025, 6, 22));

        return ResponseEntity.ok(example);
    }

    @GetMapping("/tax-rate-response")
    public ResponseEntity<Map<String, Object>> getTaxRateResponseExample() {
        Map<String, Object> example = new HashMap<>();
        example.put("id", 1);
        example.put("taxTypeId", 1);

        Map<String, Object> taxType = new HashMap<>();
        taxType.put("id", 1);
        taxType.put("taxType", "GST");
        taxType.put("taxTypeDescription", "Goods and Services Tax");
        example.put("taxType", taxType);

        example.put("rate", new BigDecimal("18.0"));
        example.put("effectiveFrom", LocalDate.of(2025, 4, 17));
        example.put("effectiveTo", LocalDate.of(2025, 6, 22));
        example.put("created_at", LocalDateTime.now().toString());
        example.put("updated_at", LocalDateTime.now().toString());

        return ResponseEntity.ok(example);
    }
}
