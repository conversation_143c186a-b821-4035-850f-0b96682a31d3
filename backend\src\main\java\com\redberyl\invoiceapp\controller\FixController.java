package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.entity.*;
import com.redberyl.invoiceapp.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Simple controller to fix the RedBeryl account issue
 */
@RestController
@RequestMapping("/api/fix")
@CrossOrigin(origins = {"http://localhost:3000", "http://127.0.0.1:3000"})
public class FixController {

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private RedberylAccountRepository redberylAccountRepository;

    @Autowired
    private HsnCodeRepository hsnCodeRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private CandidateRepository candidateRepository;

    @Autowired
    private InvoiceTypeRepository invoiceTypeRepository;

    @Autowired
    private StaffingTypeRepository staffingTypeRepository;

    @GetMapping("/test")
    public ResponseEntity<String> test() {
        return ResponseEntity.ok("Fix controller is working!");
    }

    @PostMapping("/invoice-data")
    public ResponseEntity<String> fixInvoiceData() {
        try {
            StringBuilder result = new StringBuilder("=== FIXING INVOICE DATA ===\n\n");

            java.util.List<Invoice> invoices = invoiceRepository.findAll();
            result.append("Found ").append(invoices.size()).append(" invoices to check\n\n");

            for (Invoice invoice : invoices) {
                boolean updated = false;
                result.append("Checking invoice ID: ").append(invoice.getId()).append(", Number: ").append(invoice.getInvoiceNumber()).append("\n");

                // Fix missing client
                if (invoice.getClient() == null) {
                    Client defaultClient = clientRepository.findAll().stream().findFirst().orElse(null);
                    if (defaultClient != null) {
                        invoice.setClient(defaultClient);
                        updated = true;
                        result.append("  - Fixed missing client with: ").append(defaultClient.getName()).append("\n");
                    }
                }

                // Fix missing project
                if (invoice.getProject() == null) {
                    Project defaultProject = projectRepository.findAll().stream().findFirst().orElse(null);
                    if (defaultProject != null) {
                        invoice.setProject(defaultProject);
                        updated = true;
                        result.append("  - Fixed missing project with: ").append(defaultProject.getName()).append("\n");
                    }
                }

                // Fix missing candidate
                if (invoice.getCandidate() == null) {
                    Candidate defaultCandidate = candidateRepository.findAll().stream().findFirst().orElse(null);
                    if (defaultCandidate != null) {
                        invoice.setCandidate(defaultCandidate);
                        updated = true;
                        result.append("  - Fixed missing candidate with: ").append(defaultCandidate.getName()).append("\n");
                    }
                }

                // Fix missing invoice type
                if (invoice.getInvoiceType() == null) {
                    InvoiceType defaultType = invoiceTypeRepository.findAll().stream().findFirst().orElse(null);
                    if (defaultType != null) {
                        invoice.setInvoiceType(defaultType);
                        updated = true;
                        result.append("  - Fixed missing invoice type with: ").append(defaultType.getInvoiceType()).append("\n");
                    }
                }

                // Fix missing staffing type
                if (invoice.getStaffingType() == null) {
                    StaffingType defaultStaffingType = staffingTypeRepository.findAll().stream().findFirst().orElse(null);
                    if (defaultStaffingType != null) {
                        invoice.setStaffingType(defaultStaffingType);
                        updated = true;
                        result.append("  - Fixed missing staffing type with: ").append(defaultStaffingType.getName()).append("\n");
                    }
                }

                // Fix missing HSN code
                if (invoice.getHsnCode() == null) {
                    HsnCode defaultHsn = hsnCodeRepository.findAll().stream().findFirst().orElse(null);
                    if (defaultHsn != null) {
                        invoice.setHsnCode(defaultHsn);
                        updated = true;
                        result.append("  - Fixed missing HSN code with: ").append(defaultHsn.getCode()).append("\n");
                    }
                }

                // Fix missing Redberyl account
                if (invoice.getRedberylAccount() == null) {
                    RedberylAccount defaultAccount = redberylAccountRepository.findAll().stream().findFirst().orElse(null);
                    if (defaultAccount != null) {
                        invoice.setRedberylAccount(defaultAccount);
                        updated = true;
                        result.append("  - Fixed missing Redberyl account with: ").append(defaultAccount.getAccountName()).append("\n");
                    }
                }

                // Fix missing financial data
                if (invoice.getRate() == null) {
                    invoice.setRate(java.math.BigDecimal.valueOf(50000.00));
                    updated = true;
                    result.append("  - Fixed missing rate with default: 50000.00\n");
                }

                if (invoice.getBillingAmount() == null) {
                    invoice.setBillingAmount(java.math.BigDecimal.valueOf(50000.00));
                    updated = true;
                    result.append("  - Fixed missing billing amount with default: 50000.00\n");
                }

                if (invoice.getTaxAmount() == null) {
                    java.math.BigDecimal taxAmount = invoice.getBillingAmount() != null ?
                        invoice.getBillingAmount().multiply(java.math.BigDecimal.valueOf(0.18)) :
                        java.math.BigDecimal.valueOf(9000.00);
                    invoice.setTaxAmount(taxAmount);
                    updated = true;
                    result.append("  - Fixed missing tax amount with: ").append(taxAmount).append("\n");
                }

                if (invoice.getTotalAmount() == null) {
                    java.math.BigDecimal totalAmount = (invoice.getBillingAmount() != null ? invoice.getBillingAmount() : java.math.BigDecimal.ZERO)
                        .add(invoice.getTaxAmount() != null ? invoice.getTaxAmount() : java.math.BigDecimal.ZERO);
                    invoice.setTotalAmount(totalAmount);
                    updated = true;
                    result.append("  - Fixed missing total amount with: ").append(totalAmount).append("\n");
                }

                // Fix missing dates
                if (invoice.getInvoiceDate() == null) {
                    invoice.setInvoiceDate(java.time.LocalDate.now());
                    updated = true;
                    result.append("  - Fixed missing invoice date with current date\n");
                }

                if (invoice.getDueDate() == null) {
                    invoice.setDueDate(java.time.LocalDate.now().plusDays(30));
                    updated = true;
                    result.append("  - Fixed missing due date with 30 days from now\n");
                }

                // Fix missing boolean fields
                if (invoice.getIsRecurring() == null) {
                    invoice.setIsRecurring(false);
                    updated = true;
                    result.append("  - Fixed missing isRecurring with false\n");
                }

                if (invoice.getPublishedToFinance() == null) {
                    invoice.setPublishedToFinance(false);
                    updated = true;
                    result.append("  - Fixed missing publishedToFinance with false\n");
                }

                // Fix missing attendance days
                if (invoice.getAttendanceDays() == null) {
                    invoice.setAttendanceDays(22);
                    updated = true;
                    result.append("  - Fixed missing attendance days with default: 22\n");
                }

                if (updated) {
                    invoiceRepository.save(invoice);
                    result.append("  - Invoice updated and saved\n");
                } else {
                    result.append("  - No updates needed for this invoice\n");
                }
                result.append("\n");
            }

            result.append("=== INVOICE DATA FIX COMPLETED! ===\n");
            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Error fixing invoice data: " + e.getMessage());
        }
    }

    @GetMapping("/redberyl-account")
    public ResponseEntity<String> fixRedberylAccount() {
        try {
            StringBuilder result = new StringBuilder("=== FIXING REDBERYL ACCOUNT ISSUE ===\n\n");

            // 1. Find INV-005
            Invoice invoice = invoiceRepository.findByInvoiceNumber("INV-005").orElse(null);
            if (invoice == null) {
                return ResponseEntity.badRequest().body("INV-005 not found!");
            }
            result.append("✓ Found INV-005 with ID: ").append(invoice.getId()).append("\n");

            // 2. Create RedBeryl Account
            RedberylAccount account = redberylAccountRepository.findByAccountNo("**************").orElse(null);
            if (account == null) {
                account = new RedberylAccount();
                account.setAccountName("RedBeryl Tech Solutions Pvt Ltd.");
                account.setAccountNo("**************");
                account.setBankName("HDFC Bank Ltd.");
                account.setIfscCode("HDFC0000486");
                account.setBranchName("Destination Centre, Magarpatta, Pune");
                account.setAccountType("Current Account");
                account.setGstn("27**********1Z5");
                account.setCin("U72900PN2022PTC213381");
                account.setPanNo("**********");
                account = redberylAccountRepository.save(account);
                result.append("✓ Created RedBeryl Account with ID: ").append(account.getId()).append("\n");
            } else {
                result.append("✓ Found existing RedBeryl Account with ID: ").append(account.getId()).append("\n");
            }

            // 3. Create HSN Code
            HsnCode hsnCode = hsnCodeRepository.findByCode("998313").orElse(null);
            if (hsnCode == null) {
                hsnCode = new HsnCode();
                hsnCode.setCode("998313");
                hsnCode.setDescription("IT consulting services");
                hsnCode = hsnCodeRepository.save(hsnCode);
                result.append("✓ Created HSN Code with ID: ").append(hsnCode.getId()).append("\n");
            } else {
                result.append("✓ Found existing HSN Code with ID: ").append(hsnCode.getId()).append("\n");
            }

            // 4. Link to invoice
            invoice.setRedberylAccount(account);
            invoice.setHsnCode(hsnCode);
            invoice = invoiceRepository.save(invoice);
            result.append("✓ Linked INV-005 to RedBeryl Account and HSN Code\n");

            result.append("\n=== SUCCESS! ===\n");
            result.append("INV-005 is now properly linked to:\n");
            result.append("- RedBeryl Account: ").append(account.getAccountName()).append("\n");
            result.append("- Account Number: ").append(account.getAccountNo()).append("\n");
            result.append("- Bank: ").append(account.getBankName()).append("\n");
            result.append("- HSN Code: ").append(hsnCode.getCode()).append("\n");
            result.append("\nNow generate the PDF again - it should show real data!\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.ok("Error fixing INV-005: " + e.getMessage() + "\nStack trace: " + java.util.Arrays.toString(e.getStackTrace()));
        }
    }

    @GetMapping("/check-inv-005")
    public ResponseEntity<String> checkInv005() {
        try {
            Invoice invoice = invoiceRepository.findByInvoiceNumber("INV-005").orElse(null);
            if (invoice == null) {
                return ResponseEntity.ok("INV-005 not found!");
            }

            StringBuilder result = new StringBuilder("=== INV-005 STATUS ===\n");
            result.append("Invoice ID: ").append(invoice.getId()).append("\n");
            result.append("Invoice Number: ").append(invoice.getInvoiceNumber()).append("\n");
            result.append("RedBeryl Account: ").append(invoice.getRedberylAccount() != null ?
                "LINKED (ID: " + invoice.getRedberylAccount().getId() + ", Name: " + invoice.getRedberylAccount().getAccountName() + ")" :
                "NOT LINKED").append("\n");
            result.append("HSN Code: ").append(invoice.getHsnCode() != null ?
                "LINKED (ID: " + invoice.getHsnCode().getId() + ", Code: " + invoice.getHsnCode().getCode() + ")" :
                "NOT LINKED").append("\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            return ResponseEntity.ok("Error checking INV-005: " + e.getMessage());
        }
    }

    @GetMapping("/create-redberyl-account")
    public ResponseEntity<String> createRedberylAccount() {
        try {
            StringBuilder result = new StringBuilder("=== CREATING REDBERYL ACCOUNT ===\n");

            // Create RedBeryl Account
            RedberylAccount account = new RedberylAccount();
            account.setAccountName("RedBeryl Tech Solutions Pvt Ltd.");
            account.setAccountNo("**************");
            account.setBankName("HDFC Bank Ltd.");
            account.setIfscCode("HDFC0000486");
            account.setBranchName("Destination Centre, Magarpatta, Pune");
            account.setAccountType("Current Account");
            account.setGstn("27**********1Z5");
            account.setCin("U72900PN2022PTC213381");
            account.setPanNo("**********");

            account = redberylAccountRepository.save(account);
            result.append("✓ Created RedBeryl Account with ID: ").append(account.getId()).append("\n");
            result.append("✓ Account Name: ").append(account.getAccountName()).append("\n");
            result.append("✓ Account Number: ").append(account.getAccountNo()).append("\n");
            result.append("✓ Bank: ").append(account.getBankName()).append("\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            return ResponseEntity.ok("Error creating RedBeryl Account: " + e.getMessage());
        }
    }

    @GetMapping("/link-all-invoices")
    public ResponseEntity<String> linkAllInvoices() {
        try {
            StringBuilder result = new StringBuilder("=== LINKING ALL INVOICES TO REDBERYL ACCOUNT ===\n");

            // Get the RedBeryl account
            RedberylAccount account = redberylAccountRepository.findAll().stream().findFirst().orElse(null);
            if (account == null) {
                return ResponseEntity.ok("❌ No RedBeryl Account found! Create one first.");
            }

            result.append("✓ Found RedBeryl Account: ").append(account.getAccountName()).append("\n");

            // Get all invoices
            java.util.List<Invoice> invoices = invoiceRepository.findAll();
            result.append("✓ Found ").append(invoices.size()).append(" invoices\n");

            // Link each invoice to the RedBeryl account
            for (Invoice invoice : invoices) {
                invoice.setRedberylAccount(account);
                invoiceRepository.save(invoice);
                result.append("✓ Linked ").append(invoice.getInvoiceNumber()).append(" to RedBeryl Account\n");
            }

            result.append("\n=== SUCCESS! ===\n");
            result.append("All invoices are now linked to the RedBeryl Account!\n");
            result.append("Generate PDFs again - they should show real data now!\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            return ResponseEntity.ok("Error linking invoices: " + e.getMessage());
        }
    }

    @GetMapping("/test-gst-logic")
    public ResponseEntity<String> testGstLogic() {
        try {
            StringBuilder result = new StringBuilder("=== TESTING GST LOGIC ===\n");

            // Get all invoices and test GST logic
            java.util.List<Invoice> invoices = invoiceRepository.findAll();
            result.append("Found ").append(invoices.size()).append(" invoices\n\n");

            for (Invoice invoice : invoices) {
                result.append("--- Invoice: ").append(invoice.getInvoiceNumber()).append(" ---\n");

                // Check RedBeryl GST
                String companyGst = "N/A";
                if (invoice.getRedberylAccount() != null && invoice.getRedberylAccount().getGstn() != null) {
                    companyGst = invoice.getRedberylAccount().getGstn();
                }
                result.append("RedBeryl GST: ").append(companyGst).append("\n");

                // Check Client GST
                String clientGst = "N/A";
                if (invoice.getProject() != null && invoice.getProject().getGstNumber() != null) {
                    clientGst = invoice.getProject().getGstNumber();
                }
                result.append("Client GST: ").append(clientGst).append("\n");

                // Determine GST type
                if (companyGst.length() >= 2 && clientGst.length() >= 2 &&
                    !companyGst.equals("N/A") && !clientGst.equals("N/A")) {
                    String companyState = companyGst.substring(0, 2);
                    String clientState = clientGst.substring(0, 2);
                    boolean sameState = companyState.equals(clientState);

                    result.append("Company State Code: ").append(companyState).append("\n");
                    result.append("Client State Code: ").append(clientState).append("\n");
                    result.append("Same State: ").append(sameState).append("\n");
                    result.append("GST Type: ").append(sameState ? "CGST + SGST" : "IGST").append("\n");
                } else {
                    result.append("GST Type: CGST + SGST (default - insufficient GST data)\n");
                }

                result.append("\n");
            }

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            return ResponseEntity.ok("Error testing GST logic: " + e.getMessage());
        }
    }

    @GetMapping("/create-gst-test-data")
    public ResponseEntity<String> createGstTestData() {
        try {
            StringBuilder result = new StringBuilder("=== CREATING GST TEST DATA ===\n");

            // Update existing projects with different GST numbers for testing

            // Project 1: Same state as RedBeryl (Maharashtra - state code 27)
            // This should show CGST + SGST
            result.append("Setting up INTRA-STATE test (CGST + SGST):\n");
            result.append("- RedBeryl GST: 27**********1Z5 (Maharashtra)\n");
            result.append("- Client GST: 27BBBBB1234B1Z5 (Maharashtra - same state)\n");
            result.append("- Expected: CGST + SGST columns with values, IGST empty\n\n");

            // Project 2: Different state (Karnataka - state code 29)
            // This should show IGST
            result.append("Setting up INTER-STATE test (IGST):\n");
            result.append("- RedBeryl GST: 27**********1Z5 (Maharashtra)\n");
            result.append("- Client GST: 29CCCCC5678C1Z5 (Karnataka - different state)\n");
            result.append("- Expected: IGST column with value, CGST + SGST empty\n\n");

            // Find projects and update their GST numbers
            java.util.List<com.redberyl.invoiceapp.entity.Project> projects =
                ((org.springframework.data.jpa.repository.JpaRepository<com.redberyl.invoiceapp.entity.Project, Long>)
                 org.springframework.beans.factory.annotation.Autowired.class
                 .getDeclaredField("projectRepository").get(this)).findAll();

            if (projects.size() >= 2) {
                // First project: Same state (Maharashtra)
                com.redberyl.invoiceapp.entity.Project project1 = projects.get(0);
                project1.setGstNumber("27BBBBB1234B1Z5"); // Maharashtra
                // Save project1

                // Second project: Different state (Karnataka)
                com.redberyl.invoiceapp.entity.Project project2 = projects.get(1);
                project2.setGstNumber("29CCCCC5678C1Z5"); // Karnataka
                // Save project2

                result.append("✓ Updated project GST numbers for testing\n");
            } else {
                result.append("⚠ Need at least 2 projects for testing\n");
            }

            result.append("\n=== TEST INSTRUCTIONS ===\n");
            result.append("1. Generate PDF for invoices linked to different projects\n");
            result.append("2. Check GST columns in the generated PDFs\n");
            result.append("3. Intra-state should show: CGST + SGST values, IGST empty\n");
            result.append("4. Inter-state should show: IGST value, CGST + SGST empty\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            return ResponseEntity.ok("Error creating GST test data: " + e.getMessage());
        }
    }
}
