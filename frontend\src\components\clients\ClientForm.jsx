import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  VStack,
  HStack,
  SimpleGrid,
  Heading,
  useToast,
  Spinner,
  Text
} from '@chakra-ui/react';
import BdmDropdown from '../BdmDropdown';

const ClientForm = ({ initialData, onSubmit, isEdit = false }) => {
  const toast = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    contactPerson: '',
    website: '',
    bdmId: null,
    commissionPercentage: '',
    billingAddress: '',
    shippingAddress: '',
    gstNumber: '',
    panNumber: '',
    cinNumber: '',
    notes: ''
  });

  // Initialize form with data if provided
  useEffect(() => {
    if (initialData) {
      setFormData({
        ...initialData,
        // Ensure bdmId is a number
        bdmId: initialData.bdmId ? Number(initialData.bdmId) : null,
        // Ensure commissionPercentage is a string for the input
        commissionPercentage: initialData.commissionPercentage ? String(initialData.commissionPercentage) : ''
      });
    }
  }, [initialData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleBdmChange = (bdmId) => {
    console.log('BDM selected:', bdmId);
    setFormData(prev => ({ ...prev, bdmId }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.name || !formData.email || !formData.phone || !formData.contactPerson || !formData.billingAddress) {
      toast({
        title: 'Missing required fields',
        description: 'Please fill in all required fields',
        status: 'error',
        duration: 3060,
        isClosable: true
      });
      return;
    }

    // Validate BDM selection
    if (!formData.bdmId) {
      toast({
        title: 'BDM selection required',
        description: 'Please select a BDM',
        status: 'error',
        duration: 3060,
        isClosable: true
      });
      return;
    }

    try {
      setIsSubmitting(true);
      
      // Prepare data for API
      const clientData = {
        ...formData,
        // Ensure bdmId is a number
        bdmId: Number(formData.bdmId),
        // Convert commissionPercentage to a string for the API
        commissionPercentage: formData.commissionPercentage ? String(formData.commissionPercentage) : '0'
      };
      
      console.log('Submitting client data:', clientData);
      
      // Call the onSubmit function passed from parent
      await onSubmit(clientData);
      
      toast({
        title: `Client ${isEdit ? 'updated' : 'created'} successfully`,
        status: 'success',
        duration: 3060,
        isClosable: true
      });
    } catch (error) {
      console.error('Error submitting client:', error);
      toast({
        title: `Error ${isEdit ? 'updating' : 'creating'} client`,
        description: error.message || 'An unexpected error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box as="form" onSubmit={handleSubmit} width="100%">
      <VStack spacing={6} align="stretch">
        <Heading size="md">{isEdit ? 'Edit Client' : 'Create New Client'}</Heading>
        
        {/* Basic Information */}
        <Box>
          <Heading size="sm" mb={3}>Basic Information</Heading>
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
            <FormControl isRequired>
              <FormLabel>Company Name</FormLabel>
              <Input 
                name="name" 
                value={formData.name} 
                onChange={handleChange} 
                placeholder="ABC Technologies Pvt Ltd"
              />
            </FormControl>
            
            <FormControl isRequired>
              <FormLabel>Email</FormLabel>
              <Input 
                name="email" 
                type="email" 
                value={formData.email} 
                onChange={handleChange} 
                placeholder="<EMAIL>"
              />
            </FormControl>
            
            <FormControl isRequired>
              <FormLabel>Phone</FormLabel>
              <Input 
                name="phone" 
                value={formData.phone} 
                onChange={handleChange} 
                placeholder="+91-9876543210"
              />
            </FormControl>
            
            <FormControl isRequired>
              <FormLabel>Contact Person</FormLabel>
              <Input 
                name="contactPerson" 
                value={formData.contactPerson} 
                onChange={handleChange} 
                placeholder="Rahul Sharma"
              />
            </FormControl>
            
            <FormControl>
              <FormLabel>Website (Optional)</FormLabel>
              <Input 
                name="website" 
                value={formData.website} 
                onChange={handleChange} 
                placeholder="https://www.abctech.com"
              />
            </FormControl>
          </SimpleGrid>
        </Box>
        
        {/* BDM Information */}
        <Box>
          <Heading size="sm" mb={3}>BDM Information</Heading>
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
            <BdmDropdown 
              value={formData.bdmId} 
              onChange={handleBdmChange}
              required={true}
            />
            
            <FormControl>
              <FormLabel>Commission Percentage (Optional)</FormLabel>
              <Input 
                name="commissionPercentage" 
                type="number" 
                step="0.01" 
                min="0" 
                max="100"
                value={formData.commissionPercentage} 
                onChange={handleChange} 
                placeholder="5.00"
              />
            </FormControl>
          </SimpleGrid>
        </Box>
        
        {/* Address Information */}
        <Box>
          <Heading size="sm" mb={3}>Address Information</Heading>
          <VStack spacing={4} align="stretch">
            <FormControl isRequired>
              <FormLabel>Billing Address</FormLabel>
              <Textarea 
                name="billingAddress" 
                value={formData.billingAddress} 
                onChange={handleChange} 
                placeholder="123, Tech Park, Sector 45, Bangalore, Karnataka, India - 560045"
                rows={3}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel>Shipping Address (Optional)</FormLabel>
              <Textarea 
                name="shippingAddress" 
                value={formData.shippingAddress} 
                onChange={handleChange} 
                placeholder="456, Industrial Area, Sector 67, Noida, Uttar Pradesh, India - 201301"
                rows={3}
              />
            </FormControl>
          </VStack>
        </Box>
        
        {/* Legal Information */}
        <Box>
          <Heading size="sm" mb={3}>Legal Information</Heading>
          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
            <FormControl>
              <FormLabel>GST Number (Optional)</FormLabel>
              <Input 
                name="gstNumber" 
                value={formData.gstNumber} 
                onChange={handleChange} 
                placeholder="29**********1Z5"
              />
            </FormControl>
            
            <FormControl>
              <FormLabel>PAN Number (Optional)</FormLabel>
              <Input 
                name="panNumber" 
                value={formData.panNumber} 
                onChange={handleChange} 
                placeholder="**********"
              />
            </FormControl>
            
            <FormControl>
              <FormLabel>CIN Number (Optional)</FormLabel>
              <Input 
                name="cinNumber" 
                value={formData.cinNumber} 
                onChange={handleChange} 
                placeholder="U12345KA2010PTC012345"
              />
            </FormControl>
          </SimpleGrid>
        </Box>
        
        {/* Additional Information */}
        <Box>
          <Heading size="sm" mb={3}>Additional Information</Heading>
          <FormControl>
            <FormLabel>Notes (Optional)</FormLabel>
            <Textarea 
              name="notes" 
              value={formData.notes} 
              onChange={handleChange} 
              placeholder="Preferred vendor for software development and consulting services."
              rows={3}
            />
          </FormControl>
        </Box>
        
        {/* Submit Button */}
        <HStack justifyContent="flex-end" mt={4}>
          <Button 
            type="submit" 
            colorScheme="blue" 
            isLoading={isSubmitting}
            loadingText={isEdit ? "Updating" : "Creating"}
          >
            {isEdit ? 'Update Client' : 'Create Client'}
          </Button>
        </HStack>
      </VStack>
    </Box>
  );
};

export default ClientForm;
