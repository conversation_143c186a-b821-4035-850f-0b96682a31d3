package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.InvoiceMilestone;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface InvoiceMilestoneRepository extends JpaRepository<InvoiceMilestone, Long> {
    List<InvoiceMilestone> findByInvoiceId(Long invoiceId);
    List<InvoiceMilestone> findByMilestoneDateBefore(LocalDate date);
}
