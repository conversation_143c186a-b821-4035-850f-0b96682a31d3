package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.StaffingTypeDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.StaffingTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@CrossOrigin(origins = { "http://localhost:3000", "http://127.0.0.1:3000" }, maxAge = 3600)
@Tag(name = "Staffing Type", description = "Staffing Type management API")
public class StaffingTypeController {

    @Autowired
    private StaffingTypeService staffingTypeService;

    @GetMapping("/staffing-types/getAll")
    @Operation(summary = "Get all staffing types", description = "Get all staffing types")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Staffing types found"),
            @ApiResponse(responseCode = "204", description = "No staffing types found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<StaffingTypeDto>> getAllStaffingTypes() {
        try {
            List<StaffingTypeDto> staffingTypes = staffingTypeService.getAllStaffingTypes();
            return new ResponseEntity<>(staffingTypes, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/staffing-types/getById/{id}")
    @Operation(summary = "Get staffing type by ID", description = "Get staffing type by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Staffing type found"),
            @ApiResponse(responseCode = "404", description = "Staffing type not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<StaffingTypeDto> getStaffingTypeById(@PathVariable Long id) {
        StaffingTypeDto staffingType = staffingTypeService.getStaffingTypeById(id);
        return new ResponseEntity<>(staffingType, HttpStatus.OK);
    }

    @GetMapping("/staffing-types/getByName/{name}")
    @Operation(summary = "Get staffing type by name", description = "Get staffing type by name")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Staffing type found"),
            @ApiResponse(responseCode = "404", description = "Staffing type not found"),
            @ApiResponse(responseCode = "400", description = "Invalid name supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<StaffingTypeDto> getStaffingTypeByName(@PathVariable String name) {
        StaffingTypeDto staffingType = staffingTypeService.getStaffingTypeByName(name);
        return new ResponseEntity<>(staffingType, HttpStatus.OK);
    }

    @PostMapping("/staffing-types/create")
    @Operation(summary = "Create staffing type", description = "Create staffing type")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Staffing type created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<StaffingTypeDto> createStaffingType(@Valid @RequestBody StaffingTypeDto staffingTypeDto) {
        StaffingTypeDto createdStaffingType = staffingTypeService.createStaffingType(staffingTypeDto);
        return new ResponseEntity<>(createdStaffingType, HttpStatus.CREATED);
    }

    @PutMapping("/staffing-types/update/{id}")
    @Operation(summary = "Update staffing type", description = "Update staffing type")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Staffing type updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Staffing type not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<StaffingTypeDto> updateStaffingType(@PathVariable Long id,
            @Valid @RequestBody StaffingTypeDto staffingTypeDto) {
        StaffingTypeDto updatedStaffingType = staffingTypeService.updateStaffingType(id, staffingTypeDto);
        return new ResponseEntity<>(updatedStaffingType, HttpStatus.OK);
    }

    @DeleteMapping("/staffing-types/deleteById/{id}")
    @Operation(summary = "Delete staffing type", description = "Delete staffing type")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Staffing type deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied or staffing type is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "Staffing type not found")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteStaffingType(@PathVariable Long id) {
        staffingTypeService.deleteStaffingType(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }


}
