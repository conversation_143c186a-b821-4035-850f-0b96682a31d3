<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Standalone Client Dropdown Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2 {
            color: #2563eb;
        }
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
        }
        select, input {
            width: 100%;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #d1d5db;
            font-size: 1rem;
        }
        button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }
        button:hover {
            background-color: #1d4ed8;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .success {
            background-color: #d1fae5;
            color: #065f46;
            border-left: 4px solid #10b981;
        }
        .error {
            background-color: #fee2e2;
            color: #b91c1c;
            border-left: 4px solid #ef4444;
        }
        .info {
            background-color: #dbeafe;
            color: #1e40af;
            border-left: 4px solid #3b82f6;
        }
        pre {
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Standalone Client Dropdown Test</h1>
    
    <div class="card">
        <h2>Mock Client Data</h2>
        <p>This page uses mock client data to simulate the client dropdown functionality.</p>
        
        <div class="form-group">
            <label for="client-select">Select Client:</label>
            <select id="client-select">
                <option value="">Select a client</option>
                <!-- Mock clients will be added here -->
            </select>
        </div>
        
        <div class="form-group">
            <label for="project-name">Project Name:</label>
            <input type="text" id="project-name" placeholder="Enter project name">
        </div>
        
        <div class="form-group">
            <label for="project-description">Description:</label>
            <input type="text" id="project-description" placeholder="Enter project description">
        </div>
        
        <button id="save-btn">Save Project</button>
    </div>
    
    <div class="card">
        <h2>Project Data</h2>
        <div id="project-data" class="status info">No project saved yet</div>
        <pre id="project-json"></pre>
    </div>
    
    <div class="card">
        <h2>Mock API Test</h2>
        <p>Test the mock API to simulate backend functionality:</p>
        <button id="test-api-btn">Test Mock API</button>
        <div id="api-status" class="status info">Not tested yet</div>
        <pre id="api-response"></pre>
    </div>
    
    <script>
        // Mock client data
        const mockClients = [
            { id: 1, name: "Acme Corporation" },
            { id: 2, name: "Globex Industries" },
            { id: 3, name: "Initech Solutions" },
            { id: 4, name: "Umbrella Corp" },
            { id: 5, name: "Stark Industries" }
        ];
        
        // DOM elements
        const clientSelect = document.getElementById('client-select');
        const projectName = document.getElementById('project-name');
        const projectDescription = document.getElementById('project-description');
        const saveBtn = document.getElementById('save-btn');
        const projectData = document.getElementById('project-data');
        const projectJson = document.getElementById('project-json');
        const testApiBtn = document.getElementById('test-api-btn');
        const apiStatus = document.getElementById('api-status');
        const apiResponse = document.getElementById('api-response');
        
        // Populate client dropdown with mock data
        function populateClientDropdown() {
            // Clear existing options except the first one
            while (clientSelect.options.length > 1) {
                clientSelect.remove(1);
            }
            
            // Add mock clients
            mockClients.forEach(client => {
                const option = document.createElement('option');
                option.value = client.id;
                option.textContent = client.name;
                clientSelect.appendChild(option);
            });
        }
        
        // Save project data
        function saveProject() {
            const clientId = clientSelect.value;
            const name = projectName.value.trim();
            const description = projectDescription.value.trim();
            
            if (!clientId) {
                projectData.className = 'status error';
                projectData.textContent = 'Please select a client';
                return;
            }
            
            if (!name) {
                projectData.className = 'status error';
                projectData.textContent = 'Please enter a project name';
                return;
            }
            
            // Find the selected client
            const selectedClient = mockClients.find(client => client.id == clientId);
            
            // Create project object
            const project = {
                id: Math.floor(Math.random() * 1000) + 1,
                name,
                description: description || 'No description',
                client: {
                    id: selectedClient.id,
                    name: selectedClient.name
                },
                status: 'Not Started',
                startDate: new Date().toISOString().split('T')[0],
                endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                value: 10000
            };
            
            // Display project data
            projectData.className = 'status success';
            projectData.textContent = `Project "${name}" saved successfully with client "${selectedClient.name}"`;
            projectJson.textContent = JSON.stringify(project, null, 2);
            
            // Clear form
            projectName.value = '';
            projectDescription.value = '';
        }
        
        // Test mock API
        function testMockApi() {
            apiStatus.className = 'status info';
            apiStatus.textContent = 'Testing mock API...';
            
            // Simulate API delay
            setTimeout(() => {
                // Create mock API response
                const response = {
                    status: 'success',
                    timestamp: new Date().toISOString(),
                    data: {
                        clients: mockClients,
                        totalClients: mockClients.length,
                        message: 'Mock API is working correctly'
                    }
                };
                
                // Display API response
                apiStatus.className = 'status success';
                apiStatus.textContent = 'Mock API test successful!';
                apiResponse.textContent = JSON.stringify(response, null, 2);
            }, 500);
        }
        
        // Event listeners
        saveBtn.addEventListener('click', saveProject);
        testApiBtn.addEventListener('click', testMockApi);
        
        // Initialize the page
        document.addEventListener('DOMContentLoaded', () => {
            populateClientDropdown();
            
            // Display initial message
            apiStatus.textContent = 'Click "Test Mock API" to simulate a backend request';
        });
    </script>
</body>
</html>
