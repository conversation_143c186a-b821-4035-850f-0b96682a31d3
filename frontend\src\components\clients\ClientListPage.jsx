import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  HStack,
  VStack,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useToast,
  Badge,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton
} from '@chakra-ui/react';
import { Link, useNavigate } from 'react-router-dom';
import { ChevronDownIcon, EditIcon, ViewIcon, DeleteIcon, AddIcon } from '@chakra-ui/icons';

const ClientListPage = () => {
  const navigate = useNavigate();
  const toast = useToast();

  const [clients, setClients] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch clients
  useEffect(() => {
    const fetchClients = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Create basic auth header
        const authHeader = 'Basic ' + btoa('admin:admin123');

        // Try multiple endpoints in sequence
        const endpoints = [
          '/api/clients',
          'http://*************:8091/api/clients',
          'http://*************:8091/api/clients/public'
        ];

        let fetchSuccess = false;

        for (const endpoint of endpoints) {
          try {
            console.log(`Trying to fetch clients from ${endpoint}`);

            // Make the API call to get all clients
            const response = await fetch(endpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': authHeader
              }
              // Removed credentials: 'include' as it can cause issues with CORS
            });

            if (!response.ok) {
              if (response.status === 204) {
                // No content
                setClients([]);
                setIsLoading(false);
                fetchSuccess = true;
                break;
              }

              console.error(`Endpoint ${endpoint} returned status ${response.status}`);
              continue;
            }

            const data = await response.json();
            console.log(`Clients fetched successfully from ${endpoint}:`, data);

            setClients(Array.isArray(data) ? data : []);
            fetchSuccess = true;
            break;
          } catch (endpointError) {
            console.error(`Error fetching from ${endpoint}:`, endpointError);
          }
        }

        if (!fetchSuccess) {
          throw new Error('Failed to fetch clients from any endpoint');
        }
      } catch (err) {
        console.error('Error fetching clients:', err);
        setError(err.message || 'An unexpected error occurred');

        toast({
          title: 'Error fetching clients',
          description: err.message || 'An unexpected error occurred',
          status: 'error',
          duration: 5000,
          isClosable: true
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchClients();
  }, [toast]);

  const handleCreateClient = () => {
    navigate('/clients/create');
  };

  const handleEditClient = (id) => {
    navigate(`/clients/edit/${id}`);
  };

  const handleViewClient = (id) => {
    navigate(`/clients/view/${id}`);
  };

  const handleDeleteClient = async (id, name) => {
    if (!window.confirm(`Are you sure you want to delete client "${name}"?`)) {
      return;
    }

    try {
      // Create basic auth header
      const authHeader = 'Basic ' + btoa('admin:admin123');

      // Try multiple endpoints in sequence
      const endpoints = [
        `/api/clients/${id}`,
        `http://*************:8091/api/clients/${id}`
      ];

      let fetchSuccess = false;

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying to delete client from ${endpoint}`);

          // Make the API call to delete the client
          const response = await fetch(endpoint, {
            method: 'DELETE',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            }
            // Removed credentials: 'include' as it can cause issues with CORS
          });

          if (!response.ok) {
            console.error(`Endpoint ${endpoint} returned status ${response.status}`);
            continue;
          }

          // Remove the client from the list
          setClients(clients.filter(client => client.id !== id));

          // Show success toast
          toast({
            title: 'Client deleted successfully',
            description: `Client "${name}" has been deleted`,
            status: 'success',
            duration: 3060,
            isClosable: true
          });

          fetchSuccess = true;
          break;
        } catch (endpointError) {
          console.error(`Error deleting from ${endpoint}:`, endpointError);
        }
      }

      if (!fetchSuccess) {
        throw new Error('Failed to delete client from any endpoint');
      }
    } catch (err) {
      console.error('Error deleting client:', err);

      toast({
        title: 'Error deleting client',
        description: err.message || 'An unexpected error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    }
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <HStack justify="space-between">
          <Box>
            <Heading size="lg">Clients</Heading>
            <Text mt={2} color="gray.600">
              Manage your clients and their information
            </Text>
          </Box>
          <Button
            leftIcon={<AddIcon />}
            colorScheme="blue"
            onClick={handleCreateClient}
          >
            Add Client
          </Button>
        </HStack>

        {error && (
          <Alert status="error">
            <AlertIcon />
            <AlertTitle mr={2}>Error!</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          <Box textAlign="center" py={10}>
            <Spinner size="xl" />
            <Text mt={4}>Loading clients...</Text>
          </Box>
        ) : clients.length === 0 ? (
          <Box textAlign="center" py={10} borderWidth="1px" borderRadius="lg">
            <Text fontSize="lg" mb={4}>No clients found</Text>
            <Button
              leftIcon={<AddIcon />}
              colorScheme="blue"
              onClick={handleCreateClient}
            >
              Add Your First Client
            </Button>
          </Box>
        ) : (
          <Box overflowX="auto">
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>ID</Th>
                  <Th>Name</Th>
                  <Th>Contact Person</Th>
                  <Th>Email</Th>
                  <Th>Phone</Th>
                  <Th>BDM</Th>
                  <Th>Actions</Th>
                </Tr>
              </Thead>
              <Tbody>
                {clients.map(client => (
                  <Tr key={client.id}>
                    <Td>{client.id}</Td>
                    <Td fontWeight="medium">{client.name}</Td>
                    <Td>{client.contactPerson}</Td>
                    <Td>{client.email}</Td>
                    <Td>{client.phone}</Td>
                    <Td>
                      {client.bdmName ? (
                        <Badge colorScheme="green" variant="subtle" px={2} py={1}>
                          {client.bdmName}
                        </Badge>
                      ) : (
                        <Badge colorScheme="red" variant="subtle" px={2} py={1}>
                          No BDM
                        </Badge>
                      )}
                    </Td>
                    <Td>
                      <Menu>
                        <MenuButton
                          as={IconButton}
                          icon={<ChevronDownIcon />}
                          variant="outline"
                          size="sm"
                        />
                        <MenuList>
                          <MenuItem
                            icon={<ViewIcon />}
                            onClick={() => handleViewClient(client.id)}
                          >
                            View Details
                          </MenuItem>
                          <MenuItem
                            icon={<EditIcon />}
                            onClick={() => handleEditClient(client.id)}
                          >
                            Edit
                          </MenuItem>
                          <MenuItem
                            icon={<DeleteIcon />}
                            color="red.500"
                            onClick={() => handleDeleteClient(client.id, client.name)}
                          >
                            Delete
                          </MenuItem>
                        </MenuList>
                      </Menu>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </Box>
        )}
      </VStack>
    </Container>
  );
};

export default ClientListPage;
