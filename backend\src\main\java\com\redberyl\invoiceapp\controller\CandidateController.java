package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.CandidateDto;
import com.redberyl.invoiceapp.entity.Candidate;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.CandidateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Tag(name = "Candidate", description = "Candidate management API")
public class CandidateController {

    @Autowired
    private CandidateService candidateService;

    @GetMapping("/candidates/getAll")
    @Operation(summary = "Get all candidates", description = "Get all candidates")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Candidates found"),
            @ApiResponse(responseCode = "204", description = "No candidates found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<CandidateDto>> getAllCandidates() {
        try {
            List<CandidateDto> candidates = candidateService.getAllCandidates();
            return new ResponseEntity<>(candidates, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping({"/api/candidates", "/api/candidates/getAll"})
    @Operation(summary = "Get all candidates (no auth)", description = "Get all candidates without authentication for debugging")
    public ResponseEntity<List<CandidateDto>> getAllCandidatesNoAuth() {
        try {
            List<CandidateDto> candidates = candidateService.getAllCandidates();
            System.out.println("Returning " + candidates.size() + " candidates via no-auth endpoint");
            for (CandidateDto candidate : candidates) {
                System.out.println("Candidate: " + candidate.getName() + " (ID: " + candidate.getId() + ", Billing Rate: " + candidate.getBillingRate() + ")");
            }
            return new ResponseEntity<>(candidates, HttpStatus.OK);
        } catch (NoContentException e) {
            System.out.println("No candidates found in database");
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/candidates/getById/{id}")
    @Operation(summary = "Get candidate by ID", description = "Get candidate by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Candidate found"),
            @ApiResponse(responseCode = "404", description = "Candidate not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<CandidateDto> getCandidateById(@PathVariable Long id) {
        CandidateDto candidate = candidateService.getCandidateById(id);
        return new ResponseEntity<>(candidate, HttpStatus.OK);
    }

    @GetMapping("/candidates/getByClientId/{clientId}")
    @Operation(summary = "Get candidates by client ID", description = "Get candidates by client ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Candidates found"),
            @ApiResponse(responseCode = "204", description = "No candidates found for this client"),
            @ApiResponse(responseCode = "404", description = "Client not found"),
            @ApiResponse(responseCode = "400", description = "Invalid client ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<CandidateDto>> getCandidatesByClientId(@PathVariable Long clientId) {
        try {
            List<CandidateDto> candidates = candidateService.getCandidatesByClientId(clientId);
            return new ResponseEntity<>(candidates, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/candidates/getByProjectId/{projectId}")
    @Operation(summary = "Get candidates by project ID", description = "Get candidates by project ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Candidates found"),
            @ApiResponse(responseCode = "204", description = "No candidates found for this project"),
            @ApiResponse(responseCode = "404", description = "Project not found"),
            @ApiResponse(responseCode = "400", description = "Invalid project ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<CandidateDto>> getCandidatesByProjectId(@PathVariable Long projectId) {
        try {
            List<CandidateDto> candidates = candidateService.getCandidatesByProjectId(projectId);
            return new ResponseEntity<>(candidates, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/candidates/create")
    @Operation(summary = "Create candidate", description = "Create candidate")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Candidate created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<CandidateDto> createCandidate(@Valid @RequestBody CandidateDto candidateDto) {
        CandidateDto createdCandidate = candidateService.createCandidate(candidateDto);
        return new ResponseEntity<>(createdCandidate, HttpStatus.CREATED);
    }

    @PutMapping("/candidates/update/{id}")
    @Operation(summary = "Update candidate", description = "Update candidate")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Candidate updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "404", description = "Candidate not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<CandidateDto> updateCandidate(@PathVariable Long id,
            @Valid @RequestBody CandidateDto candidateDto) {
        CandidateDto updatedCandidate = candidateService.updateCandidate(id, candidateDto);
        return new ResponseEntity<>(updatedCandidate, HttpStatus.OK);
    }

    @DeleteMapping("/candidates/deleteById/{id}")
    @Operation(summary = "Delete candidate", description = "Delete candidate")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Candidate deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied or candidate is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "Candidate not found")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteCandidate(@PathVariable Long id) {
        candidateService.deleteCandidate(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
