package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class CommunicationDto extends BaseDto {
    private Long id;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long clientId;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long leadId;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long dealId;

    // Include the full client object
    private ClientDto client;

    // Include the full lead object
    private LeadDto lead;

    // Deal relationship removed as requested - no DealDto field

    @NotBlank(message = "Subject is required")
    private String subject;

    private String body;
    private String type;
    private String status;
    private LocalDate communicationDate;
    private String communicatedBy;
    private String communicatedTo;

    // Add missing fields
    private String method;
    private String content;
    private LocalDate followUpDate;
    private LocalDate loggedAt;
}
