# RedBeryl Logo Setup Guide for Invoice PDF Generation

## 🎯 Quick Setup

To use your actual RedBeryl logo in invoice PDFs, follow these simple steps:

### Step 1: Add Your Logo Image
1. Save your RedBeryl logo image as: `public/assets/redberyl-logo.png`
2. Recommended specifications:
   - **Format**: PNG (with transparent background) or JPG
   - **Size**: 600x240 pixels (or similar 2.5:1 aspect ratio)
   - **Quality**: High resolution for crisp PDF output

### Step 2: Update the Logo Component (Optional)
If you want to use the actual image instead of the SVG version:

```typescript
// In your invoice template or demo component
<RedBerylLogo 
  width={250} 
  height={80} 
  useImage={true}  // This will use the actual image file
  imageUrl="/assets/redberyl-logo.png"
/>
```

### Step 3: Test the PDF Generation
1. Start your development server: `npm run dev`
2. Navigate to: `http://localhost:3060/invoice-pdf-demo`
3. Click "Download PDF" to test the logo integration

## 🔧 Current Implementation

### What's Already Working:
- ✅ SVG version of RedBeryl logo (based on your provided design)
- ✅ Professional invoice layout matching your template
- ✅ Automatic logo scaling and positioning
- ✅ PDF generation with embedded logo
- ✅ Fallback system if image fails to load

### Logo Features:
- **Responsive sizing**: Automatically scales for different contexts
- **PDF-friendly**: Embedded as base64 for reliable PDF generation
- **Fallback support**: Uses SVG version if image file is missing
- **High quality**: Maintains crisp appearance in printed PDFs

## 📁 File Structure

```
Invoiceapp/frontend/
├── public/
│   └── assets/
│       ├── redberyl-logo.png          ← Place your logo here
│       └── redberyl-logo-base64.js    ← Base64 encoded version
├── src/
│   ├── components/
│   │   ├── ui/
│   │   │   └── RedBerylLogo.tsx       ← Logo component
│   │   └── invoices/
│   │       ├── InvoicePdfTemplate.tsx ← PDF template with logo
│   │       └── InvoicePdfDemo.tsx     ← Demo page
│   └── utils/
│       └── logoUtils.ts               ← Logo utilities
```

## 🎨 Logo Customization

### To modify logo appearance in PDFs:
1. Edit `src/components/invoices/InvoicePdfTemplate.tsx`
2. Adjust the logo size in the header section:

```typescript
<RedBerylLogo 
  width={300}    // Adjust width
  height={100}   // Adjust height
  useImage={true}
/>
```

### To update company colors:
1. Edit `src/components/ui/RedBerylLogo.tsx`
2. Modify the color values in the SVG:
   - Blue: `#1565C0`
   - Pink/Red: `#E91E63`
   - Purple: `#8E44AD`

## 🚀 Testing Your Logo

### Demo Page Features:
- **Live Preview**: See how your logo appears in the invoice
- **PDF Download**: Generate actual PDF with your logo
- **Sample Data**: Test with realistic invoice information
- **Error Handling**: Fallback if logo image is missing

### Access the Demo:
```
http://localhost:3060/invoice-pdf-demo
```

## 🔍 Troubleshooting

### Logo Not Appearing?
1. **Check file path**: Ensure logo is at `public/assets/redberyl-logo.png`
2. **Check file format**: Use PNG, JPG, or SVG
3. **Check browser console**: Look for loading errors
4. **Try fallback**: The SVG version should always work

### PDF Quality Issues?
1. **Use high resolution**: Minimum 600x240 pixels
2. **Avoid compression**: Use PNG for best quality
3. **Test print**: Generate PDF and check print preview

### Need Different Size?
1. **Maintain aspect ratio**: Keep 2.5:1 ratio (width:height)
2. **Update component props**: Adjust width/height in template
3. **Test responsiveness**: Check on different screen sizes

## 📞 Support

If you encounter any issues:
1. Check the browser console for error messages
2. Verify file paths and permissions
3. Test with the SVG fallback version
4. Review the demo page for working examples

The logo system is designed to be flexible and reliable, with automatic fallbacks to ensure your invoices always generate successfully.
