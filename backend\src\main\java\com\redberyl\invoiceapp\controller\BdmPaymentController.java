package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.BdmPaymentDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.BdmPaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Tag(name = "BDM Payment", description = "BDM Payment management API")
public class BdmPaymentController {

    @Autowired
    private BdmPaymentService bdmPaymentService;

    @GetMapping("/bdm-payments/getAll")
    @Operation(summary = "Get all BDM payments", description = "Get all BDM payments")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "BDM payments found"),
            @ApiResponse(responseCode = "204", description = "No BDM payments found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<BdmPaymentDto>> getAllBdmPayments() {
        try {
            List<BdmPaymentDto> bdmPayments = bdmPaymentService.getAllBdmPayments();
            return new ResponseEntity<>(bdmPayments, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/bdm-payments/getById/{id}")
    @Operation(summary = "Get BDM payment by ID", description = "Get BDM payment by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "BDM payment found"),
            @ApiResponse(responseCode = "404", description = "BDM payment not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<BdmPaymentDto> getBdmPaymentById(@PathVariable Long id) {
        BdmPaymentDto bdmPayment = bdmPaymentService.getBdmPaymentById(id);
        return new ResponseEntity<>(bdmPayment, HttpStatus.OK);
    }

    @GetMapping("/bdm-payments/getByBdmId/{bdmId}")
    @Operation(summary = "Get BDM payments by BDM ID", description = "Get BDM payments by BDM ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "BDM payments found"),
            @ApiResponse(responseCode = "204", description = "No BDM payments found for this BDM"),
            @ApiResponse(responseCode = "404", description = "BDM not found"),
            @ApiResponse(responseCode = "400", description = "Invalid BDM ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<BdmPaymentDto>> getBdmPaymentsByBdmId(@PathVariable Long bdmId) {
        try {
            List<BdmPaymentDto> bdmPayments = bdmPaymentService.getBdmPaymentsByBdmId(bdmId);
            return new ResponseEntity<>(bdmPayments, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/bdm-payments/getByInvoiceId/{invoiceId}")
    @Operation(summary = "Get BDM payments by invoice ID", description = "Get BDM payments by invoice ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "BDM payments found"),
            @ApiResponse(responseCode = "204", description = "No BDM payments found for this invoice"),
            @ApiResponse(responseCode = "404", description = "Invoice not found"),
            @ApiResponse(responseCode = "400", description = "Invalid invoice ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<BdmPaymentDto>> getBdmPaymentsByInvoiceId(@PathVariable Long invoiceId) {
        try {
            List<BdmPaymentDto> bdmPayments = bdmPaymentService.getBdmPaymentsByInvoiceId(invoiceId);
            return new ResponseEntity<>(bdmPayments, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/bdm-payments/create")
    @Operation(summary = "Create BDM payment", description = "Create BDM payment")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "BDM payment created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<BdmPaymentDto> createBdmPayment(@Valid @RequestBody BdmPaymentDto bdmPaymentDto) {
        BdmPaymentDto createdBdmPayment = bdmPaymentService.createBdmPayment(bdmPaymentDto);
        return new ResponseEntity<>(createdBdmPayment, HttpStatus.CREATED);
    }

    @PutMapping("/bdm-payments/update/{id}")
    @Operation(summary = "Update BDM payment", description = "Update BDM payment")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "BDM payment updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "404", description = "BDM payment not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<BdmPaymentDto> updateBdmPayment(@PathVariable Long id,
            @Valid @RequestBody BdmPaymentDto bdmPaymentDto) {
        BdmPaymentDto updatedBdmPayment = bdmPaymentService.updateBdmPayment(id, bdmPaymentDto);
        return new ResponseEntity<>(updatedBdmPayment, HttpStatus.OK);
    }

    @DeleteMapping("/bdm-payments/deleteById/{id}")
    @Operation(summary = "Delete BDM payment", description = "Delete BDM payment")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "BDM payment deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied or BDM payment is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "BDM payment not found")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteBdmPayment(@PathVariable Long id) {
        bdmPaymentService.deleteBdmPayment(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
