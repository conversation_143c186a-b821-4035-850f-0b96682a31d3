import React, { useState, useEffect } from "react";
import { z } from "zod";
import MasterPageTemplate, { MasterItem } from "@/components/masters/MasterPageTemplate";
import { toast } from "sonner";
import { invoiceTypeService, InvoiceType } from "@/services/invoiceTypeService";

// Define the schema for invoice type form
const invoiceTypeSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
});

// Fallback mock data in case API fails
const fallbackInvoiceTypes: MasterItem[] = [
  {
    id: "1",
    name: "Standard",
    description: "Regular invoice for services or products",
  },
  {
    id: "2",
    name: "<PERSON>orm<PERSON>",
    description: "Preliminary bill of sale sent to buyers in advance of a shipment or delivery",
  },
  {
    id: "3",
    name: "Credit Note",
    description: "Document issued to indicate a return of funds",
  },
  {
    id: "4",
    name: "Debit Note",
    description: "Document issued to request additional payment",
  },
];

const InvoiceTypes = () => {
  const [invoiceTypes, setInvoiceTypes] = useState<MasterItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState<number>(0);

  // Fetch invoice types from API
  useEffect(() => {
    const fetchInvoiceTypes = async () => {
      setIsLoading(true);
      setError(null);

      try {
        console.log("InvoiceTypes: Fetching invoice types from API...");

        // Try multiple direct fetch endpoints for debugging
        const endpoints = [
          'http://*************:8091/invoice-types/getAll',
          'http://*************:8091/api/invoice-types',
          'http://*************:8091/api/noauth/invoice-types',
          'http://*************:8091/api/noauth/invoice-types/test'
        ];

        let directData = null;

        // First try with auth headers
        for (const endpoint of endpoints) {
          try {
            console.log(`InvoiceTypes: Trying direct fetch to ${endpoint} with auth...`);
            const authHeader = 'Basic ' + btoa('admin:admin123');
            const directResponse = await fetch(endpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': authHeader
              }
            });

            console.log(`InvoiceTypes: Direct fetch response status for ${endpoint}:`, directResponse.status);
            if (directResponse.ok) {
              directData = await directResponse.json();
              console.log(`InvoiceTypes: Direct fetch successful for ${endpoint}:`, directData);
              break; // Exit the loop if successful
            } else {
              console.warn(`InvoiceTypes: Direct fetch failed for ${endpoint} with status:`, directResponse.status);
            }
          } catch (directError) {
            console.error(`InvoiceTypes: Direct fetch error for ${endpoint}:`, directError);
          }
        }

        // If auth didn't work, try without auth for the no-auth endpoint
        if (!directData) {
          try {
            const noAuthEndpoint = 'http://*************:8091/api/noauth/invoice-types';
            console.log(`InvoiceTypes: Trying direct fetch to ${noAuthEndpoint} without auth...`);
            const noAuthResponse = await fetch(noAuthEndpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
              }
            });

            console.log(`InvoiceTypes: No-auth fetch response status:`, noAuthResponse.status);
            if (noAuthResponse.ok) {
              directData = await noAuthResponse.json();
              console.log(`InvoiceTypes: No-auth fetch successful:`, directData);
            } else {
              console.warn(`InvoiceTypes: No-auth fetch failed with status:`, noAuthResponse.status);
            }
          } catch (noAuthError) {
            console.error(`InvoiceTypes: No-auth fetch error:`, noAuthError);
          }
        }

        // If still no data, try the test endpoint
        if (!directData) {
          try {
            const testEndpoint = 'http://*************:8091/api/noauth/invoice-types/test';
            console.log(`InvoiceTypes: Trying test endpoint ${testEndpoint}...`);
            const testResponse = await fetch(testEndpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
              }
            });

            console.log(`InvoiceTypes: Test endpoint response status:`, testResponse.status);
            if (testResponse.ok) {
              directData = await testResponse.json();
              console.log(`InvoiceTypes: Test endpoint successful:`, directData);
            } else {
              console.warn(`InvoiceTypes: Test endpoint failed with status:`, testResponse.status);
            }
          } catch (testError) {
            console.error(`InvoiceTypes: Test endpoint error:`, testError);
          }
        }

        // Use direct data if available, otherwise try the service
        const data = directData || await invoiceTypeService.getAllInvoiceTypes();
        console.log("InvoiceTypes: Service returned data:", data);

        // Transform API data to match MasterItem format
        const transformedData = data.map((item: any) => ({
          id: item.id?.toString() || "",
          name: item.invoiceType || item.name || "",
          description: item.typeDesc || item.description || "",
        }));

        setInvoiceTypes(transformedData);
        console.log("InvoiceTypes: Successfully transformed data:", transformedData);
      } catch (error) {
        console.error("Error fetching invoice types:", error);
        setError(error as Error);
        toast.error("Failed to load invoice types. Using fallback data.");

        // Use fallback data if API fails
        setInvoiceTypes(fallbackInvoiceTypes);
      } finally {
        setIsLoading(false);
      }
    };

    fetchInvoiceTypes();
  }, [refreshTrigger]);

  // Handle adding a new invoice type
  const handleAddInvoiceType = async (data: z.infer<typeof invoiceTypeSchema>) => {
    try {
      // Create payload with both field naming conventions to handle backend differences
      const newInvoiceType = {
        name: data.name,
        description: data.description,
        invoiceType: data.name,
        typeDesc: data.description
      };

      console.log("InvoiceTypes: Creating new invoice type:", newInvoiceType);

      // Try direct POST first
      try {
        const endpoint = 'http://*************:8091/invoice-types/create';
        console.log(`InvoiceTypes: Trying direct POST to ${endpoint}...`);
        const authHeader = 'Basic ' + btoa('admin:admin123');
        const directResponse = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          body: JSON.stringify(newInvoiceType)
        });

        console.log(`InvoiceTypes: Direct POST response status:`, directResponse.status);
        if (directResponse.ok) {
          const responseData = await directResponse.json();
          console.log(`InvoiceTypes: Direct POST successful:`, responseData);
          toast.success("Invoice type created successfully");
          setRefreshTrigger(prev => prev + 1);
          return;
        } else {
          console.warn(`InvoiceTypes: Direct POST failed with status:`, directResponse.status);
        }
      } catch (directError) {
        console.error(`InvoiceTypes: Direct POST error:`, directError);
      }

      // If direct POST failed, try using the service
      console.log("InvoiceTypes: Trying to create invoice type via service...");
      const createdInvoiceType = await invoiceTypeService.createInvoiceType(newInvoiceType as InvoiceType);
      console.log("InvoiceTypes: Service created invoice type:", createdInvoiceType);

      toast.success("Invoice type created successfully");

      // Refresh the list
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error("InvoiceTypes: Error creating invoice type:", error);
      toast.error("Failed to create invoice type");
    }
  };

  // Handle editing an invoice type
  const handleEditInvoiceType = async (id: string, data: z.infer<typeof invoiceTypeSchema>) => {
    try {
      // Create payload with both field naming conventions to handle backend differences
      const updatedInvoiceType = {
        id,
        name: data.name,
        description: data.description,
        invoiceType: data.name,
        typeDesc: data.description
      };

      console.log("InvoiceTypes: Updating invoice type:", updatedInvoiceType);

      // Try direct PUT first
      try {
        const endpoint = `http://*************:8091/invoice-types/update/${id}`;
        console.log(`InvoiceTypes: Trying direct PUT to ${endpoint}...`);
        const authHeader = 'Basic ' + btoa('admin:admin123');
        const directResponse = await fetch(endpoint, {
          method: 'PUT',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          body: JSON.stringify(updatedInvoiceType)
        });

        console.log(`InvoiceTypes: Direct PUT response status:`, directResponse.status);
        if (directResponse.ok) {
          const responseData = await directResponse.json();
          console.log(`InvoiceTypes: Direct PUT successful:`, responseData);
          toast.success("Invoice type updated successfully");
          setRefreshTrigger(prev => prev + 1);
          return;
        } else {
          console.warn(`InvoiceTypes: Direct PUT failed with status:`, directResponse.status);
        }
      } catch (directError) {
        console.error(`InvoiceTypes: Direct PUT error:`, directError);
      }

      // If direct PUT failed, try using the service
      console.log("InvoiceTypes: Trying to update invoice type via service...");
      await invoiceTypeService.updateInvoiceType(id, updatedInvoiceType as InvoiceType);
      console.log("InvoiceTypes: Service updated invoice type");

      toast.success("Invoice type updated successfully");

      // Refresh the list
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error("InvoiceTypes: Error updating invoice type:", error);
      toast.error("Failed to update invoice type");
    }
  };

  // Handle deleting an invoice type
  const handleDeleteInvoiceType = async (id: string) => {
    try {
      console.log("InvoiceTypes: Deleting invoice type with ID:", id);

      // Try direct DELETE first
      try {
        const endpoint = `http://*************:8091/invoice-types/deleteById/${id}`;
        console.log(`InvoiceTypes: Trying direct DELETE to ${endpoint}...`);
        const authHeader = 'Basic ' + btoa('admin:admin123');
        const directResponse = await fetch(endpoint, {
          method: 'DELETE',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': authHeader
          }
        });

        console.log(`InvoiceTypes: Direct DELETE response status:`, directResponse.status);
        if (directResponse.ok) {
          console.log(`InvoiceTypes: Direct DELETE successful`);
          toast.success("Invoice type deleted successfully");
          setRefreshTrigger(prev => prev + 1);
          return;
        } else {
          console.warn(`InvoiceTypes: Direct DELETE failed with status:`, directResponse.status);
        }
      } catch (directError) {
        console.error(`InvoiceTypes: Direct DELETE error:`, directError);
      }

      // If direct DELETE failed, try using the service
      console.log("InvoiceTypes: Trying to delete invoice type via service...");
      await invoiceTypeService.deleteInvoiceType(id);
      console.log("InvoiceTypes: Service deleted invoice type");

      toast.success("Invoice type deleted successfully");

      // Refresh the list
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error("InvoiceTypes: Error deleting invoice type:", error);
      toast.error("Failed to delete invoice type");
    }
  };

  return (
    <MasterPageTemplate
      title="Invoice Types"
      description="Manage invoice types for billing and accounting"
      items={invoiceTypes}
      formSchema={invoiceTypeSchema}
      onAdd={handleAddInvoiceType}
      onEdit={handleEditInvoiceType}
      onDelete={handleDeleteInvoiceType}
      isLoading={isLoading}
      error={error ? error.message : undefined}
    />
  );
};

export default InvoiceTypes;
