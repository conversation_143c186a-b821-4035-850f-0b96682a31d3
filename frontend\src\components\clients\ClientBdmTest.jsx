import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  FormControl,
  FormLabel,
  Input,
  Select,
  Textarea,
  Divider,
  Code,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Spinner,
  useToast
} from '@chakra-ui/react';
import BdmDropdown from '../BdmDropdown';

const ClientBdmTest = () => {
  const toast = useToast();
  
  const [bdms, setBdms] = useState([]);
  const [selectedBdm, setSelectedBdm] = useState(null);
  const [bdmLoading, setBdmLoading] = useState(true);
  const [bdmError, setBdmError] = useState(null);
  
  const [clientData, setClientData] = useState({
    name: 'ABC Technologies Pvt Ltd',
    email: '<EMAIL>',
    phone: '+91-9876543210',
    contactPerson: '<PERSON><PERSON>',
    website: 'https://www.abctech.com',
    bdmId: null,
    commissionPercentage: '5',
    billingAddress: '123, Tech Park, Sector 45, Bangalore, Karnataka, India - 560045',
    shippingAddress: '456, Industrial Area, Sector 67, Noida, Uttar Pradesh, India - 201301',
    gstNumber: '29**********1Z5',
    panNumber: '**********',
    cinNumber: 'U12345KA2010PTC012345',
    notes: 'Preferred vendor for software development and consulting services.'
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [createdClient, setCreatedClient] = useState(null);
  const [error, setError] = useState(null);

  // Fetch BDMs
  useEffect(() => {
    const fetchBdms = async () => {
      try {
        setBdmLoading(true);
        setBdmError(null);
        
        // Create basic auth header if needed
        const authHeader = 'Basic ' + btoa('admin:admin123');
        
        const response = await fetch('http://*************:8091/api/v1/bdms', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          credentials: 'include'
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch BDMs: ${response.status}`);
        }
        
        const responseData = await response.json();
        console.log('BDM API response:', responseData);
        
        // Extract BDMs from the nested structure
        let bdmsData = [];
        
        if (responseData && responseData.success && responseData.data && responseData.data.content) {
          bdmsData = responseData.data.content;
        } else if (Array.isArray(responseData)) {
          bdmsData = responseData;
        } else if (responseData && responseData.data && Array.isArray(responseData.data)) {
          bdmsData = responseData.data;
        } else if (responseData && responseData.content && Array.isArray(responseData.content)) {
          bdmsData = responseData.content;
        } else {
          console.error('Unexpected BDM data format:', responseData);
          throw new Error('Unexpected data format from BDM API');
        }
        
        setBdms(bdmsData);
        
        // Set the first BDM as selected if available
        if (bdmsData.length > 0) {
          setSelectedBdm(bdmsData[0]);
          setClientData(prev => ({ ...prev, bdmId: bdmsData[0].id }));
        }
      } catch (err) {
        console.error('Error fetching BDMs:', err);
        setBdmError(err.message);
        
        toast({
          title: 'Error fetching BDMs',
          description: err.message,
          status: 'error',
          duration: 5000,
          isClosable: true
        });
      } finally {
        setBdmLoading(false);
      }
    };
    
    fetchBdms();
  }, [toast]);

  const handleBdmChange = (bdmId) => {
    console.log('BDM selected:', bdmId);
    
    // Find the selected BDM
    const bdm = bdms.find(b => b.id === bdmId);
    setSelectedBdm(bdm);
    
    // Update client data
    setClientData(prev => ({ ...prev, bdmId }));
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setClientData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setIsSubmitting(true);
      setError(null);
      
      // Validate BDM selection
      if (!clientData.bdmId) {
        throw new Error('Please select a BDM');
      }
      
      console.log('Creating client with data:', clientData);
      
      // Create basic auth header if needed
      const authHeader = 'Basic ' + btoa('admin:admin123');
      
      // Make the API call to create the client
      const response = await fetch('http://*************:8091/api/clients', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': authHeader
        },
        body: JSON.stringify(clientData),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to create client: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Client created successfully:', data);
      
      setCreatedClient(data);
      
      toast({
        title: 'Client created successfully',
        description: `Client "${data.name}" has been created with BDM "${data.bdmName}"`,
        status: 'success',
        duration: 5000,
        isClosable: true
      });
    } catch (err) {
      console.error('Error creating client:', err);
      setError(err.message);
      
      toast({
        title: 'Error creating client',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    setCreatedClient(null);
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box>
          <Heading size="lg">Client-BDM Relationship Test</Heading>
          <Text mt={2} color="gray.600">
            This page demonstrates how to create a client with a BDM relationship.
          </Text>
        </Box>
        
        {bdmError && (
          <Alert status="error">
            <AlertIcon />
            <AlertTitle mr={2}>BDM Error!</AlertTitle>
            <AlertDescription>{bdmError}</AlertDescription>
          </Alert>
        )}
        
        {error && (
          <Alert status="error">
            <AlertIcon />
            <AlertTitle mr={2}>Error!</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {createdClient ? (
          <Box>
            <Alert status="success" mb={4}>
              <AlertIcon />
              <AlertTitle mr={2}>Success!</AlertTitle>
              <AlertDescription>
                Client created successfully with BDM relationship.
              </AlertDescription>
            </Alert>
            
            <Box p={4} borderWidth="1px" borderRadius="md" bg="gray.50">
              <Heading size="sm" mb={2}>Client Details:</Heading>
              <VStack align="stretch" spacing={2}>
                <HStack>
                  <Text fontWeight="bold" width="150px">ID:</Text>
                  <Text>{createdClient.id}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold" width="150px">Name:</Text>
                  <Text>{createdClient.name}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold" width="150px">BDM ID:</Text>
                  <Text>{createdClient.bdmId}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold" width="150px">BDM Name:</Text>
                  <Text>{createdClient.bdmName}</Text>
                </HStack>
              </VStack>
              
              <Divider my={4} />
              
              <Heading size="sm" mb={2}>Full Response:</Heading>
              <Box overflowX="auto">
                <Code p={2} display="block" whiteSpace="pre">
                  {JSON.stringify(createdClient, null, 2)}
                </Code>
              </Box>
            </Box>
            
            <Button mt={4} colorScheme="blue" onClick={handleReset}>
              Create Another Client
            </Button>
          </Box>
        ) : (
          <Box p={6} borderWidth="1px" borderRadius="md" bg="white">
            <form onSubmit={handleSubmit}>
              <VStack spacing={6} align="stretch">
                <Box>
                  <Heading size="sm" mb={3}>BDM Selection</Heading>
                  {bdmLoading ? (
                    <HStack>
                      <Spinner size="sm" />
                      <Text>Loading BDMs...</Text>
                    </HStack>
                  ) : (
                    <BdmDropdown 
                      value={clientData.bdmId} 
                      onChange={handleBdmChange}
                      required={true}
                    />
                  )}
                  
                  {selectedBdm && (
                    <Box mt={4} p={3} bg="blue.50" borderRadius="md">
                      <Heading size="xs" mb={2}>Selected BDM Details:</Heading>
                      <VStack align="stretch" spacing={1}>
                        <HStack>
                          <Text fontWeight="bold" width="100px">ID:</Text>
                          <Text>{selectedBdm.id}</Text>
                        </HStack>
                        <HStack>
                          <Text fontWeight="bold" width="100px">Name:</Text>
                          <Text>{selectedBdm.name}</Text>
                        </HStack>
                        <HStack>
                          <Text fontWeight="bold" width="100px">Email:</Text>
                          <Text>{selectedBdm.email || 'N/A'}</Text>
                        </HStack>
                      </VStack>
                    </Box>
                  )}
                </Box>
                
                <Divider />
                
                <Box>
                  <Heading size="sm" mb={3}>Client Information</Heading>
                  <VStack spacing={4} align="stretch">
                    <FormControl isRequired>
                      <FormLabel>Company Name</FormLabel>
                      <Input 
                        name="name" 
                        value={clientData.name} 
                        onChange={handleInputChange} 
                      />
                    </FormControl>
                    
                    <HStack>
                      <FormControl isRequired>
                        <FormLabel>Email</FormLabel>
                        <Input 
                          name="email" 
                          value={clientData.email} 
                          onChange={handleInputChange} 
                        />
                      </FormControl>
                      
                      <FormControl isRequired>
                        <FormLabel>Phone</FormLabel>
                        <Input 
                          name="phone" 
                          value={clientData.phone} 
                          onChange={handleInputChange} 
                        />
                      </FormControl>
                    </HStack>
                    
                    <FormControl isRequired>
                      <FormLabel>Contact Person</FormLabel>
                      <Input 
                        name="contactPerson" 
                        value={clientData.contactPerson} 
                        onChange={handleInputChange} 
                      />
                    </FormControl>
                    
                    <FormControl>
                      <FormLabel>Commission Percentage</FormLabel>
                      <Input 
                        name="commissionPercentage" 
                        value={clientData.commissionPercentage} 
                        onChange={handleInputChange} 
                      />
                    </FormControl>
                  </VStack>
                </Box>
                
                <Button 
                  type="submit" 
                  colorScheme="blue" 
                  isLoading={isSubmitting}
                  loadingText="Creating Client"
                  isDisabled={!clientData.bdmId}
                >
                  Create Client with BDM Relationship
                </Button>
              </VStack>
            </form>
          </Box>
        )}
      </VStack>
    </Container>
  );
};

export default ClientBdmTest;
