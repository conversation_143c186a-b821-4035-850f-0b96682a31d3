package com.redberyl.invoiceapp.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class HtmlController {

    @GetMapping("/html/getTaxRateForm")
    public String taxRateForm() {
        return "tax-rate-form.html";
    }

    @GetMapping("/html/getFixedTaxRateForm")
    public String fixedTaxRateForm() {
        return "fixed-tax-rate-form.html";
    }

    @GetMapping("/html/getTaxRateWithAudit")
    public String taxRateWithAudit() {
        return "tax-rate-with-audit.html";
    }
}
