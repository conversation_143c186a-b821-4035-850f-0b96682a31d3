package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.InvoiceDto;
import com.redberyl.invoiceapp.service.InvoiceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/invoices")
@Tag(name = "Invoice", description = "Invoice management API")
public class InvoiceController {

    @Autowired
    private InvoiceService invoiceService;

    @GetMapping
    @Operation(summary = "Get all invoices", description = "Retrieve a list of all invoices")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceDto>> getAllInvoices() {
        List<InvoiceDto> invoices = invoiceService.getAllInvoices();
        return new ResponseEntity<>(invoices, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get invoice by ID", description = "Retrieve an invoice by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceDto> getInvoiceById(@PathVariable Long id) {
        InvoiceDto invoice = invoiceService.getInvoiceById(id);
        return new ResponseEntity<>(invoice, HttpStatus.OK);
    }

    @GetMapping("/number/{invoiceNumber}")
    @Operation(summary = "Get invoice by number", description = "Retrieve an invoice by its number")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceDto> getInvoiceByNumber(@PathVariable String invoiceNumber) {
        InvoiceDto invoice = invoiceService.getInvoiceByNumber(invoiceNumber);
        return new ResponseEntity<>(invoice, HttpStatus.OK);
    }

    @GetMapping("/client/{clientId}")
    @Operation(summary = "Get invoices by client ID", description = "Retrieve all invoices for a specific client")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceDto>> getInvoicesByClientId(@PathVariable Long clientId) {
        List<InvoiceDto> invoices = invoiceService.getInvoicesByClientId(clientId);
        return new ResponseEntity<>(invoices, HttpStatus.OK);
    }

    @GetMapping("/project/{projectId}")
    @Operation(summary = "Get invoices by project ID", description = "Retrieve all invoices for a specific project")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceDto>> getInvoicesByProjectId(@PathVariable Long projectId) {
        List<InvoiceDto> invoices = invoiceService.getInvoicesByProjectId(projectId);
        return new ResponseEntity<>(invoices, HttpStatus.OK);
    }

    @GetMapping("/candidate/{candidateId}")
    @Operation(summary = "Get invoices by candidate ID", description = "Retrieve all invoices for a specific candidate")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceDto>> getInvoicesByCandidateId(@PathVariable Long candidateId) {
        List<InvoiceDto> invoices = invoiceService.getInvoicesByCandidateId(candidateId);
        return new ResponseEntity<>(invoices, HttpStatus.OK);
    }

    @GetMapping("/date-range")
    @Operation(summary = "Get invoices by date range", description = "Retrieve all invoices within a specific date range")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceDto>> getInvoicesByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        List<InvoiceDto> invoices = invoiceService.getInvoicesByDateRange(startDate, endDate);
        return new ResponseEntity<>(invoices, HttpStatus.OK);
    }

    @GetMapping("/overdue")
    @Operation(summary = "Get overdue invoices", description = "Retrieve all overdue invoices")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceDto>> getOverdueInvoices(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate asOfDate) {
        LocalDate date = asOfDate != null ? asOfDate : LocalDate.now();
        List<InvoiceDto> invoices = invoiceService.getOverdueInvoices(date);
        return new ResponseEntity<>(invoices, HttpStatus.OK);
    }

    @GetMapping("/recurring")
    @Operation(summary = "Get recurring invoices", description = "Retrieve all recurring invoices")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceDto>> getRecurringInvoices() {
        List<InvoiceDto> invoices = invoiceService.getRecurringInvoices(true);
        return new ResponseEntity<>(invoices, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create invoice", description = "Create a new invoice")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceDto> createInvoice(@Valid @RequestBody InvoiceDto invoiceDto) {
        InvoiceDto createdInvoice = invoiceService.createInvoice(invoiceDto);
        return new ResponseEntity<>(createdInvoice, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update invoice", description = "Update an existing invoice")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceDto> updateInvoice(@PathVariable Long id, @Valid @RequestBody InvoiceDto invoiceDto) {
        InvoiceDto updatedInvoice = invoiceService.updateInvoice(id, invoiceDto);
        return new ResponseEntity<>(updatedInvoice, HttpStatus.OK);
    }

    @PutMapping("/{id}/publish")
    @Operation(summary = "Publish invoice", description = "Publish an invoice to finance")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceDto> publishInvoice(@PathVariable Long id) {
        InvoiceDto publishedInvoice = invoiceService.publishInvoice(id);
        return new ResponseEntity<>(publishedInvoice, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete invoice", description = "Delete an invoice by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteInvoice(@PathVariable Long id) {
        invoiceService.deleteInvoice(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
