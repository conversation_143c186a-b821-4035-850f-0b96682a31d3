package com.redberyl.invoiceapp.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Test controller to verify Swagger path handling
 */
@RestController
@RequestMapping("/api/swagger-test")
@Tag(name = "Swagger Test", description = "Test endpoints for Swagger configuration")
public class SwaggerTestController {

    @GetMapping("/path-test")
    @Operation(
        summary = "Test endpoint for Swagger path handling",
        description = "This endpoint is used to verify that Swagger paths are correctly handled"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "Test successful",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        )
    })
    public ResponseEntity<Map<String, Object>> testSwaggerPath() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Swagger path test successful");
        response.put("status", "OK");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
}
