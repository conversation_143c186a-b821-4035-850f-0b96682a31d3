package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;

import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "spocs")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Spoc extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "email_id", nullable = false)
    private String emailId;

    @Column(name = "contact_no", nullable = false)
    private String contactNo;

    @OneToMany(mappedBy = "managerSpoc")
    @Builder.Default
    private Set<Project> managedProjects = new HashSet<>();

    @OneToMany(mappedBy = "accountHeadSpoc")
    @Builder.Default
    private Set<Project> accountHeadProjects = new HashSet<>();

    @OneToMany(mappedBy = "businessHeadSpoc")
    @Builder.Default
    private Set<Project> businessHeadProjects = new HashSet<>();

    @OneToMany(mappedBy = "hrSpoc")
    @Builder.Default
    private Set<Project> hrProjects = new HashSet<>();

    @OneToMany(mappedBy = "financeSpoc")
    @Builder.Default
    private Set<Project> financeProjects = new HashSet<>();
}
