
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import NotFound from "./pages/NotFound";
import MainLayout from "./components/layout/MainLayout";
import Dashboard from "./pages/Dashboard";
import Clients from "./pages/Clients";
import Candidates from "./pages/Candidates";
import Invoices from "./pages/Invoices";
import Payments from "./pages/Payments";
import CRM from "./pages/CRM";
import Documents from "./pages/Documents";
import BDMs from "./pages/BDMs";
import Profile from "./pages/Profile";
import Settings from "./pages/Settings";
import RoleMaster from "./pages/masters/RoleMaster";
import AdminMaster from "./pages/masters/AdminMaster";
import HsnCodeMaster from "./pages/masters/HsnCodeMaster";
import SpocMaster from "./pages/masters/SpocMaster";
import StaffingTypes from "./pages/masters/StaffingTypes";
import InvoiceTypes from "./pages/masters/InvoiceTypes";
import RedberylAccounts from "./pages/masters/RedberylAccounts";
import OneDriveTest from "./pages/OneDriveTest";

import NewLogin from "./pages/auth/NewLogin";
import NewSignup from "./pages/auth/NewSignup";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import { AuthProvider } from "./contexts/AuthContext";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AuthProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            {/* Auth Routes */}
            <Route path="/auth">
              <Route path="login" element={<NewLogin />} />
              <Route path="signup" element={<NewSignup />} />
              <Route path="" element={<Navigate to="/auth/login" replace />} />
            </Route>

            {/* Protected Routes */}
            <Route element={<ProtectedRoute />}>
              <Route path="/" element={<MainLayout />}>
                <Route index element={<Dashboard />} />
                <Route path="dashboard" element={<Dashboard />} />
                <Route path="clients" element={<Clients />} />
                <Route path="candidates" element={<Candidates />} />
                <Route path="invoices" element={<Invoices />} />

                <Route path="payments" element={<Payments />} />
                <Route path="crm" element={<CRM />} />
                <Route path="documents" element={<Documents />} />
                <Route path="bdms" element={<BDMs />} />
                <Route path="profile" element={<Profile />} />
                <Route path="settings" element={<Settings />} />
                <Route path="masters/roles" element={<RoleMaster />} />
                <Route path="masters/admin" element={<AdminMaster />} />
                <Route path="masters/hsn-codes" element={<HsnCodeMaster />} />
                <Route path="masters/spocs" element={<SpocMaster />} />
                <Route path="masters/staffing-types" element={<StaffingTypes />} />
                <Route path="masters/invoice-types" element={<InvoiceTypes />} />
                <Route path="masters/redberyl-accounts" element={<RedberylAccounts />} />
                <Route path="onedrive-test" element={<OneDriveTest />} />
              </Route>
            </Route>

            {/* 404 Route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
