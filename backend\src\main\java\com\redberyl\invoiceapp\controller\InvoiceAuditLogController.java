package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.InvoiceAuditLogDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.InvoiceAuditLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Tag(name = "Invoice Audit Log", description = "Invoice Audit Log management API")
public class InvoiceAuditLogController {

    @Autowired
    private InvoiceAuditLogService invoiceAuditLogService;

    @GetMapping("/invoice-audit-logs/getAll")
    @Operation(summary = "Get all invoice audit logs", description = "Get all invoice audit logs")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice audit logs found"),
            @ApiResponse(responseCode = "204", description = "No invoice audit logs found", content = @Content)
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceAuditLogDto>> getAllInvoiceAuditLogs() {
        try {
            List<InvoiceAuditLogDto> invoiceAuditLogs = invoiceAuditLogService.getAllInvoiceAuditLogs();
            return new ResponseEntity<>(invoiceAuditLogs, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/invoice-audit-logs/getById/{id}")
    @Operation(summary = "Get invoice audit log by ID", description = "Get invoice audit log by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice audit log found"),
            @ApiResponse(responseCode = "404", description = "Invoice audit log not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceAuditLogDto> getInvoiceAuditLogById(@PathVariable Long id) {
        InvoiceAuditLogDto invoiceAuditLog = invoiceAuditLogService.getInvoiceAuditLogById(id);
        return new ResponseEntity<>(invoiceAuditLog, HttpStatus.OK);
    }

    @GetMapping("/invoice-audit-logs/getByInvoiceId/{invoiceId}")
    @Operation(summary = "Get invoice audit logs by invoice ID", description = "Get invoice audit logs by invoice ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice audit logs found"),
            @ApiResponse(responseCode = "204", description = "No invoice audit logs found for this invoice"),
            @ApiResponse(responseCode = "404", description = "Invoice not found"),
            @ApiResponse(responseCode = "400", description = "Invalid invoice ID supplied")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceAuditLogDto>> getInvoiceAuditLogsByInvoiceId(@PathVariable Long invoiceId) {
        try {
            List<InvoiceAuditLogDto> invoiceAuditLogs = invoiceAuditLogService
                    .getInvoiceAuditLogsByInvoiceId(invoiceId);
            return new ResponseEntity<>(invoiceAuditLogs, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/invoice-audit-logs/create")
    @Operation(summary = "Create invoice audit log", description = "Create invoice audit log")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Invoice audit log created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceAuditLogDto> createInvoiceAuditLog(
            @Valid @RequestBody InvoiceAuditLogDto invoiceAuditLogDto) {
        InvoiceAuditLogDto createdInvoiceAuditLog = invoiceAuditLogService.createInvoiceAuditLog(invoiceAuditLogDto);
        return new ResponseEntity<>(createdInvoiceAuditLog, HttpStatus.CREATED);
    }

    @DeleteMapping("/invoice-audit-logs/deleteById/{id}")
    @Operation(summary = "Delete invoice audit log", description = "Delete invoice audit log")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Invoice audit log deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied or invoice audit log is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "Invoice audit log not found")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteInvoiceAuditLog(@PathVariable Long id) {
        invoiceAuditLogService.deleteInvoiceAuditLog(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
