package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.BdmDto;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.service.BdmService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * This controller provides compatibility for the /api/bdms endpoint
 * to ensure the frontend can access BDM data from this endpoint.
 */
@RestController
@RequestMapping("/bdms")
@Tag(name = "BDM Compatibility", description = "BDM compatibility API for multiple endpoint formats")
public class BdmCompatController {

    @Autowired
    private BdmService bdmService;

    @GetMapping
    @Operation(summary = "Get all BDMs (compatibility endpoint)", description = "Retrieve a list of all BDMs")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<BdmDto>> getAllBdms() {
        try {
            List<BdmDto> bdms = bdmService.getAllBdms();
            return new ResponseEntity<>(bdms, HttpStatus.OK);
        } catch (NoContentException e) {
            // Return empty list instead of 204 No Content to avoid frontend issues
            return new ResponseEntity<>(List.of(), HttpStatus.OK);
        } catch (Exception e) {
            throw new CustomException("Error retrieving BDMs: " + e.getMessage(), e);
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get BDM by ID (compatibility endpoint)", description = "Retrieve a BDM by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<BdmDto> getBdmById(@PathVariable Long id) {
        try {
            BdmDto bdm = bdmService.getBdmById(id);
            return new ResponseEntity<>(bdm, HttpStatus.OK);
        } catch (ResourceNotFoundException e) {
            throw new ResourceNotFoundException("BDM not found with id: " + id);
        } catch (Exception e) {
            throw new CustomException("Error retrieving BDM: " + e.getMessage(), e);
        }
    }

    @PostMapping
    @Operation(summary = "Create BDM (compatibility endpoint)", description = "Create a new BDM")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<BdmDto> createBdm(@Valid @RequestBody BdmDto bdmDto) {
        try {
            // Ensure clientCount and projectCount are not set in the request
            bdmDto.setClientCount(null);
            bdmDto.setProjectCount(null);

            BdmDto createdBdm = bdmService.createBdm(bdmDto);
            return new ResponseEntity<>(createdBdm, HttpStatus.CREATED);
        } catch (NullConstraintViolationException e) {
            throw new NullConstraintViolationException(e.getFieldName(), e.getMessage());
        } catch (UniqueConstraintViolationException e) {
            throw new UniqueConstraintViolationException(e.getFieldName(), e.getMessage());
        } catch (Exception e) {
            throw new CustomException("Error creating BDM: " + e.getMessage(), e);
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update BDM (compatibility endpoint)", description = "Update an existing BDM")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<BdmDto> updateBdm(@PathVariable Long id, @Valid @RequestBody BdmDto bdmDto) {
        try {
            BdmDto updatedBdm = bdmService.updateBdm(id, bdmDto);
            return new ResponseEntity<>(updatedBdm, HttpStatus.OK);
        } catch (ResourceNotFoundException e) {
            throw new ResourceNotFoundException("BDM not found with id: " + id);
        } catch (Exception e) {
            throw new CustomException("Error updating BDM: " + e.getMessage(), e);
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete BDM (compatibility endpoint)", description = "Delete a BDM by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteBdm(@PathVariable Long id) {
        try {
            bdmService.deleteBdm(id);
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        } catch (ResourceNotFoundException e) {
            throw new ResourceNotFoundException("BDM not found with id: " + id);
        } catch (Exception e) {
            throw new CustomException("Error deleting BDM: " + e.getMessage(), e);
        }
    }
}
