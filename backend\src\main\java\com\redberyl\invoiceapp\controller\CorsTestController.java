package com.redberyl.invoiceapp.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/cors-test")
@CrossOrigin(origins = "http://localhost:3000", allowCredentials = "true")
public class CorsTestController {

    @GetMapping
    public ResponseEntity<Map<String, String>> testCors() {
        Map<String, String> response = new HashMap<>();
        response.put("message", "CORS is working correctly!");
        response.put("status", "success");
        return ResponseEntity.ok(response);
    }
}
