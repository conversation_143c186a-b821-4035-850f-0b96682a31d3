import { api } from './api';

export interface Project {
  id: number;
  name: string;
  clientId: number;
  client?: {
    id: number;
    name: string;
  };
  description?: string;
  email?: string;
  phone?: string;
  gstNumber?: string;
  billingAddress?: string;
  shippingAddress?: string;
  state?: string;
  engagementCode?: string;
  clientPartnerName?: string;
  clientPartnerEmail?: string;
  clientPartnerPhone?: string;
  bdmId?: number;
  bdm?: {
    id: number;
    name: string;
  };
  hsnCodeId?: number;
  hsnCode?: {
    id: number;
    code: string;
    description: string;
  };
  commissionPercentage?: string | number;
  commissionAmount?: string | number;
  startDate?: string;
  endDate?: string;
  status?: string;
  value?: string | number;
  createdAt?: string;
  updatedAt?: string;
  managerSpocId?: number;
  managerSpoc?: {
    id: number;
    name: string;
  };
  accountHeadSpocId?: number;
  accountHeadSpoc?: {
    id: number;
    name: string;
  };
  businessHeadSpocId?: number;
  businessHeadSpoc?: {
    id: number;
    name: string;
  };
  hrSpocId?: number;
  hrSpoc?: {
    id: number;
    name: string;
  };
  financeSpocId?: number;
  financeSpoc?: {
    id: number;
    name: string;
  };
}

/**
 * Create a clean project payload for API submission
 * @param projectData Raw project data that might contain nested objects
 * @returns Clean project payload with only the required fields
 */
export const createCleanProjectPayload = (projectData: any): Partial<Project> => {
  // Helper function to check if a value is an empty object
  const isEmptyObject = (value: any): boolean => {
    return value && typeof value === 'object' && Object.keys(value).length === 0;
  };

  // Extract clientId from either direct clientId or nested client object
  let clientId = projectData.clientId;
  // If clientId is an empty object, set it to null
  if (isEmptyObject(clientId)) {
    clientId = null;
  }
  // If clientId is not available but client object is, use client.id
  if (!clientId && projectData.client && projectData.client.id) {
    clientId = projectData.client.id;
  }

  // Extract hsnCodeId from either direct hsnCodeId or nested hsnCode object
  let hsnCodeId = projectData.hsnCodeId;
  // If hsnCodeId is an empty object, set it to null
  if (isEmptyObject(hsnCodeId)) {
    hsnCodeId = null;
  }
  // If hsnCodeId is not available but hsnCode object is, use hsnCode.id
  if (!hsnCodeId && projectData.hsnCode && projectData.hsnCode.id) {
    hsnCodeId = projectData.hsnCode.id;
  }

  // Extract bdmId from either direct bdmId or nested bdm object
  let bdmId = projectData.bdmId;
  // If bdmId is an empty object, set it to null
  if (isEmptyObject(bdmId)) {
    bdmId = null;
  }
  // If bdmId is not available but bdm object is, use bdm.id
  if (!bdmId && projectData.bdm && projectData.bdm.id) {
    bdmId = projectData.bdm.id;
  }

  // Create a clean payload with only the fields needed for API submission
  const cleanPayload: any = {
    name: projectData.name,
    clientId: clientId,
    hsnCodeId: hsnCodeId,
    description: projectData.description,
    email: projectData.email,
    phone: projectData.phone,
    gstNumber: projectData.gstNumber,
    billingAddress: projectData.billingAddress,
    shippingAddress: projectData.shippingAddress,
    state: projectData.state,
    engagementCode: projectData.engagementCode,
    clientPartnerName: projectData.clientPartnerName,
    clientPartnerEmail: projectData.clientPartnerEmail,
    clientPartnerPhone: projectData.clientPartnerPhone,
    bdmId: bdmId,
    commissionPercentage: projectData.commissionPercentage,
    commissionAmount: projectData.commissionAmount,
    startDate: projectData.startDate,
    endDate: projectData.endDate,
    status: projectData.status,
    value: projectData.value
  };

  // Only include ID if it exists and is valid
  if (projectData.id) {
    // Convert string ID to number if needed
    if (typeof projectData.id === 'string') {
      const numericId = parseInt(projectData.id, 10);
      if (!isNaN(numericId)) {
        cleanPayload.id = numericId;
      } else {
        console.warn(`Invalid project ID format: ${projectData.id}, not including in payload`);
      }
    } else {
      cleanPayload.id = projectData.id;
    }
  }

  // Ensure numeric fields are actually numbers, not strings
  if (typeof cleanPayload.clientId === 'string') {
    cleanPayload.clientId = parseInt(cleanPayload.clientId, 10);
  }
  if (typeof cleanPayload.hsnCodeId === 'string') {
    cleanPayload.hsnCodeId = parseInt(cleanPayload.hsnCodeId, 10);
  }
  if (typeof cleanPayload.bdmId === 'string') {
    cleanPayload.bdmId = parseInt(cleanPayload.bdmId, 10);
  }
  if (typeof cleanPayload.commissionPercentage === 'string') {
    cleanPayload.commissionPercentage = parseFloat(cleanPayload.commissionPercentage);
  }
  if (typeof cleanPayload.commissionAmount === 'string') {
    cleanPayload.commissionAmount = parseFloat(cleanPayload.commissionAmount);
  }

  // Explicitly remove the full objects to avoid sending both ID and object
  delete cleanPayload.client;
  delete cleanPayload.hsnCode;
  delete cleanPayload.bdm;

  console.log('Final clean payload:', {
    id: cleanPayload.id,
    name: cleanPayload.name,
    clientId: cleanPayload.clientId,
    hsnCodeId: cleanPayload.hsnCodeId,
    bdmId: cleanPayload.bdmId
  });

  return cleanPayload;
};

export const projectService = {
  /**
   * Get all projects
   * @returns Promise with array of projects
   */
  getAllProjects: async (): Promise<Project[]> => {
    try {
      console.log('ProjectService: Fetching all projects');

      // Try multiple endpoints in order of preference
      const endpoints = [
        '/projects/getAll',
        '/api/projects',
        '/projects',
        '/api/noauth/projects'
      ];

      let response = null;
      let data = null;

      // Try each endpoint until one works
      for (const endpoint of endpoints) {
        try {
          console.log(`ProjectService: Trying endpoint ${endpoint}`);

          response = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': 'Basic ' + btoa('admin:admin123')
            }
          });

          console.log(`ProjectService: Response status for ${endpoint}:`, response.status);

          if (response.ok) {
            data = await response.json();
            console.log(`ProjectService: Successfully fetched projects from ${endpoint}:`, data);

            if (Array.isArray(data) && data.length > 0) {
              break; // Exit the loop if we got valid data
            } else {
              console.warn(`ProjectService: Endpoint ${endpoint} returned empty array or invalid data`);
            }
          } else {
            console.warn(`ProjectService: Endpoint ${endpoint} returned status ${response.status}`);
          }
        } catch (endpointError) {
          console.error(`ProjectService: Error fetching from ${endpoint}:`, endpointError);
        }
      }

      // If we have valid data from any endpoint, process and return it
      if (data && Array.isArray(data) && data.length > 0) {
        console.log('ProjectService: Processing project data:', data);
        return data.map(project => ({
          ...project,
          id: typeof project.id === 'string' ? parseInt(project.id, 10) : project.id,
          client: project.client || (project.clientId ? { id: project.clientId, name: 'Unknown' } : undefined)
        }));
      }

      // If all endpoints failed, return mock data
      console.warn('ProjectService: All endpoints failed, returning mock data');
      return [
        {
          id: 1,
          name: "Website Redesign",
          clientId: 1,
          client: { id: 1, name: "Acme Corporation" }
        },
        {
          id: 2,
          name: "ERP Implementation",
          clientId: 2,
          client: { id: 2, name: "Globex Industries" }
        },
        {
          id: 3,
          name: "Mobile App Development",
          clientId: 3,
          client: { id: 3, name: "Initech Solutions" }
        }
      ];
    } catch (error) {
      console.error('ProjectService: Error in getAllProjects:', error);

      // Return mock data in case of error
      console.log('ProjectService: Returning mock data due to error');
      return [
        {
          id: 1,
          name: "Website Redesign",
          clientId: 1,
          client: { id: 1, name: "Acme Corporation" }
        },
        {
          id: 2,
          name: "ERP Implementation",
          clientId: 2,
          client: { id: 2, name: "Globex Industries" }
        },
        {
          id: 3,
          name: "Mobile App Development",
          clientId: 3,
          client: { id: 3, name: "Initech Solutions" }
        }
      ];
    }
  },

  /**
   * Get project by ID
   * @param id Project ID
   * @returns Promise with project data
   */
  getProjectById: async (id: number): Promise<Project> => {
    try {
      console.log(`ProjectService: Fetching project with ID ${id}`);

      // Try the API method first
      try {
        const project = await api.getProject(id);

        // Check if project is an object
        if (project && typeof project === 'object') {
          console.log(`ProjectService: Successfully fetched project with ID ${id}`, project);
          return project;
        }

        console.warn(`ProjectService: API did not return a valid project object for ID ${id}`, project);
      } catch (apiError) {
        console.error(`ProjectService: Error fetching project with ID ${id} from API:`, apiError);
      }

      // If API method fails, try direct fetch
      console.log(`ProjectService: Trying alternative approach to fetch project with ID ${id}`);

      // Create basic auth header
      const authHeader = 'Basic ' + btoa('admin:admin123');

      // Try multiple endpoints through the proxy
      const endpoints = [
        `/api/projects/${id}`,
        `/api/v1/projects/${id}`,
        `/api/projects/getById/${id}`
      ];

      for (const endpoint of endpoints) {
        try {
          console.log(`ProjectService: Trying endpoint ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            credentials: 'include'
          });

          if (!response.ok) {
            console.warn(`ProjectService: Endpoint ${endpoint} returned status ${response.status}`);
            continue;
          }

          const data = await response.json();
          console.log(`ProjectService: Successfully fetched project from ${endpoint}`, data);

          // Return the project data
          return data;
        } catch (endpointError) {
          console.error(`ProjectService: Error fetching from ${endpoint}:`, endpointError);
        }
      }

      // If all attempts fail, throw an error
      throw new Error(`Could not fetch project with ID ${id}`);
    } catch (error) {
      console.error(`ProjectService: Error in getProjectById:`, error);
      throw error;
    }
  },

  /**
   * Create a new project
   * @param projectData Project data to create
   * @returns Promise with created project
   */
  createProject: async (projectData: any): Promise<Project> => {
    try {
      console.log('ProjectService: Creating new project', projectData);

      // Create a clean payload
      const cleanPayload = createCleanProjectPayload(projectData);
      console.log('ProjectService: Clean payload for project creation', cleanPayload);

      // Ensure we're not sending empty objects for relationship IDs
      if (typeof cleanPayload.clientId === 'object' && Object.keys(cleanPayload.clientId).length === 0) {
        cleanPayload.clientId = null;
      }
      if (typeof cleanPayload.hsnCodeId === 'object' && Object.keys(cleanPayload.hsnCodeId).length === 0) {
        cleanPayload.hsnCodeId = null;
      }
      if (typeof cleanPayload.bdmId === 'object' && Object.keys(cleanPayload.bdmId).length === 0) {
        cleanPayload.bdmId = null;
      }

      // Create basic auth header
      const authHeader = 'Basic ' + btoa('admin:admin123');

      // Try multiple endpoints through the proxy - using the correct endpoints from the controllers
      const endpoints = [
        '/api/projects',                  // From ProjectController with @RequestMapping("/api/projects")
        '/api/projects/create',           // From ProjectController with specific mapping
        '/api/noauth/createProject',      // From NoAuthController
        '/api/v1/projects/create'         // Alternative endpoint
      ];

      let lastError = null;

      for (const endpoint of endpoints) {
        try {
          console.log(`ProjectService: Trying to create project at ${endpoint}`);
          console.log(`ProjectService: Payload being sent:`, JSON.stringify(cleanPayload, null, 2));

          const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            body: JSON.stringify(cleanPayload)
          });

          if (!response.ok) {
            let errorMessage = "";
            try {
              // Try to parse the error as JSON
              const errorJson = await response.json();
              console.error(`ProjectService: Error from ${endpoint} (JSON):`, errorJson);
              errorMessage = errorJson.message || errorJson.error || JSON.stringify(errorJson);
            } catch (e) {
              // If not JSON, get as text
              const errorText = await response.text();
              console.error(`ProjectService: Error from ${endpoint} (Text):`, errorText);
              errorMessage = errorText;
            }

            lastError = new Error(`Server returned ${response.status}: ${errorMessage}`);
            continue;
          }

          const data = await response.json();
          console.log(`ProjectService: Successfully created project at ${endpoint}`, data);

          // Return the created project
          return data;
        } catch (endpointError) {
          console.error(`ProjectService: Error creating project at ${endpoint}:`, endpointError);
          lastError = endpointError;
        }
      }

      // If all attempts fail, throw the last error
      throw lastError || new Error('Could not create project');
    } catch (error) {
      console.error('ProjectService: Error in createProject:', error);
      throw error;
    }
  },

  /**
   * Update an existing project
   * @param id Project ID (can be string or number)
   * @param projectData Project data to update
   * @returns Promise with updated project
   */
  updateProject: async (id: string | number, projectData: any): Promise<Project> => {
    try {
      // Ensure ID is a number for the API
      const numericId = typeof id === 'string' ? parseInt(id, 10) : id;

      if (isNaN(numericId)) {
        throw new Error(`Invalid project ID: ${id} is not a valid number`);
      }

      console.log(`ProjectService: Updating project with ID ${numericId} (original: ${id})`, projectData);

      // Create a clean payload
      const cleanPayload = createCleanProjectPayload(projectData);
      console.log('ProjectService: Clean payload for project update', cleanPayload);

      // Create basic auth header
      const authHeader = 'Basic ' + btoa('admin:admin123');

      // Try multiple endpoints
      const endpoints = [
        `http://*************:8091/projects/update/${numericId}`,
        `http://*************:8091/api/projects/${numericId}`,
        `http://*************:8091/api/projects/update/${numericId}`,
        `http://*************:8091/api/v1/projects/${numericId}`
      ];

      let lastError = null;
      let lastResponseStatus = null;
      let lastResponseText = null;

      for (const endpoint of endpoints) {
        try {
          console.log(`ProjectService: Trying to update project at ${endpoint}`);
          console.log(`ProjectService: Request payload:`, JSON.stringify(cleanPayload, null, 2));

          const response = await fetch(endpoint, {
            method: 'PUT',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            body: JSON.stringify(cleanPayload)
          });

          if (!response.ok) {
            lastResponseStatus = response.status;

            try {
              // Try to get error details as JSON
              const errorJson = await response.json();
              console.warn(`ProjectService: Endpoint ${endpoint} returned status ${response.status} with JSON:`, errorJson);
              lastResponseText = JSON.stringify(errorJson);
            } catch (e) {
              // If not JSON, get as text
              const errorText = await response.text();
              console.warn(`ProjectService: Endpoint ${endpoint} returned status ${response.status} with text:`, errorText);
              lastResponseText = errorText;
            }

            continue;
          }

          const data = await response.json();
          console.log(`ProjectService: Successfully updated project at ${endpoint}`, data);

          // Return the updated project
          return data;
        } catch (endpointError) {
          console.error(`ProjectService: Error updating project at ${endpoint}:`, endpointError);
          lastError = endpointError;
        }
      }

      // If all attempts fail, throw an error with detailed information
      const errorMessage = lastResponseText
        ? `Could not update project with ID ${numericId}. Server returned ${lastResponseStatus}: ${lastResponseText}`
        : `Could not update project with ID ${numericId}. ${lastError?.message || 'Unknown error'}`;

      throw new Error(errorMessage);
    } catch (error) {
      console.error(`ProjectService: Error in updateProject:`, error);
      throw error;
    }
  }
};
