/**
 * <PERSON><PERSON><PERSON> to find and display the machine's IP addresses
 * This helps users access the application from other devices on the network
 */

const os = require('os');
const interfaces = os.networkInterfaces();
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

console.log(`${colors.bright}${colors.blue}=== Network Interfaces ====${colors.reset}\n`);

// Get all network interfaces
Object.keys(interfaces).forEach((interfaceName) => {
  const networkInterface = interfaces[interfaceName];

  // Skip internal/loopback interfaces
  if (networkInterface.some(addr => !addr.internal)) {
    console.log(`${colors.bright}${colors.cyan}Interface: ${interfaceName}${colors.reset}`);

    // Display all addresses for this interface
    networkInterface.forEach((address) => {
      if (address.family === 'IPv4' && !address.internal) {
        console.log(`  ${colors.green}IPv4: ${address.address}${colors.reset}`);
      } else if (address.family === 'IPv6' && !address.internal) {
        console.log(`  IPv6: ${address.address}`);
      }
    });
    console.log('');
  }
});

// Display usage instructions
console.log(`${colors.bright}${colors.yellow}=== How to Access Your Application ====${colors.reset}`);
console.log(`
1. Start your backend server:
   ${colors.cyan}cd backend && mvn spring-boot:run${colors.reset}

2. Start your frontend server:
   ${colors.cyan}cd frontend && npm run dev${colors.reset}

3. Access your application:

   ${colors.bright}${colors.blue}Local Development (on your machine):${colors.reset}
   ${colors.green}http://localhost:3060${colors.reset}

   ${colors.bright}${colors.blue}From Other Devices (on the same network):${colors.reset}
   ${colors.green}http://<YOUR_IP_ADDRESS>:3060${colors.reset}

   Replace <YOUR_IP_ADDRESS> with one of the IPv4 addresses listed above.

   Example: ${colors.green}http://*************:3060${colors.reset}
`);

console.log(`${colors.bright}${colors.yellow}Notes:${colors.reset}
1. The application is configured to work with both localhost and IP address access.
2. API calls will automatically use the correct backend URL based on how you access the frontend.
3. Make sure your firewall allows connections to ports 3060 (frontend) and 8091 (backend).
`);
