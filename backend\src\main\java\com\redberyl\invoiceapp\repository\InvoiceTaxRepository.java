package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.InvoiceTax;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InvoiceTaxRepository extends JpaRepository<InvoiceTax, Long> {
    List<InvoiceTax> findByInvoiceId(Long invoiceId);
    List<InvoiceTax> findByTaxRateId(Long taxRateId);
}
