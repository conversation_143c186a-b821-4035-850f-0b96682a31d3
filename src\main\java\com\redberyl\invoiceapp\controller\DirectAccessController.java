package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.TaxRateDto;
import com.redberyl.invoiceapp.dto.TaxTypeDto;
import com.redberyl.invoiceapp.service.TaxRateService;
import com.redberyl.invoiceapp.service.TaxTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/direct")
@Tag(name = "Direct Access", description = "Direct access API without authentication")
public class DirectAccessController {

    @Autowired
    private TaxTypeService taxTypeService;

    @Autowired
    private TaxRateService taxRateService;

    @GetMapping("/tax-types")
    @Operation(summary = "Get all tax types", description = "Retrieve a list of all tax types without authentication")
    public ResponseEntity<List<TaxTypeDto>> getAllTaxTypes() {
        List<TaxTypeDto> taxTypes = taxTypeService.getAllTaxTypes();
        return new ResponseEntity<>(taxTypes, HttpStatus.OK);
    }

    @GetMapping("/tax-rates")
    @Operation(summary = "Get all tax rates", description = "Retrieve a list of all tax rates without authentication")
    public ResponseEntity<List<TaxRateDto>> getAllTaxRates() {
        List<TaxRateDto> taxRates = taxRateService.getAllTaxRates();
        return new ResponseEntity<>(taxRates, HttpStatus.OK);
    }
}
