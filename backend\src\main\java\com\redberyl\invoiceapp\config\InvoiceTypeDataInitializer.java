package com.redberyl.invoiceapp.config;

import com.redberyl.invoiceapp.entity.InvoiceType;
import com.redberyl.invoiceapp.repository.InvoiceTypeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import java.util.Arrays;
import java.util.List;

/**
 * Initializes invoice type data if the table is empty
 */
@Configuration
public class InvoiceTypeDataInitializer {

    @Autowired
    private InvoiceTypeRepository invoiceTypeRepository;

    @Bean
    @Order(3) // Run after other initializers
    public CommandLineRunner initInvoiceTypeData() {
        return args -> {
            // Only add sample data if the invoice_types table is empty
            if (invoiceTypeRepository.count() == 0) {
                System.out.println("Initializing invoice type data...");

                // Create standard invoice types
                List<InvoiceType> invoiceTypes = Arrays.asList(
                    createInvoiceType("Standard", "Regular invoice for services or products"),
                    createInvoiceType("Proforma", "Preliminary bill of sale sent to buyers in advance of a shipment or delivery"),
                    createInvoiceType("Credit Note", "Document issued to indicate a return of funds"),
                    createInvoiceType("Debit Note", "Document issued to request additional payment"),
                    createInvoiceType("Tax Invoice", "Invoice that includes tax information"),
                    createInvoiceType("Commercial Invoice", "Used for international shipping and customs"),
                    createInvoiceType("Recurring Invoice", "Automatically generated on a regular schedule"),
                    createInvoiceType("Final Invoice", "The last invoice sent for a project")
                );

                // Save all invoice types
                invoiceTypeRepository.saveAll(invoiceTypes);
                System.out.println("Invoice type data initialized successfully!");
                
                // Print all invoice types for verification
                System.out.println("All invoice types in database:");
                invoiceTypeRepository.findAll()
                        .forEach(type -> System.out.println("ID: " + type.getId() + 
                                                          ", Type: " + type.getInvoiceType() + 
                                                          ", Description: " + type.getTypeDesc()));
            } else {
                System.out.println("Invoice types already exist in the database. Skipping initialization.");
                
                // Print existing invoice types
                System.out.println("Existing invoice types in database:");
                invoiceTypeRepository.findAll()
                        .forEach(type -> System.out.println("ID: " + type.getId() + 
                                                          ", Type: " + type.getInvoiceType() + 
                                                          ", Description: " + type.getTypeDesc()));
            }
        };
    }

    /**
     * Helper method to create an invoice type
     */
    private InvoiceType createInvoiceType(String type, String description) {
        InvoiceType invoiceType = new InvoiceType();
        invoiceType.setInvoiceType(type);
        invoiceType.setTypeDesc(description);
        return invoiceType;
    }
}
