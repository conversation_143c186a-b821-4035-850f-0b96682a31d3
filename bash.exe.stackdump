Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5A0) msys-2.0.dll+0x2118E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6A0  00021006A545 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBE4F10000 ntdll.dll
7FFBE4A90000 KERNEL32.DLL
7FFBE2400000 KERNELBASE.dll
7FFBE39D0000 USER32.dll
7FFBE27E0000 win32u.dll
7FFBE4D90000 GDI32.dll
7FFBE2A70000 gdi32full.dll
7FFBE22E0000 msvcp_win.dll
7FFBE2810000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBE3C50000 advapi32.dll
7FFBE3BA0000 msvcrt.dll
7FFBE4E00000 sechost.dll
7FFBE2140000 bcrypt.dll
7FFBE3D10000 RPCRT4.dll
7FFBE1670000 CRYPTBASE.DLL
7FFBE2000000 bcryptPrimitives.dll
7FFBE4DC0000 IMM32.DLL
