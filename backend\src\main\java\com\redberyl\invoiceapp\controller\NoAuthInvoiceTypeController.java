package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.InvoiceTypeDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.InvoiceTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controller for accessing invoice types without authentication
 * This is specifically created to ensure the frontend can access invoice types
 * even if there are authentication issues
 */
@RestController
@CrossOrigin(origins = { "http://localhost:3060", "http://127.0.0.1:3060", "http://localhost:3001", "http://127.0.0.1:3001" }, maxAge = 3600)
@Tag(name = "No Auth Invoice Type", description = "Invoice Type API without authentication")
public class NoAuthInvoiceTypeController {

    @Autowired
    private InvoiceTypeService invoiceTypeService;

    /**
     * Get all invoice types without authentication
     * @return List of invoice types
     */
    @GetMapping("/noauth/invoice-types")
    @Operation(summary = "Get all invoice types (no auth)", description = "Get all invoice types without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice types found"),
            @ApiResponse(responseCode = "204", description = "No invoice types found", content = @Content)
    })
    public ResponseEntity<List<InvoiceTypeDto>> getAllInvoiceTypesNoAuth() {
        try {
            List<InvoiceTypeDto> invoiceTypes = invoiceTypeService.getAllInvoiceTypes();
            return new ResponseEntity<>(invoiceTypes, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    /**
     * Test endpoint that returns a fixed list of invoice types
     * This is useful for testing when the database is not available
     * @return Fixed list of invoice types
     */
    @GetMapping("/noauth/invoice-types/test")
    @Operation(summary = "Test endpoint for invoice types", description = "Simple test endpoint that returns a fixed list of invoice types")
    public ResponseEntity<List<InvoiceTypeDto>> getTestInvoiceTypes() {
        // Create a fixed list of invoice types for testing
        List<InvoiceTypeDto> testTypes = List.of(
            InvoiceTypeDto.builder().id(1L).invoiceType("Standard").typeDesc("Regular invoice for services or products").build(),
            InvoiceTypeDto.builder().id(2L).invoiceType("Proforma").typeDesc("Preliminary bill of sale sent to buyers in advance of a shipment or delivery").build(),
            InvoiceTypeDto.builder().id(3L).invoiceType("Credit Note").typeDesc("Document issued to indicate a return of funds").build(),
            InvoiceTypeDto.builder().id(4L).invoiceType("Debit Note").typeDesc("Document issued to request additional payment").build()
        );
        
        return new ResponseEntity<>(testTypes, HttpStatus.OK);
    }
}
