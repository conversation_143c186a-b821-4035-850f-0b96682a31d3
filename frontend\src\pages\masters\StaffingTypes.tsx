import React, { useState, useEffect } from "react";
import { z } from "zod";
import MasterPageTemplate, { MasterItem } from "@/components/masters/MasterPageTemplate";
import { toast } from "sonner";
import { staffingTypeService, StaffingType } from "@/services/staffingTypeService";

// Define the schema for staffing type form
const staffingTypeSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
});

// Fallback mock data in case API fails
const fallbackStaffingTypes: MasterItem[] = [
  {
    id: "1",
    name: "Full-time",
    description: "Regular full-time employment",
  },
  {
    id: "2",
    name: "Part-time",
    description: "Regular part-time employment",
  },
  {
    id: "3",
    name: "Contract",
    description: "Fixed-term contract employment",
  },
  {
    id: "4",
    name: "Temporary",
    description: "Short-term temporary employment",
  },
];

const StaffingTypes = () => {
  const [staffingTypes, setStaffingTypes] = useState<MasterItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState<number>(0);

  // Fetch staffing types from API
  useEffect(() => {
    const fetchStaffingTypes = async () => {
      setIsLoading(true);
      setError(null);

      try {
        console.log("StaffingTypes: Fetching staffing types from API...");

        // Try multiple direct fetch endpoints for debugging
        const endpoints = [
          'http://*************:8091/staffing-types/getAll',
          'http://*************:8091/api/staffing-types',
          'http://*************:8091/api/noauth/staffing-types'
        ];

        let directData = null;

        // First try with auth headers
        for (const endpoint of endpoints) {
          try {
            console.log(`StaffingTypes: Trying direct fetch to ${endpoint} with auth...`);
            const authHeader = 'Basic ' + btoa('admin:admin123');
            const directResponse = await fetch(endpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': authHeader
              }
            });

            console.log(`StaffingTypes: Direct fetch response status for ${endpoint}:`, directResponse.status);
            if (directResponse.ok) {
              directData = await directResponse.json();
              console.log(`StaffingTypes: Direct fetch successful for ${endpoint}:`, directData);
              break; // Exit the loop if successful
            } else {
              console.warn(`StaffingTypes: Direct fetch failed for ${endpoint} with status:`, directResponse.status);
            }
          } catch (directError) {
            console.error(`StaffingTypes: Direct fetch error for ${endpoint}:`, directError);
          }
        }

        // If auth didn't work, try without auth for the no-auth endpoint
        if (!directData) {
          try {
            const noAuthEndpoint = 'http://*************:8091/api/noauth/staffing-types';
            console.log(`StaffingTypes: Trying direct fetch to ${noAuthEndpoint} without auth...`);
            const noAuthResponse = await fetch(noAuthEndpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
              }
            });

            console.log(`StaffingTypes: No-auth fetch response status:`, noAuthResponse.status);
            if (noAuthResponse.ok) {
              directData = await noAuthResponse.json();
              console.log(`StaffingTypes: No-auth fetch successful:`, directData);
            } else {
              console.warn(`StaffingTypes: No-auth fetch failed with status:`, noAuthResponse.status);
            }
          } catch (noAuthError) {
            console.error(`StaffingTypes: No-auth fetch error:`, noAuthError);
          }
        }

        // If still no data, try the test endpoint
        if (!directData) {
          try {
            const testEndpoint = 'http://*************:8091/api/noauth/staffing-types/test';
            console.log(`StaffingTypes: Trying test endpoint ${testEndpoint}...`);
            const testResponse = await fetch(testEndpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
              }
            });

            console.log(`StaffingTypes: Test endpoint response status:`, testResponse.status);
            if (testResponse.ok) {
              directData = await testResponse.json();
              console.log(`StaffingTypes: Test endpoint successful:`, directData);
            } else {
              console.warn(`StaffingTypes: Test endpoint failed with status:`, testResponse.status);
            }
          } catch (testError) {
            console.error(`StaffingTypes: Test endpoint error:`, testError);
          }
        }

        // Use direct data if available, otherwise try the service
        const data = directData || await staffingTypeService.getAllStaffingTypes();
        console.log("StaffingTypes: Service returned data:", data);

        // Transform API data to match MasterItem format
        const transformedData = data.map((item: StaffingType) => ({
          id: item.id?.toString() || "",
          name: item.name,
          description: item.description || "",
        }));

        setStaffingTypes(transformedData);
        console.log("StaffingTypes: Successfully fetched staffing types:", transformedData);
      } catch (error) {
        console.error("StaffingTypes: Error fetching staffing types:", error);
        setError(error as Error);
        toast.error("Failed to load staffing types. Using fallback data.");

        // Use fallback data if API fails
        setStaffingTypes(fallbackStaffingTypes);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStaffingTypes();
  }, [refreshTrigger]);

  // Handle adding a new staffing type
  const handleAddStaffingType = async (data: z.infer<typeof staffingTypeSchema>) => {
    try {
      const newStaffingType: StaffingType = {
        name: data.name,
        description: data.description,
      };

      // Call API to create staffing type
      const createdStaffingType = await staffingTypeService.createStaffingType(newStaffingType);

      toast.success("Staffing type created successfully");

      // Refresh the list
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error("Error creating staffing type:", error);
      toast.error("Failed to create staffing type");
    }
  };

  // Handle editing a staffing type
  const handleEditStaffingType = async (id: string, data: z.infer<typeof staffingTypeSchema>) => {
    try {
      console.log("Editing staffing type with ID:", id, "Data:", data);

      const updatedStaffingType: StaffingType = {
        id,
        name: data.name,
        description: data.description,
      };

      // Call API to update staffing type
      await staffingTypeService.updateStaffingType(id, updatedStaffingType);

      toast.success("Staffing type updated successfully");

      // Refresh the list
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error("Error updating staffing type:", error);
      toast.error("Failed to update staffing type");
    }
  };

  // Handle deleting a staffing type
  const handleDeleteStaffingType = async (id: string) => {
    try {
      // Call API to delete staffing type
      await staffingTypeService.deleteStaffingType(id);

      toast.success("Staffing type deleted successfully");

      // Refresh the list
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error("Error deleting staffing type:", error);
      toast.error("Failed to delete staffing type");
    }
  };

  return (
    <MasterPageTemplate
      title="Staffing Types"
      description="Manage staffing types for invoices and candidates"
      items={staffingTypes}
      formSchema={staffingTypeSchema}
      onAdd={handleAddStaffingType}
      onEdit={handleEditStaffingType}
      onDelete={handleDeleteStaffingType}
      isLoading={isLoading}
      error={error ? error.message : undefined}
    />
  );
};

export default StaffingTypes;
