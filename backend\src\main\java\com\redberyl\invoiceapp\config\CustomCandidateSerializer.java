package com.redberyl.invoiceapp.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.redberyl.invoiceapp.entity.Candidate;
import org.springframework.boot.jackson.JsonComponent;

import java.io.IOException;

@JsonComponent
public class CustomCandidateSerializer extends JsonSerializer<Candidate> {

    @Override
    public void serialize(Candidate candidate, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        jsonGenerator.writeStartObject();
        
        // Write the candidate properties
        jsonGenerator.writeNumberField("id", candidate.getId());
        jsonGenerator.writeStringField("name", candidate.getName());
        
        // Write client information
        if (candidate.getClient() != null) {
            jsonGenerator.writeNumberField("clientId", candidate.getClient().getId());
            
            // Write the client as a nested object
            jsonGenerator.writeObjectFieldStart("client");
            jsonGenerator.writeNumberField("id", candidate.getClient().getId());
            jsonGenerator.writeStringField("name", candidate.getClient().getName());
            jsonGenerator.writeEndObject();
        }
        
        // Write project information
        if (candidate.getProject() != null) {
            jsonGenerator.writeNumberField("projectId", candidate.getProject().getId());
            
            // Write the project as a nested object
            jsonGenerator.writeObjectFieldStart("project");
            jsonGenerator.writeNumberField("id", candidate.getProject().getId());
            jsonGenerator.writeStringField("name", candidate.getProject().getName());
            
            // Include client ID in project if available
            if (candidate.getProject().getClient() != null) {
                jsonGenerator.writeNumberField("clientId", candidate.getProject().getClient().getId());
            }
            
            jsonGenerator.writeEndObject();
        }
        
        // Write other candidate properties
        if (candidate.getJoiningDate() != null) {
            jsonGenerator.writeStringField("joiningDate", candidate.getJoiningDate().toString());
        }
        
        if (candidate.getBillingRate() != null) {
            jsonGenerator.writeNumberField("billingRate", candidate.getBillingRate());
        }
        
        if (candidate.getDesignation() != null) {
            jsonGenerator.writeStringField("designation", candidate.getDesignation());
        }
        
        // Write the audit fields
        if (candidate.getCreatedAt() != null) {
            jsonGenerator.writeStringField("created_at", candidate.getCreatedAt().toString());
        }
        
        if (candidate.getModifiedAt() != null) {
            jsonGenerator.writeStringField("updated_at", candidate.getModifiedAt().toString());
        }
        
        jsonGenerator.writeEndObject();
    }
}
