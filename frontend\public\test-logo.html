<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RedBeryl Logo Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .logo-test {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            background: #fafafa;
        }
        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .invoice-title {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            text-decoration: underline;
            letter-spacing: 2px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>RedBeryl Logo Test for Invoice Generation</h1>
        
        <div class="logo-test">
            <h3>Logo Test 1: Exact RedBeryl Logo (Used in PDF)</h3>
            <svg
                width="320"
                height="100"
                viewBox="0 0 800 250"
                style="border: 1px solid #ddd; background: white;"
            >
                <rect width="800" height="250" fill="white"/>

                <!-- Left Blue Cloud Shape -->
                <g transform="translate(20, 50)">
                  <path
                    d="M20 80 C20 40, 50 10, 90 10 C100 10, 110 12, 118 16 C125 8, 135 4, 146 4 C170 4, 190 24, 190 48 C190 52, 189 56, 188 60 C195 65, 200 73, 200 82 C200 95, 190 106, 177 106 L45 106 C30 106, 18 94, 18 79 C18 79, 19 79, 20 80 Z"
                    fill="#1E88E5"
                    stroke="#1565C0"
                    stroke-width="2"
                  />
                  <circle cx="80" cy="60" r="8" fill="#2196F3"/>
                </g>

                <!-- Right Pink/Magenta Cloud Shape -->
                <g transform="translate(160, 30)">
                  <path
                    d="M30 60 C30 35, 50 15, 75 15 C85 15, 94 18, 101 23 C108 18, 117 15, 127 15 C152 15, 172 35, 172 60 C172 63, 171 66, 170 69 C177 73, 182 80, 182 88 C182 98, 174 106, 164 106 L46 106 C34 106, 25 97, 25 86 C25 75, 32 66, 41 63 C35 59, 30 54, 30 60 Z"
                    fill="#E91E63"
                    stroke="#C2185B"
                    stroke-width="2"
                  />
                  <circle cx="90" cy="50" r="6" fill="#F06292"/>
                </g>

                <!-- Connecting elements -->
                <g transform="translate(120, 80)">
                  <circle cx="0" cy="0" r="4" fill="#9C27B0"/>
                  <circle cx="20" cy="10" r="3" fill="#673AB7"/>
                  <circle cx="40" cy="5" r="2" fill="#3F51B5"/>
                </g>

                <!-- RedBeryl Text -->
                <g transform="translate(350, 80)">
                  <text x="0" y="50" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="#E91E63">Red</text>
                  <text x="140" y="50" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="#1565C0">Beryl</text>
                  <text x="0" y="85" font-family="Arial, sans-serif" font-size="20" font-weight="600" fill="#424242" letter-spacing="4px">TECH SOLUTIONS</text>
                  <text x="0" y="110" font-family="Arial, sans-serif" font-size="16" fill="#666666" font-style="italic">Integrates Business With Technology</text>
                </g>
            </svg>
            <p><strong>Status:</strong> <span id="logo1-status">✅ Exact RedBeryl logo loaded successfully</span></p>
        </div>

        <div class="logo-test">
            <h3>Logo Test 2: External Image (if available)</h3>
            <img 
                id="external-logo"
                src="/assets/redberyl-logo.png"
                alt="RedBeryl Tech Solutions"
                style="width: 250px; height: 80px; object-fit: contain; border: 1px solid #ddd;"
                onerror="this.style.display='none'; document.getElementById('logo2-status').innerHTML='❌ External image not found';"
                onload="document.getElementById('logo2-status').innerHTML='✅ External image loaded successfully';"
            />
            <p><strong>Status:</strong> <span id="logo2-status">Loading...</span></p>
        </div>

        <div class="logo-test">
            <h3>Invoice Header Preview</h3>
            <div class="invoice-header">
                <div style="flex: 1;">
                    <svg
                        width="320"
                        height="100"
                        viewBox="0 0 800 250"
                    >
                        <rect width="800" height="250" fill="white"/>

                        <!-- Left Blue Cloud Shape -->
                        <g transform="translate(20, 50)">
                          <path
                            d="M20 80 C20 40, 50 10, 90 10 C100 10, 110 12, 118 16 C125 8, 135 4, 146 4 C170 4, 190 24, 190 48 C190 52, 189 56, 188 60 C195 65, 200 73, 200 82 C200 95, 190 106, 177 106 L45 106 C30 106, 18 94, 18 79 C18 79, 19 79, 20 80 Z"
                            fill="#1E88E5"
                            stroke="#1565C0"
                            stroke-width="2"
                          />
                          <circle cx="80" cy="60" r="8" fill="#2196F3"/>
                        </g>

                        <!-- Right Pink/Magenta Cloud Shape -->
                        <g transform="translate(160, 30)">
                          <path
                            d="M30 60 C30 35, 50 15, 75 15 C85 15, 94 18, 101 23 C108 18, 117 15, 127 15 C152 15, 172 35, 172 60 C172 63, 171 66, 170 69 C177 73, 182 80, 182 88 C182 98, 174 106, 164 106 L46 106 C34 106, 25 97, 25 86 C25 75, 32 66, 41 63 C35 59, 30 54, 30 60 Z"
                            fill="#E91E63"
                            stroke="#C2185B"
                            stroke-width="2"
                          />
                          <circle cx="90" cy="50" r="6" fill="#F06292"/>
                        </g>

                        <!-- Connecting elements -->
                        <g transform="translate(120, 80)">
                          <circle cx="0" cy="0" r="4" fill="#9C27B0"/>
                          <circle cx="20" cy="10" r="3" fill="#673AB7"/>
                          <circle cx="40" cy="5" r="2" fill="#3F51B5"/>
                        </g>

                        <!-- RedBeryl Text -->
                        <g transform="translate(350, 80)">
                          <text x="0" y="50" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="#E91E63">Red</text>
                          <text x="140" y="50" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="#1565C0">Beryl</text>
                          <text x="0" y="85" font-family="Arial, sans-serif" font-size="20" font-weight="600" fill="#424242" letter-spacing="4px">TECH SOLUTIONS</text>
                          <text x="0" y="110" font-family="Arial, sans-serif" font-size="16" fill="#666666" font-style="italic">Integrates Business With Technology</text>
                        </g>
                    </svg>
                </div>
                <div style="text-align: center; flex: 1;">
                    <h1 class="invoice-title">INVOICE</h1>
                </div>
                <div style="flex: 1;"></div>
            </div>
            <p>This is how the logo appears in the actual invoice header.</p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button" onclick="window.open('/invoice-pdf-demo', '_blank')">
                🧪 Test PDF Generation
            </button>
            <button class="test-button" onclick="window.open('/invoices', '_blank')">
                📄 Go to Invoices
            </button>
        </div>

        <div style="background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4>🔧 Troubleshooting:</h4>
            <ul>
                <li><strong>Logo not showing in PDF?</strong> The base64 SVG should always work</li>
                <li><strong>Want to use your own logo?</strong> Place it at <code>/public/assets/redberyl-logo.png</code></li>
                <li><strong>PDF generation failing?</strong> Check browser console for errors</li>
                <li><strong>Logo quality issues?</strong> Use high-resolution images (600x240px recommended)</li>
            </ul>
        </div>
    </div>

    <script>
        // Check if base64 logo loaded
        setTimeout(() => {
            const logo1Status = document.getElementById('logo1-status');
            if (logo1Status.textContent === 'Loading...') {
                logo1Status.innerHTML = '✅ Base64 SVG loaded successfully';
            }
        }, 1000);

        // Set default status for external logo if not changed
        setTimeout(() => {
            const logo2Status = document.getElementById('logo2-status');
            if (logo2Status.textContent === 'Loading...') {
                logo2Status.innerHTML = '⚠️ External image not found (this is normal if you haven\'t added your logo yet)';
            }
        }, 2000);
    </script>
</body>
</html>
