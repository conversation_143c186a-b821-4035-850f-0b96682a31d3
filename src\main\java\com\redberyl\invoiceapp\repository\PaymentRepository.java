package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.Payment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface PaymentRepository extends JpaRepository<Payment, Long> {
    List<Payment> findByInvoiceId(Long invoiceId);
    List<Payment> findByReceivedOnBetween(LocalDate startDate, LocalDate endDate);
    List<Payment> findByPaymentMode(String paymentMode);
}
