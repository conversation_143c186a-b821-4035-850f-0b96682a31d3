import React, { useState, useEffect } from 'react';
import { Box, Button, Text, Heading, Select, FormControl, FormLabel, VStack, Code, Divider } from '@chakra-ui/react';

const BdmTest = () => {
  const [bdms, setBdms] = useState([]);
  const [selectedBdm, setSelectedBdm] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [apiResponse, setApiResponse] = useState(null);

  const fetchBdms = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Fetching BDMs...');

      // Create basic auth header if needed
      const authHeader = 'Basic ' + btoa('admin:admin123');

      const response = await fetch('http://localhost:8091/api/v1/bdms', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': authHeader
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch BDMs: ${response.status}`);
      }

      const responseData = await response.json();
      console.log('BDM API response:', responseData);
      setApiResponse(responseData);

      // Extract BDMs from the nested structure based on the API response format
      let bdmsData = [];
      
      // Check for the specific format from BdmController.java
      if (responseData && responseData.success && responseData.data && responseData.data.content) {
        // This is the format from /api/v1/bdms endpoint
        console.log('Found BDMs in data.data.content:', responseData.data.content);
        bdmsData = responseData.data.content;
      } else if (Array.isArray(responseData)) {
        // Direct array format
        bdmsData = responseData;
      } else if (responseData && responseData.data && Array.isArray(responseData.data)) {
        // Data in data property
        bdmsData = responseData.data;
      } else if (responseData && responseData.content && Array.isArray(responseData.content)) {
        // Data in content property
        bdmsData = responseData.content;
      } else {
        console.error('Unexpected BDM data format:', responseData);
        throw new Error('Unexpected data format from BDM API');
      }

      setBdms(bdmsData);
    } catch (err) {
      console.error('Error fetching BDMs:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBdms();
  }, []);

  return (
    <Box p={5} shadow="md" borderWidth="1px" borderRadius="md">
      <VStack spacing={4} align="stretch">
        <Heading size="md">BDM Test Component</Heading>
        
        <Button 
          colorScheme="blue" 
          onClick={fetchBdms} 
          isLoading={loading}
          loadingText="Fetching BDMs"
        >
          Refresh BDMs
        </Button>
        
        {error && (
          <Box p={3} bg="red.100" color="red.800" borderRadius="md">
            <Text fontWeight="bold">Error:</Text>
            <Text>{error}</Text>
          </Box>
        )}
        
        <FormControl>
          <FormLabel>Select a BDM</FormLabel>
          <Select
            placeholder="Select BDM"
            value={selectedBdm}
            onChange={(e) => setSelectedBdm(e.target.value)}
            isDisabled={loading || bdms.length === 0}
          >
            {bdms.map((bdm) => (
              <option key={bdm.id} value={bdm.id}>
                {bdm.name}
              </option>
            ))}
          </Select>
        </FormControl>
        
        {selectedBdm && (
          <Box p={3} bg="blue.50" borderRadius="md">
            <Text fontWeight="bold">Selected BDM:</Text>
            <Text>{bdms.find(bdm => bdm.id === parseInt(selectedBdm))?.name}</Text>
          </Box>
        )}
        
        <Divider />
        
        <Heading size="sm">API Response Structure</Heading>
        {apiResponse && (
          <Box overflowX="auto" maxHeight="300px" overflowY="auto">
            <Code display="block" whiteSpace="pre" p={2}>
              {JSON.stringify(apiResponse, null, 2)}
            </Code>
          </Box>
        )}
        
        <Heading size="sm">Extracted BDMs</Heading>
        <Box overflowX="auto" maxHeight="300px" overflowY="auto">
          <Code display="block" whiteSpace="pre" p={2}>
            {JSON.stringify(bdms, null, 2)}
          </Code>
        </Box>
      </VStack>
    </Box>
  );
};

export default BdmTest;
