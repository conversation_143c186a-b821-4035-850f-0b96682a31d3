package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.DealDto;

import java.util.List;

public interface DealService {
    List<DealDto> getAllDeals();
    DealDto getDealById(Long id);
    List<DealDto> getDealsByLeadId(Long leadId);
    List<DealDto> getDealsByClientId(Long clientId);
    List<DealDto> getDealsByStatus(String status);
    DealDto createDeal(DealDto dealDto);
    DealDto updateDeal(Long id, DealDto dealDto);
    void deleteDeal(Long id);
}
