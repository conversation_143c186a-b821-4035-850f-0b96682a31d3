import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "0.0.0.0", // Listen on all interfaces
    port: 3060,
    strictPort: true,
    open: true,
    cors: true,
    // Enable history API fallback for client-side routing
    // This ensures that refreshing pages like /clients, /invoices works correctly
    historyApiFallback: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, ''), // Strip /api prefix for backend endpoints
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('Proxy error:', err.message);
        },
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (_proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        }
      },
      // REMOVED: Frontend routes that were causing refresh issues
      // These routes should be handled by React Router, not proxied to backend:
      // - /candidates, /projects, /clients, /invoices
      //
      // When these were proxied, refreshing pages like /clients would show
      // JSON data instead of the React component because the request was
      // being forwarded to the backend API instead of letting React Router handle it.
      // IMPORTANT: Only proxy actual API endpoints, not frontend routes
      // Frontend routes like /invoices, /candidates, /clients, etc. should be handled by React Router
      // API calls should use direct URLs like http://192.168.1.100:8091/api/... or be prefixed with /api/
    }
  },
  base: "/",
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
