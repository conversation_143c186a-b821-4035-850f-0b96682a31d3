package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.LeadDto;
import com.redberyl.invoiceapp.entity.Lead;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.LeadRepository;
import com.redberyl.invoiceapp.service.LeadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class LeadServiceImpl implements LeadService {

    @Autowired
    private LeadRepository leadRepository;

    @Override
    public List<LeadDto> getAllLeads() {
        List<Lead> leads = leadRepository.findAll();
        if (leads.isEmpty()) {
            throw new NoContentException("No leads found");
        }
        return leads.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public LeadDto getLeadById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Lead ID cannot be null");
        }

        Lead lead = leadRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Lead not found with id: " + id));
        return convertToDto(lead);
    }

    @Override
    public List<LeadDto> getLeadsByStatus(String status) {
        if (!StringUtils.hasText(status)) {
            throw new NullConstraintViolationException("status", "Status cannot be empty");
        }

        List<Lead> leads = leadRepository.findByStatus(status);
        if (leads.isEmpty()) {
            throw new NoContentException("No leads found with status: " + status);
        }

        return leads.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<LeadDto> getLeadsBySource(String source) {
        if (!StringUtils.hasText(source)) {
            throw new NullConstraintViolationException("source", "Source cannot be empty");
        }

        List<Lead> leads = leadRepository.findBySource(source);
        if (leads.isEmpty()) {
            throw new NoContentException("No leads found with source: " + source);
        }

        return leads.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private void validateLeadDto(LeadDto leadDto) {
        if (leadDto == null) {
            throw new NullConstraintViolationException("leadDto", "Lead data cannot be null");
        }

        if (!StringUtils.hasText(leadDto.getName())) {
            throw new NullConstraintViolationException("name", "Lead name cannot be empty");
        }

        // Email is optional, only validate if provided
        if (StringUtils.hasText(leadDto.getEmail())) {
            // Check for duplicate email if it's a new lead
            if (leadDto.getId() == null &&
                    leadRepository.findByEmail(leadDto.getEmail()).isPresent()) {
                throw new UniqueConstraintViolationException("email",
                        "Lead with email " + leadDto.getEmail() + " already exists");
            }
        }
    }

    @Override
    @Transactional
    public LeadDto createLead(LeadDto leadDto) {
        validateLeadDto(leadDto);

        try {
            Lead lead = convertToEntity(leadDto);
            Lead savedLead = leadRepository.save(lead);
            return convertToDto(savedLead);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("email", "Lead with this email already exists");
            } else {
                throw new CustomException("Error creating lead: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating lead", e);
        }
    }

    @Override
    @Transactional
    public LeadDto updateLead(Long id, LeadDto leadDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Lead ID cannot be null");
        }

        if (leadDto == null) {
            throw new NullConstraintViolationException("leadDto", "Lead data cannot be null");
        }

        Lead existingLead = leadRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Lead not found with id: " + id));

        // Check for duplicate email if it's being changed
        if (StringUtils.hasText(leadDto.getEmail()) &&
                !leadDto.getEmail().equals(existingLead.getEmail()) &&
                leadRepository.findByEmail(leadDto.getEmail()).isPresent()) {
            throw new UniqueConstraintViolationException("email",
                    "Lead with email " + leadDto.getEmail() + " already exists");
        }

        try {
            if (StringUtils.hasText(leadDto.getName())) {
                existingLead.setName(leadDto.getName());
            }

            if (StringUtils.hasText(leadDto.getEmail())) {
                existingLead.setEmail(leadDto.getEmail());
            }

            if (leadDto.getCompany() != null) {
                existingLead.setCompany(leadDto.getCompany());
            }

            if (StringUtils.hasText(leadDto.getPhone())) {
                existingLead.setPhone(leadDto.getPhone());
            }

            if (StringUtils.hasText(leadDto.getStatus())) {
                existingLead.setStatus(leadDto.getStatus());
            }

            if (StringUtils.hasText(leadDto.getSource())) {
                existingLead.setSource(leadDto.getSource());
            }

            Lead updatedLead = leadRepository.save(existingLead);
            return convertToDto(updatedLead);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("email", "Lead with this email already exists");
            } else {
                throw new CustomException("Error updating lead: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error updating lead", e);
        }
    }

    @Override
    @Transactional
    public void deleteLead(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Lead ID cannot be null");
        }

        if (!leadRepository.existsById(id)) {
            throw new ResourceNotFoundException("Lead not found with id: " + id);
        }

        try {
            leadRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete lead because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting lead: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting lead", e);
        }
    }

    private LeadDto convertToDto(Lead lead) {
        LeadDto dto = LeadDto.builder()
                .id(lead.getId())
                .name(lead.getName())
                .email(lead.getEmail())
                .company(lead.getCompany())
                .phone(lead.getPhone())
                .status(lead.getStatus())
                .source(lead.getSource())
                .build();

        // Set the audit fields
        dto.setCreatedAt(lead.getCreatedAt());
        dto.setUpdatedAt(lead.getModifiedAt());

        return dto;
    }

    private Lead convertToEntity(LeadDto dto) {
        Lead lead = new Lead();
        lead.setId(dto.getId());
        lead.setName(dto.getName());
        lead.setEmail(dto.getEmail());
        lead.setCompany(dto.getCompany());
        lead.setPhone(dto.getPhone());
        lead.setStatus(dto.getStatus());
        lead.setSource(dto.getSource());

        return lead;
    }

    @Override
    public Optional<Lead> findByEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return Optional.empty();
        }
        return leadRepository.findByEmail(email);
    }
}
