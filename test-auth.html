<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .form-container {
            flex: 1;
            border: 1px solid #ccc;
            padding: 20px;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, select {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 100px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>Authentication Test</h1>
    <p>This page tests the authentication endpoints for both localhost and IP address access.</p>

    <div class="container">
        <div class="form-container">
            <h2>Sign Up</h2>
            <form id="signupForm">
                <div class="form-group">
                    <label for="signupUsername">Username:</label>
                    <input type="text" id="signupUsername" required>
                </div>
                <div class="form-group">
                    <label for="signupEmail">Email:</label>
                    <input type="email" id="signupEmail" required>
                </div>
                <div class="form-group">
                    <label for="signupPassword">Password:</label>
                    <input type="password" id="signupPassword" required>
                </div>
                <div class="form-group">
                    <label for="signupRole">Role:</label>
                    <select id="signupRole">
                        <option value="user">User</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="signupEndpoint">API Endpoint:</label>
                    <select id="signupEndpoint">
                        <option value="auto">Auto (Based on current hostname)</option>
                        <option value="localhost">Localhost (http://*************:8091)</option>
                        <option value="ip">IP Address (http://************:8091)</option>
                    </select>
                </div>
                <button type="submit">Sign Up</button>
            </form>
            <div id="signupResult" class="result"></div>
        </div>

        <div class="form-container">
            <h2>Login</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="loginUsername">Username:</label>
                    <input type="text" id="loginUsername" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">Password:</label>
                    <input type="password" id="loginPassword" required>
                </div>
                <div class="form-group">
                    <label for="loginEndpoint">API Endpoint:</label>
                    <select id="loginEndpoint">
                        <option value="auto">Auto (Based on current hostname)</option>
                        <option value="localhost">Localhost (http://*************:8091)</option>
                        <option value="ip">IP Address (http://************:8091)</option>
                    </select>
                </div>
                <button type="submit">Login</button>
            </form>
            <div id="loginResult" class="result"></div>
        </div>
    </div>

    <script>
        document.getElementById('signupForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            const resultDiv = document.getElementById('signupResult');
            resultDiv.innerHTML = 'Processing...';

            try {
                const username = document.getElementById('signupUsername').value;
                const email = document.getElementById('signupEmail').value;
                const password = document.getElementById('signupPassword').value;
                const role = document.getElementById('signupRole').value;
                const endpointType = document.getElementById('signupEndpoint').value;

                // Determine the API URL based on the selected endpoint type
                let apiUrl;
                const hostname = window.location.hostname;

                if (endpointType === 'localhost') {
                    apiUrl = 'http://*************:8091/api/auth/signup';
                } else if (endpointType === 'ip') {
                    apiUrl = 'http://************:8091/api/auth/signup';
                } else { // auto
                    apiUrl = `http://${hostname}:8091/api/auth/signup`;
                }

                resultDiv.innerHTML += `<p>Using API URL: ${apiUrl}</p>`;

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Origin': window.location.origin
                    },
                    mode: 'cors',
                    credentials: 'omit', // Don't include credentials since we're using wildcard origins
                    body: JSON.stringify({
                        username,
                        email,
                        password,
                        roles: [role]
                    })
                });

                resultDiv.innerHTML += `<p>Response status: ${response.status}</p>`;

                const data = await response.json();
                resultDiv.innerHTML += `<p class="${response.ok ? 'success' : 'error'}">${JSON.stringify(data)}</p>`;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        });

        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = 'Processing...';

            try {
                const username = document.getElementById('loginUsername').value;
                const password = document.getElementById('loginPassword').value;
                const endpointType = document.getElementById('loginEndpoint').value;

                // Determine the API URL based on the selected endpoint type
                let apiUrl;
                const hostname = window.location.hostname;

                if (endpointType === 'localhost') {
                    apiUrl = 'http://*************:8091/api/auth/login';
                } else if (endpointType === 'ip') {
                    apiUrl = 'http://************:8091/api/auth/login';
                } else { // auto
                    apiUrl = `http://${hostname}:8091/api/auth/login`;
                }

                resultDiv.innerHTML += `<p>Using API URL: ${apiUrl}</p>`;

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Origin': window.location.origin
                    },
                    mode: 'cors',
                    credentials: 'omit', // Don't include credentials since we're using wildcard origins
                    body: JSON.stringify({
                        username,
                        password
                    })
                });

                resultDiv.innerHTML += `<p>Response status: ${response.status}</p>`;

                const data = await response.json();
                resultDiv.innerHTML += `<p class="${response.ok ? 'success' : 'error'}">${JSON.stringify(data)}</p>`;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
