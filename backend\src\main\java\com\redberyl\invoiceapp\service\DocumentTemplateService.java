package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.DocumentTemplateDto;

import java.util.List;

public interface DocumentTemplateService {
    List<DocumentTemplateDto> getAllDocumentTemplates();
    DocumentTemplateDto getDocumentTemplateById(Long id);
    List<DocumentTemplateDto> getDocumentTemplatesByType(String templateType);
    DocumentTemplateDto createDocumentTemplate(DocumentTemplateDto documentTemplateDto);
    DocumentTemplateDto updateDocumentTemplate(Long id, DocumentTemplateDto documentTemplateDto);
    void deleteDocumentTemplate(Long id);
}
