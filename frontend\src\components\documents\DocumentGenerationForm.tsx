import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { CalendarIcon, Download } from "lucide-react";
import { toast } from "sonner";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

// Define the form schema with Zod
const formSchema = z.object({
  templateId: z.string({
    required_error: "Please select a template",
  }),
  clientId: z.string({
    required_error: "Please select a client",
  }),
  variables: z.record(z.string()),
});

type FormValues = z.infer<typeof formSchema>;

// Mock data - would be fetched from API in real application
const templatesData = [
  {
    id: "1",
    name: "Invoice Template",
    type: "PDF",
    variables: ["client_name", "invoice_number", "amount", "date", "due_date"],
  },
  {
    id: "2",
    name: "Client Agreement",
    type: "DOCX",
    variables: ["client_name", "project_name", "start_date", "fee_structure"],
  },
  {
    id: "3",
    name: "Project Proposal",
    type: "PDF",
    variables: ["client_name", "project_scope", "timeline", "budget", "deliverables"],
  },
  {
    id: "4",
    name: "Employment Contract",
    type: "DOCX",
    variables: ["employee_name", "position", "salary", "start_date", "benefits"],
  },
];

const clientsData = [
  { id: "1", name: "Acme Corporation" },
  { id: "2", name: "Globex Inc." },
  { id: "3", name: "Wayne Enterprises" },
  { id: "4", name: "Stark Industries" },
  { id: "5", name: "Oscorp Industries" },
];

interface DocumentGenerationFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onGenerate?: (data: any) => void;
}

const DocumentGenerationForm: React.FC<DocumentGenerationFormProps> = ({
  open,
  onOpenChange,
  onGenerate,
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      templateId: "",
      clientId: "",
      variables: {},
    },
  });

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      form.reset();
      setSelectedTemplate(null);
    }
  }, [open, form]);

  // Update variable fields when template changes
  const handleTemplateChange = (templateId: string) => {
    const template = templatesData.find((t) => t.id === templateId);
    setSelectedTemplate(template);

    // Reset variables
    const initialVariables: Record<string, string> = {};
    if (template) {
      template.variables.forEach((variable) => {
        initialVariables[variable] = "";
      });
    }

    form.setValue("variables", initialVariables);
  };

  const onSubmit = async (data: FormValues) => {
    setIsGenerating(true);

    try {
      // In a real app, you would send this data to your API
      console.log("Generating document with data:", data);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      const template = templatesData.find((t) => t.id === data.templateId);
      const client = clientsData.find((c) => c.id === data.clientId);

      // Generate a filename based on template and client
      const timestamp = format(new Date(), "yyyy-MM-dd");
      const filename = `${template?.name.replace(/\s+/g, "_")}_${client?.name.replace(/\s+/g, "_")}_${timestamp}.${template?.type.toLowerCase()}`;

      const generatedDocument = {
        id: `gen-${Date.now()}`,
        name: filename,
        template: template?.name,
        client: client?.name,
        generatedDate: new Date().toISOString(),
        createdBy: "Current User",
        variables: data.variables,
      };

      if (onGenerate) {
        onGenerate(generatedDocument);
      }

      toast.success("Document generated successfully", {
        description: filename,
      });

      onOpenChange(false);
    } catch (error) {
      console.error("Error generating document:", error);
      toast.error("Failed to generate document", {
        description: "Please try again later",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Generate Document</DialogTitle>
          <DialogDescription>
            Fill in the details to generate a new document from a template.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="templateId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Template</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      handleTemplateChange(value);
                    }}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a template" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md">
                      {templatesData.map((template) => (
                        <SelectItem key={template.id} value={template.id} className="cursor-pointer hover:bg-gray-100">
                          {template.name} ({template.type})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="clientId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a client" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md">
                      {clientsData.map((client) => (
                        <SelectItem key={client.id} value={client.id} className="cursor-pointer hover:bg-gray-100">
                          {client.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {selectedTemplate && (
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Template Variables</h3>
                {selectedTemplate.variables.map((variable: string) => (
                  <FormField
                    key={variable}
                    control={form.control}
                    name={`variables.${variable}`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{variable.replace(/_/g, " ")}</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ))}
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isGenerating}>
                {isGenerating ? (
                  <>Generating...</>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    Generate Document
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentGenerationForm;
