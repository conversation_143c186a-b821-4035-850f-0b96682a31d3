package com.redberyl.invoiceapp.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.Arrays;

/**
 * Simplified Swagger configuration with a single primary OpenAPI bean
 */
@Configuration
public class SimplifiedSwaggerConfig {

    /**
     * Primary OpenAPI bean that will be used by Swagger
     */
    @Bean
    @Primary
    public OpenAPI simplifiedOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Redberyl Invoice App API")
                        .description("Complete API documentation for Redberyl Invoice App with all tables and endpoints")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Redberyl")
                                .email("<EMAIL>")
                                .url("https://redberyl.com"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://redberyl.com/license")))
                .servers(Arrays.asList(
                        new Server().url("http://*************:8091").description("Local Development Server"),
                        new Server().url("https://api.redberyl.com").description("Production Server")))
                .components(new Components()
                        .addSecuritySchemes("bearer-jwt", new SecurityScheme()
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")
                                .in(SecurityScheme.In.HEADER)
                                .name("Authorization")))
                .addSecurityItem(new SecurityRequirement().addList("bearer-jwt"));
    }
}
