import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  Divider,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  SimpleGrid,
  useToast,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Spinner,
  Alert,
  AlertIcon,
  Badge,
  Card,
  CardHeader,
  CardBody,
  CardFooter
} from '@chakra-ui/react';
import { AddIcon, EditIcon, DeleteIcon, ViewIcon } from '@chakra-ui/icons';

const BdmManagement = () => {
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [bdms, setBdms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedBdm, setSelectedBdm] = useState(null);
  const [formMode, setFormMode] = useState('add'); // 'add', 'edit', or 'view'
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    gstNumber: '',
    billingAddress: '',
    commissionRate: '',
    notes: ''
  });

  // Fetch all BDMs
  const fetchBdms = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Create basic auth header if needed
      const authHeader = 'Basic ' + btoa('admin:admin123');
      
      const response = await fetch('http://*************:8091/api/v1/bdms?size=100', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': authHeader
        },
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch BDMs: ${response.status}`);
      }
      
      const responseData = await response.json();
      console.log('BDM API response:', responseData);
      
      // Extract BDMs from the nested structure
      let bdmsData = [];
      
      if (responseData && responseData.success && responseData.data && responseData.data.content) {
        bdmsData = responseData.data.content;
      } else if (Array.isArray(responseData)) {
        bdmsData = responseData;
      } else if (responseData && responseData.data && Array.isArray(responseData.data)) {
        bdmsData = responseData.data;
      } else if (responseData && responseData.content && Array.isArray(responseData.content)) {
        bdmsData = responseData.content;
      } else {
        console.error('Unexpected BDM data format:', responseData);
        throw new Error('Unexpected data format from BDM API');
      }
      
      // Sort BDMs by name
      bdmsData.sort((a, b) => a.name.localeCompare(b.name));
      
      setBdms(bdmsData);
    } catch (err) {
      console.error('Error fetching BDMs:', err);
      setError(err.message);
      
      toast({
        title: 'Error fetching BDMs',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch BDMs on component mount
  useEffect(() => {
    fetchBdms();
  }, []);

  // Handle form input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Open modal for adding a new BDM
  const handleAddBdm = () => {
    setFormMode('add');
    setFormData({
      name: '',
      email: '',
      phone: '',
      gstNumber: '',
      billingAddress: '',
      commissionRate: '',
      notes: ''
    });
    onOpen();
  };

  // Open modal for editing a BDM
  const handleEditBdm = (bdm) => {
    setFormMode('edit');
    setSelectedBdm(bdm);
    setFormData({
      name: bdm.name || '',
      email: bdm.email || '',
      phone: bdm.phone || '',
      gstNumber: bdm.gstNumber || '',
      billingAddress: bdm.billingAddress || '',
      commissionRate: bdm.commissionRate || '',
      notes: bdm.notes || ''
    });
    onOpen();
  };

  // Open modal for viewing a BDM
  const handleViewBdm = (bdm) => {
    setFormMode('view');
    setSelectedBdm(bdm);
    setFormData({
      name: bdm.name || '',
      email: bdm.email || '',
      phone: bdm.phone || '',
      gstNumber: bdm.gstNumber || '',
      billingAddress: bdm.billingAddress || '',
      commissionRate: bdm.commissionRate || '',
      notes: bdm.notes || ''
    });
    onOpen();
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      // Validate required fields
      if (!formData.name) {
        toast({
          title: 'Name is required',
          status: 'error',
          duration: 3000,
          isClosable: true
        });
        return;
      }
      
      // Create basic auth header if needed
      const authHeader = 'Basic ' + btoa('admin:admin123');
      
      if (formMode === 'add') {
        // Create a new BDM
        const response = await fetch('http://*************:8091/api/v1/bdms', {
          method: 'POST',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          body: JSON.stringify(formData),
          credentials: 'include'
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Failed to create BDM: ${response.status}`);
        }
        
        toast({
          title: 'BDM created successfully',
          status: 'success',
          duration: 3000,
          isClosable: true
        });
      } else if (formMode === 'edit') {
        // Update an existing BDM
        const response = await fetch(`http://*************:8091/api/v1/bdms/${selectedBdm.id}`, {
          method: 'PUT',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          body: JSON.stringify(formData),
          credentials: 'include'
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Failed to update BDM: ${response.status}`);
        }
        
        toast({
          title: 'BDM updated successfully',
          status: 'success',
          duration: 3000,
          isClosable: true
        });
      }
      
      // Close the modal and refresh the BDM list
      onClose();
      fetchBdms();
    } catch (err) {
      console.error('Error saving BDM:', err);
      
      toast({
        title: 'Error saving BDM',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    }
  };

  // Handle BDM deletion
  const handleDeleteBdm = async (bdm) => {
    if (!window.confirm(`Are you sure you want to delete ${bdm.name}?`)) {
      return;
    }
    
    try {
      // Create basic auth header if needed
      const authHeader = 'Basic ' + btoa('admin:admin123');
      
      const response = await fetch(`http://*************:8091/api/v1/bdms/${bdm.id}`, {
        method: 'DELETE',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': authHeader
        },
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to delete BDM: ${response.status}`);
      }
      
      toast({
        title: 'BDM deleted successfully',
        status: 'success',
        duration: 3000,
        isClosable: true
      });
      
      // Refresh the BDM list
      fetchBdms();
    } catch (err) {
      console.error('Error deleting BDM:', err);
      
      toast({
        title: 'Error deleting BDM',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    }
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <HStack justify="space-between">
          <Box>
            <Heading size="lg">BDM Management</Heading>
            <Text mt={2} color="gray.600">
              Manage Business Development Managers (BDMs) for your clients
            </Text>
          </Box>
          <Button 
            leftIcon={<AddIcon />} 
            colorScheme="blue" 
            onClick={handleAddBdm}
          >
            Add New BDM
          </Button>
        </HStack>
        
        {error && (
          <Alert status="error">
            <AlertIcon />
            <Text>{error}</Text>
          </Alert>
        )}
        
        <Card>
          <CardHeader>
            <Heading size="md">BDM List</Heading>
          </CardHeader>
          <CardBody>
            {loading ? (
              <Box textAlign="center" py={10}>
                <Spinner size="xl" />
                <Text mt={4}>Loading BDMs...</Text>
              </Box>
            ) : bdms.length === 0 ? (
              <Alert status="info">
                <AlertIcon />
                <Text>No BDMs found. Click "Add New BDM" to create one.</Text>
              </Alert>
            ) : (
              <Box overflowX="auto">
                <Table variant="simple">
                  <Thead>
                    <Tr>
                      <Th>Name</Th>
                      <Th>Email</Th>
                      <Th>Phone</Th>
                      <Th>Commission Rate</Th>
                      <Th>Clients</Th>
                      <Th>Actions</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {bdms.map(bdm => (
                      <Tr key={bdm.id}>
                        <Td fontWeight="medium">{bdm.name}</Td>
                        <Td>{bdm.email || '-'}</Td>
                        <Td>{bdm.phone || '-'}</Td>
                        <Td>{bdm.commissionRate ? `${bdm.commissionRate}%` : '-'}</Td>
                        <Td>
                          <Badge colorScheme="green">
                            {bdm.clientCount || 0} clients
                          </Badge>
                        </Td>
                        <Td>
                          <HStack spacing={2}>
                            <IconButton
                              icon={<ViewIcon />}
                              aria-label="View BDM"
                              size="sm"
                              onClick={() => handleViewBdm(bdm)}
                            />
                            <IconButton
                              icon={<EditIcon />}
                              aria-label="Edit BDM"
                              size="sm"
                              colorScheme="blue"
                              onClick={() => handleEditBdm(bdm)}
                            />
                            <IconButton
                              icon={<DeleteIcon />}
                              aria-label="Delete BDM"
                              size="sm"
                              colorScheme="red"
                              onClick={() => handleDeleteBdm(bdm)}
                            />
                          </HStack>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>
            )}
          </CardBody>
        </Card>
      </VStack>
      
      {/* BDM Form Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            {formMode === 'add' ? 'Add New BDM' : 
             formMode === 'edit' ? 'Edit BDM' : 'View BDM'}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                <FormControl isRequired={formMode !== 'view'}>
                  <FormLabel>Name</FormLabel>
                  <Input 
                    name="name" 
                    value={formData.name} 
                    onChange={handleInputChange} 
                    isReadOnly={formMode === 'view'}
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel>Email</FormLabel>
                  <Input 
                    name="email" 
                    type="email" 
                    value={formData.email} 
                    onChange={handleInputChange} 
                    isReadOnly={formMode === 'view'}
                  />
                </FormControl>
              </SimpleGrid>
              
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                <FormControl>
                  <FormLabel>Phone</FormLabel>
                  <Input 
                    name="phone" 
                    value={formData.phone} 
                    onChange={handleInputChange} 
                    isReadOnly={formMode === 'view'}
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel>Commission Rate (%)</FormLabel>
                  <Input 
                    name="commissionRate" 
                    type="number" 
                    step="0.01" 
                    min="0" 
                    max="100"
                    value={formData.commissionRate} 
                    onChange={handleInputChange} 
                    isReadOnly={formMode === 'view'}
                  />
                </FormControl>
              </SimpleGrid>
              
              <FormControl>
                <FormLabel>GST Number</FormLabel>
                <Input 
                  name="gstNumber" 
                  value={formData.gstNumber} 
                  onChange={handleInputChange} 
                  isReadOnly={formMode === 'view'}
                />
              </FormControl>
              
              <FormControl>
                <FormLabel>Billing Address</FormLabel>
                <Textarea 
                  name="billingAddress" 
                  value={formData.billingAddress} 
                  onChange={handleInputChange} 
                  isReadOnly={formMode === 'view'}
                  rows={3}
                />
              </FormControl>
              
              <FormControl>
                <FormLabel>Notes</FormLabel>
                <Textarea 
                  name="notes" 
                  value={formData.notes} 
                  onChange={handleInputChange} 
                  isReadOnly={formMode === 'view'}
                  rows={3}
                />
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              {formMode === 'view' ? 'Close' : 'Cancel'}
            </Button>
            {formMode !== 'view' && (
              <Button colorScheme="blue" onClick={handleSubmit}>
                {formMode === 'add' ? 'Create BDM' : 'Update BDM'}
              </Button>
            )}
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Container>
  );
};

export default BdmManagement;
