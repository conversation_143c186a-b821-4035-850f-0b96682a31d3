import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  Divider,
  Code,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Spinner,
  useToast,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  CardFooter,
  Badge
} from '@chakra-ui/react';

const BdmDuplicationTest = () => {
  const toast = useToast();
  const [client, setClient] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [clientId, setClientId] = useState(1); // Default to client ID 1

  // Fetch client details
  useEffect(() => {
    const fetchClientDetails = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Create basic auth header if needed
        const authHeader = 'Basic ' + btoa('admin:admin123');
        
        const response = await fetch(`http://*************:8091/api/clients/${clientId}`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          credentials: 'include'
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch client details: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Client details fetched:', data);
        setClient(data);
        
        // Check if the client has a complete BDM object
        if (data.bdm) {
          console.log('Client has complete BDM object:', data.bdm);
          
          // Check if bdmId and bdmName are also present as separate properties
          if (data.hasOwnProperty('bdmId') && data.hasOwnProperty('bdmName')) {
            console.warn('Duplication detected: bdmId and bdmName are present alongside bdm object');
          } else {
            console.log('No duplication: bdmId and bdmName are not present as separate properties');
          }
        }
      } catch (err) {
        console.error('Error fetching client details:', err);
        setError(err.message);
        
        toast({
          title: 'Error fetching client details',
          description: err.message,
          status: 'error',
          duration: 5000,
          isClosable: true
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchClientDetails();
  }, [clientId, toast]);

  const handleFetchClient = (id) => {
    setClientId(id);
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box>
          <Heading size="lg">BDM Duplication Test</Heading>
          <Text mt={2} color="gray.600">
            This page tests whether the BDM data duplication issue has been fixed.
          </Text>
        </Box>
        
        <HStack spacing={4}>
          <Button 
            colorScheme="blue" 
            onClick={() => handleFetchClient(1)}
            isDisabled={clientId === 1}
          >
            Fetch Client #1
          </Button>
          <Button 
            colorScheme="blue" 
            onClick={() => handleFetchClient(2)}
            isDisabled={clientId === 2}
          >
            Fetch Client #2
          </Button>
          <Button 
            colorScheme="blue" 
            onClick={() => handleFetchClient(3)}
            isDisabled={clientId === 3}
          >
            Fetch Client #3
          </Button>
        </HStack>
        
        {error && (
          <Alert status="error">
            <AlertIcon />
            <AlertTitle mr={2}>Error!</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {loading ? (
          <Box textAlign="center" py={10}>
            <Spinner size="xl" />
            <Text mt={4}>Loading client data...</Text>
          </Box>
        ) : client ? (
          <Card>
            <CardHeader>
              <Heading size="md">Client Details: {client.name}</Heading>
            </CardHeader>
            <CardBody>
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                <Box>
                  <Heading size="sm" mb={3}>Client Information</Heading>
                  <VStack align="stretch" spacing={2}>
                    <HStack>
                      <Text fontWeight="bold" width="150px">ID:</Text>
                      <Text>{client.id}</Text>
                    </HStack>
                    <HStack>
                      <Text fontWeight="bold" width="150px">Name:</Text>
                      <Text>{client.name}</Text>
                    </HStack>
                    <HStack>
                      <Text fontWeight="bold" width="150px">BDM ID:</Text>
                      <Text>{client.bdm ? client.bdm.id : 'None'}</Text>
                    </HStack>
                    <HStack>
                      <Text fontWeight="bold" width="150px">BDM Name:</Text>
                      <Text>{client.bdm ? client.bdm.name : 'None'}</Text>
                    </HStack>
                  </VStack>
                </Box>
                
                <Box>
                  <Heading size="sm" mb={3}>BDM Information</Heading>
                  {client.bdm ? (
                    <VStack align="stretch" spacing={2}>
                      <HStack>
                        <Text fontWeight="bold" width="150px">ID:</Text>
                        <Text>{client.bdm.id}</Text>
                      </HStack>
                      <HStack>
                        <Text fontWeight="bold" width="150px">Name:</Text>
                        <Text>{client.bdm.name}</Text>
                      </HStack>
                      <HStack>
                        <Text fontWeight="bold" width="150px">Email:</Text>
                        <Text>{client.bdm.email || 'N/A'}</Text>
                      </HStack>
                      <HStack>
                        <Text fontWeight="bold" width="150px">Phone:</Text>
                        <Text>{client.bdm.phone || 'N/A'}</Text>
                      </HStack>
                    </VStack>
                  ) : (
                    <Alert status="info">
                      <AlertIcon />
                      <AlertTitle>No BDM Assigned</AlertTitle>
                      <AlertDescription>
                        This client does not have a BDM assigned.
                      </AlertDescription>
                    </Alert>
                  )}
                </Box>
              </SimpleGrid>
              
              <Divider my={6} />
              
              <Box>
                <Heading size="sm" mb={3}>JSON Response Analysis</Heading>
                <Alert 
                  status={client.hasOwnProperty('bdmId') && client.hasOwnProperty('bdmName') && client.bdm ? "warning" : "success"}
                  mb={4}
                >
                  <AlertIcon />
                  <Box>
                    <AlertTitle>
                      {client.hasOwnProperty('bdmId') && client.hasOwnProperty('bdmName') && client.bdm 
                        ? "Duplication Detected" 
                        : "No Duplication Detected"}
                    </AlertTitle>
                    <AlertDescription>
                      {client.hasOwnProperty('bdmId') && client.hasOwnProperty('bdmName') && client.bdm 
                        ? "The response contains both bdmId/bdmName properties and a complete bdm object." 
                        : "The response correctly contains only the bdm object without duplicate properties."}
                    </AlertDescription>
                  </Box>
                </Alert>
                
                <Box overflowX="auto" maxH="300px" overflowY="auto">
                  <Code p={2} display="block" whiteSpace="pre">
                    {JSON.stringify(client, null, 2)}
                  </Code>
                </Box>
              </Box>
            </CardBody>
          </Card>
        ) : (
          <Alert status="info">
            <AlertIcon />
            <AlertTitle>No Client Data</AlertTitle>
            <AlertDescription>
              No client data has been loaded yet.
            </AlertDescription>
          </Alert>
        )}
      </VStack>
    </Container>
  );
};

export default BdmDuplicationTest;
