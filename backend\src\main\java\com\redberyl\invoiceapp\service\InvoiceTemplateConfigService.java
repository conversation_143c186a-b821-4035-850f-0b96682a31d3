package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.InvoiceTemplateConfigDto;

import java.util.List;
import java.util.Map;

/**
 * Service interface for invoice template configuration
 */
public interface InvoiceTemplateConfigService {
    
    /**
     * Get all configurations
     */
    List<InvoiceTemplateConfigDto> getAllConfigurations();
    
    /**
     * Get configuration by ID
     */
    InvoiceTemplateConfigDto getConfigurationById(Long id);
    
    /**
     * Get configuration by key
     */
    InvoiceTemplateConfigDto getConfigurationByKey(String configKey);
    
    /**
     * Get configurations by category
     */
    List<InvoiceTemplateConfigDto> getConfigurationsByCategory(String category);
    
    /**
     * Get configuration value by key
     */
    String getConfigValue(String configKey);
    
    /**
     * Get configuration value by key with default
     */
    String getConfigValue(String configKey, String defaultValue);
    
    /**
     * Get all configuration values as a map
     */
    Map<String, String> getAllConfigValues();
    
    /**
     * Get configuration values by category as a map
     */
    Map<String, String> getConfigValuesByCategory(String category);
    
    /**
     * Create new configuration
     */
    InvoiceTemplateConfigDto createConfiguration(InvoiceTemplateConfigDto configDto);
    
    /**
     * Update existing configuration
     */
    InvoiceTemplateConfigDto updateConfiguration(Long id, InvoiceTemplateConfigDto configDto);
    
    /**
     * Delete configuration
     */
    void deleteConfiguration(Long id);
    
    /**
     * Initialize default configurations
     */
    void initializeDefaultConfigurations();
    
    /**
     * Bulk update configurations
     */
    List<InvoiceTemplateConfigDto> bulkUpdateConfigurations(List<InvoiceTemplateConfigDto> configDtos);
}
