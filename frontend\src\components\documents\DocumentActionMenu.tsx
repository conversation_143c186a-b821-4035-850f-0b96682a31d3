import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  Download,
  Edit,
  FileClock,
  Trash,
  MoreHorizontal,
  Eye,
} from "lucide-react";
import { toast } from "sonner";

interface DocumentActionMenuProps {
  document: any;
  onDownload?: (document: any) => void;
  onEdit?: (document: any) => void;
  onViewHistory?: (document: any) => void;
  onDelete?: (documentId: string) => void;
  showInPopover?: boolean;
}

const DocumentActionMenu: React.FC<DocumentActionMenuProps> = ({
  document,
  onDownload,
  onEdit,
  onViewHistory,
  onDelete,
  showInPopover = false,
}) => {
  const handleDownload = () => {
    if (onDownload) {
      onDownload(document);
    } else {
      toast.success(`Downloading ${document.name}`, {
        description: `File type: ${document.type}`,
      });
    }
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(document);
    } else {
      toast.info(`Editing ${document.name}`, {
        description: "Edit functionality would open a form in a real app",
      });
    }
  };

  const handleViewHistory = () => {
    if (onViewHistory) {
      onViewHistory(document);
    } else {
      toast.info(`Viewing version history for ${document.name}`, {
        description: "Version history would be displayed in a real app",
      });
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(document.id);
    } else {
      toast.success(`Deleted ${document.name}`, {
        description: "Document deleted successfully",
      });
    }
  };

  if (showInPopover) {
    return (
      <div className="absolute right-2 top-2 bg-white shadow-md rounded-md border p-2 flex flex-col gap-2 z-10">
        <Button
          variant="ghost"
          size="sm"
          className="justify-start px-2 py-1 h-auto"
          onClick={handleDownload}
        >
          <Download className="mr-2 h-4 w-4" />
          Download
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="justify-start px-2 py-1 h-auto"
          onClick={handleEdit}
        >
          <Edit className="mr-2 h-4 w-4" />
          Edit Variables
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="justify-start px-2 py-1 h-auto"
          onClick={handleViewHistory}
        >
          <FileClock className="mr-2 h-4 w-4" />
          Version History
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="justify-start px-2 py-1 h-auto text-red-600"
          onClick={handleDelete}
        >
          <Trash className="mr-2 h-4 w-4" />
          Delete
        </Button>
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem onClick={handleDownload} className="cursor-pointer">
          <Download className="mr-2 h-4 w-4" /> Download
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleEdit} className="cursor-pointer">
          <Edit className="mr-2 h-4 w-4" /> Edit Variables
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleViewHistory} className="cursor-pointer">
          <FileClock className="mr-2 h-4 w-4" /> Version History
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={handleDelete}
          className="text-red-600 cursor-pointer"
        >
          <Trash className="mr-2 h-4 w-4" /> Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default DocumentActionMenu;
