package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.InvoiceTaxDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.InvoiceTaxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Tag(name = "Invoice Tax", description = "Invoice Tax management API")
public class InvoiceTaxController {

    @Autowired
    private InvoiceTaxService invoiceTaxService;

    @GetMapping("/invoice-taxes/getAll")
    @Operation(summary = "Get all invoice taxes", description = "Get all invoice taxes")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice taxes found"),
            @ApiResponse(responseCode = "204", description = "No invoice taxes found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceTaxDto>> getAllInvoiceTaxes() {
        try {
            List<InvoiceTaxDto> invoiceTaxes = invoiceTaxService.getAllInvoiceTaxes();
            return new ResponseEntity<>(invoiceTaxes, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/invoice-taxes/getById/{id}")
    @Operation(summary = "Get invoice tax by ID", description = "Get invoice tax by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice tax found"),
            @ApiResponse(responseCode = "404", description = "Invoice tax not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceTaxDto> getInvoiceTaxById(@PathVariable Long id) {
        InvoiceTaxDto invoiceTax = invoiceTaxService.getInvoiceTaxById(id);
        return new ResponseEntity<>(invoiceTax, HttpStatus.OK);
    }

    @GetMapping("/invoice-taxes/getByInvoiceId/{invoiceId}")
    @Operation(summary = "Get invoice taxes by invoice ID", description = "Get invoice taxes by invoice ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice taxes found"),
            @ApiResponse(responseCode = "204", description = "No invoice taxes found for this invoice"),
            @ApiResponse(responseCode = "404", description = "Invoice not found"),
            @ApiResponse(responseCode = "400", description = "Invalid invoice ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceTaxDto>> getInvoiceTaxesByInvoiceId(@PathVariable Long invoiceId) {
        try {
            List<InvoiceTaxDto> invoiceTaxes = invoiceTaxService.getInvoiceTaxesByInvoiceId(invoiceId);
            return new ResponseEntity<>(invoiceTaxes, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/invoice-taxes/getByTaxRateId/{taxRateId}")
    @Operation(summary = "Get invoice taxes by tax rate ID", description = "Get invoice taxes by tax rate ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice taxes found"),
            @ApiResponse(responseCode = "204", description = "No invoice taxes found for this tax rate"),
            @ApiResponse(responseCode = "404", description = "Tax rate not found"),
            @ApiResponse(responseCode = "400", description = "Invalid tax rate ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceTaxDto>> getInvoiceTaxesByTaxRateId(@PathVariable Long taxRateId) {
        try {
            List<InvoiceTaxDto> invoiceTaxes = invoiceTaxService.getInvoiceTaxesByTaxRateId(taxRateId);
            return new ResponseEntity<>(invoiceTaxes, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/invoice-taxes/create")
    @Operation(summary = "Create invoice tax", description = "Create invoice tax")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Invoice tax created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceTaxDto> createInvoiceTax(@Valid @RequestBody InvoiceTaxDto invoiceTaxDto) {
        InvoiceTaxDto createdInvoiceTax = invoiceTaxService.createInvoiceTax(invoiceTaxDto);
        return new ResponseEntity<>(createdInvoiceTax, HttpStatus.CREATED);
    }

    @PutMapping("/invoice-taxes/update/{id}")
    @Operation(summary = "Update invoice tax", description = "Update invoice tax")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice tax updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "404", description = "Invoice tax not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceTaxDto> updateInvoiceTax(@PathVariable Long id,
            @Valid @RequestBody InvoiceTaxDto invoiceTaxDto) {
        InvoiceTaxDto updatedInvoiceTax = invoiceTaxService.updateInvoiceTax(id, invoiceTaxDto);
        return new ResponseEntity<>(updatedInvoiceTax, HttpStatus.OK);
    }

    @DeleteMapping("/invoice-taxes/deleteById/{id}")
    @Operation(summary = "Delete invoice tax", description = "Delete invoice tax")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Invoice tax deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied or invoice tax is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "Invoice tax not found")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteInvoiceTax(@PathVariable Long id) {
        invoiceTaxService.deleteInvoiceTax(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
