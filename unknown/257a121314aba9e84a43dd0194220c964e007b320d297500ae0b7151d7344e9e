package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.CandidateDto;
import com.redberyl.invoiceapp.service.CandidateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/candidates")
@Tag(name = "Candidate", description = "Candidate management API")
public class CandidateController {

    @Autowired
    private CandidateService candidateService;

    @GetMapping
    @Operation(summary = "Get all candidates", description = "Retrieve a list of all candidates")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<CandidateDto>> getAllCandidates() {
        List<CandidateDto> candidates = candidateService.getAllCandidates();
        return new ResponseEntity<>(candidates, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get candidate by ID", description = "Retrieve a candidate by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<CandidateDto> getCandidateById(@PathVariable Long id) {
        CandidateDto candidate = candidateService.getCandidateById(id);
        return new ResponseEntity<>(candidate, HttpStatus.OK);
    }

    @GetMapping("/client/{clientId}")
    @Operation(summary = "Get candidates by client ID", description = "Retrieve all candidates for a specific client")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<CandidateDto>> getCandidatesByClientId(@PathVariable Long clientId) {
        List<CandidateDto> candidates = candidateService.getCandidatesByClientId(clientId);
        return new ResponseEntity<>(candidates, HttpStatus.OK);
    }

    @GetMapping("/project/{projectId}")
    @Operation(summary = "Get candidates by project ID", description = "Retrieve all candidates for a specific project")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<CandidateDto>> getCandidatesByProjectId(@PathVariable Long projectId) {
        List<CandidateDto> candidates = candidateService.getCandidatesByProjectId(projectId);
        return new ResponseEntity<>(candidates, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create candidate", description = "Create a new candidate")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<CandidateDto> createCandidate(@Valid @RequestBody CandidateDto candidateDto) {
        CandidateDto createdCandidate = candidateService.createCandidate(candidateDto);
        return new ResponseEntity<>(createdCandidate, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update candidate", description = "Update an existing candidate")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<CandidateDto> updateCandidate(@PathVariable Long id, @Valid @RequestBody CandidateDto candidateDto) {
        CandidateDto updatedCandidate = candidateService.updateCandidate(id, candidateDto);
        return new ResponseEntity<>(updatedCandidate, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete candidate", description = "Delete a candidate by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteCandidate(@PathVariable Long id) {
        candidateService.deleteCandidate(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
