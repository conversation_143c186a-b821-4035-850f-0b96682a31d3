package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.ApiResponseDto;
import com.redberyl.invoiceapp.dto.EntityRelationshipDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * Controller to provide information about entity relationships
 */
@RestController
@RequestMapping("/api/entity-relationships")
@Tag(name = "Entity Relationships", description = "API to get information about entity relationships")
public class EntityRelationshipController {

    @Autowired
    private DataSource dataSource;

    /**
     * Get all entity relationships
     */
    @GetMapping
    @Operation(summary = "Get all entity relationships", description = "Get information about all entity relationships in the database")
    public ResponseEntity<ApiResponseDto<List<EntityRelationshipDto>>> getAllEntityRelationships() {
        try {
            List<EntityRelationshipDto> relationships = new ArrayList<>();
            
            try (Connection connection = dataSource.getConnection()) {
                DatabaseMetaData metaData = connection.getMetaData();
                
                // Get all tables
                ResultSet tablesRs = metaData.getTables(null, "PUBLIC", null, new String[]{"TABLE"});
                
                while (tablesRs.next()) {
                    String tableName = tablesRs.getString("TABLE_NAME");
                    
                    // Skip system tables
                    if (tableName.startsWith("DATABASECHANGELOG") || 
                        tableName.startsWith("FLYWAY_") ||
                        tableName.equals("DUAL")) {
                        continue;
                    }
                    
                    // Get foreign keys for this table
                    ResultSet fkRs = metaData.getImportedKeys(null, "PUBLIC", tableName);
                    
                    while (fkRs.next()) {
                        EntityRelationshipDto relationship = new EntityRelationshipDto();
                        relationship.setSourceTable(tableName);
                        relationship.setSourceColumn(fkRs.getString("FKCOLUMN_NAME"));
                        relationship.setTargetTable(fkRs.getString("PKTABLE_NAME"));
                        relationship.setTargetColumn(fkRs.getString("PKCOLUMN_NAME"));
                        relationship.setRelationshipType("MANY_TO_ONE"); // Default assumption
                        
                        relationships.add(relationship);
                    }
                }
            }
            
            ApiResponseDto<List<EntityRelationshipDto>> response = new ApiResponseDto<>();
            response.setSuccess(true);
            response.setMessage("Entity relationships retrieved successfully");
            response.setData(relationships);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            ApiResponseDto<List<EntityRelationshipDto>> response = new ApiResponseDto<>();
            response.setSuccess(false);
            response.setMessage("Error retrieving entity relationships: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
