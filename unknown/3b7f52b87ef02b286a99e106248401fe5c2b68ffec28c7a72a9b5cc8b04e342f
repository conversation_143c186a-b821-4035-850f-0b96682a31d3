package com.redberyl.invoiceapp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for entity relationship information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Entity relationship information")
public class EntityRelationshipDto {
    
    @Schema(description = "Source table")
    private String sourceTable;
    
    @Schema(description = "Source column")
    private String sourceColumn;
    
    @Schema(description = "Target table")
    private String targetTable;
    
    @Schema(description = "Target column")
    private String targetColumn;
    
    @Schema(description = "Relationship type (ONE_TO_ONE, ONE_TO_MANY, MANY_TO_ONE, MANY_TO_MANY)")
    private String relationshipType;
}
