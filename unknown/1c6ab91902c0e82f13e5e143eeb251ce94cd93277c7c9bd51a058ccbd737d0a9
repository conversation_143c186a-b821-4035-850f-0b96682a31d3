package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.SpocDto;
import com.redberyl.invoiceapp.service.SpocService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/spocs")
@Tag(name = "SPOC", description = "SPOC management API")
public class SpocController {

    @Autowired
    private SpocService spocService;

    @GetMapping
    @Operation(summary = "Get all SPOCs", description = "Retrieve a list of all SPOCs")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<SpocDto>> getAllSpocs() {
        List<SpocDto> spocs = spocService.getAllSpocs();
        return new ResponseEntity<>(spocs, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get SPOC by ID", description = "Retrieve a SPOC by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<SpocDto> getSpocById(@PathVariable Long id) {
        SpocDto spoc = spocService.getSpocById(id);
        return new ResponseEntity<>(spoc, HttpStatus.OK);
    }

    @GetMapping("/email/{emailId}")
    @Operation(summary = "Get SPOC by email", description = "Retrieve a SPOC by its email ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<SpocDto> getSpocByEmailId(@PathVariable String emailId) {
        SpocDto spoc = spocService.getSpocByEmailId(emailId);
        return new ResponseEntity<>(spoc, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create SPOC", description = "Create a new SPOC")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<SpocDto> createSpoc(@Valid @RequestBody SpocDto spocDto) {
        SpocDto createdSpoc = spocService.createSpoc(spocDto);
        return new ResponseEntity<>(createdSpoc, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update SPOC", description = "Update an existing SPOC")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<SpocDto> updateSpoc(@PathVariable Long id, @Valid @RequestBody SpocDto spocDto) {
        SpocDto updatedSpoc = spocService.updateSpoc(id, spocDto);
        return new ResponseEntity<>(updatedSpoc, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete SPOC", description = "Delete a SPOC by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteSpoc(@PathVariable Long id) {
        spocService.deleteSpoc(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
