import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface SelectOption {
  value: string | number;
  label: string;
  description?: string;
}

interface CustomSelectProps {
  options: SelectOption[];
  value: string | number | null;
  onChange: (value: string | number) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  error?: boolean;
  id?: string;
  name?: string;
}

export const CustomSelect: React.FC<CustomSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = 'Select an option',
  className = '',
  disabled = false,
  error = false,
  id,
  name,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedLabel, setSelectedLabel] = useState<string>('');
  const selectRef = useRef<HTMLDivElement>(null);

  // Update the selected label when value changes
  useEffect(() => {
    if (value !== null && value !== undefined && value !== '') {
      // First try exact match
      let option = options.find(opt =>
        opt.value === value || opt.value.toString() === value.toString()
      );

      // If no exact match, try case-insensitive match for string values
      if (!option && typeof value === 'string' && value.trim() !== '') {
        option = options.find(opt =>
          typeof opt.value === 'string' &&
          opt.value.toLowerCase() === value.toLowerCase()
        );
      }

      // If still no match, try matching by label (useful when we have the name but not the ID)
      if (!option && typeof value === 'string' && value.trim() !== '') {
        option = options.find(opt =>
          opt.label.toLowerCase() === value.toLowerCase()
        );
      }

      // If still no match and value is a string, use it as the label
      if (!option && typeof value === 'string' && value.trim() !== '') {
        // Just use the value as the label temporarily
        setSelectedLabel(value);
        console.log(`CustomSelect: Using value as label: ${value}`);
        return;
      }

      if (option) {
        setSelectedLabel(option.label);
        console.log(`CustomSelect: Found option for value ${value}:`, option);

        // If we found a match but the value is different, update the form value
        if (option.value.toString() !== value.toString()) {
          console.log(`CustomSelect: Updating value from ${value} to ${option.value}`);
          onChange(option.value);
        }
      } else {
        console.warn(`CustomSelect: No option found for value ${value} in options:`, options);
        // Keep the previous label if no option is found
        // This prevents the field from appearing empty when options are still loading
      }
    } else {
      setSelectedLabel('');
    }
  }, [value, options, onChange]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleSelect = (option: SelectOption) => {
    onChange(option.value);
    setSelectedLabel(option.label);
    setIsOpen(false);
  };

  return (
    <div
      ref={selectRef}
      className={cn(
        'relative w-full',
        className
      )}
    >
      <div
        className={cn(
          'flex h-9 w-full items-center justify-between rounded-md border bg-background px-3 py-2 text-sm ring-offset-background',
          'focus-within:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2',
          disabled && 'cursor-not-allowed opacity-50',
          error && 'border-red-500',
          !disabled && 'cursor-pointer hover:border-primary',
          className
        )}
        onClick={handleToggle}
        tabIndex={0}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-controls={`select-dropdown-${id || name}`}
        id={id}
        data-name={name}
      >
        <span className={cn('truncate', !selectedLabel && 'text-muted-foreground')}>
          {selectedLabel || placeholder}
        </span>
        <ChevronDown className={cn('h-4 w-4 opacity-50 transition-transform', isOpen && 'rotate-180')} />
      </div>

      {isOpen && (
        <div
          className="absolute z-[9999] mt-1 max-h-60 w-full overflow-auto rounded-md border bg-white p-1 shadow-lg"
          id={`select-dropdown-${id || name}`}
          role="listbox"
        >
          {options.length === 0 ? (
            <div className="px-2 py-4 text-center text-sm text-muted-foreground">
              No options available
            </div>
          ) : (
            options.map((option) => (
              <div
                key={option.value}
                className={cn(
                  'flex cursor-pointer select-none items-center rounded-sm px-2 py-2 text-sm outline-none',
                  'hover:bg-accent hover:text-accent-foreground',
                  'focus:bg-accent focus:text-accent-foreground',
                  (value !== null && (option.value === value || option.value.toString() === value.toString())) && 'bg-accent/50'
                )}
                onClick={() => handleSelect(option)}
                role="option"
                aria-selected={value !== null && (option.value === value || option.value.toString() === value.toString())}
              >
                <div className="flex-1">
                  <div className="font-medium">{option.label}</div>
                  {option.description && (
                    <div className="text-xs text-muted-foreground">{option.description}</div>
                  )}
                </div>
                {value !== null && (option.value === value || option.value.toString() === value.toString()) && (
                  <Check className="h-4 w-4 ml-2" />
                )}
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};
