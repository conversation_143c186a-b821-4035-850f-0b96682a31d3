import { useState, useEffect } from "react";
import { Search, Download, Plus, Edit, Trash } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";

interface EntityTabProps {
  title: string;
  endpoint: string;
  entityType: string;
}

const EntityTab = ({ title, endpoint, entityType }: EntityTabProps) => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 10;
  const { toast } = useToast();

  // Define columns based on entity type
  const getColumns = () => {
    switch (entityType) {
      case "clients":
        return [
          { key: "id", label: "ID" },
          { key: "name", label: "Name" },
          { key: "createdAt", label: "Created At" },
        ];
      case "projects":
        return [
          { key: "id", label: "ID" },
          { key: "name", label: "Name" },
          { key: "client.name", label: "Client" },
          { key: "createdAt", label: "Created At" },
        ];
      case "candidates":
        return [
          { key: "id", label: "ID" },
          { key: "name", label: "Name" },
          { key: "designation", label: "Designation" },
          { key: "createdAt", label: "Created At" },
        ];
      case "spocs":
        return [
          { key: "id", label: "ID" },
          { key: "name", label: "Name" },
          { key: "emailId", label: "Email" },
          { key: "contactNo", label: "Contact" },
        ];
      case "bdms":
        return [
          { key: "id", label: "ID" },
          { key: "name", label: "Name" },
          { key: "email", label: "Email" },
          { key: "phone", label: "Phone" },
        ];
      case "staffing_types":
        return [
          { key: "id", label: "ID" },
          { key: "name", label: "Name" },
        ];
      case "invoice_types":
        return [
          { key: "id", label: "ID" },
          { key: "invoiceType", label: "Invoice Type" },
          { key: "typeDesc", label: "Description" },
        ];
      case "hsn_codes":
        return [
          { key: "id", label: "ID" },
          { key: "code", label: "Code" },
          { key: "description", label: "Description" },
          { key: "gstRate", label: "GST Rate" },
        ];
      case "tax_types":
        return [
          { key: "id", label: "ID" },
          { key: "taxType", label: "Tax Type" },
          { key: "taxTypeDescription", label: "Description" },
        ];
      case "tax_rates":
        return [
          { key: "id", label: "ID" },
          { key: "taxType.taxType", label: "Tax Type" },
          { key: "rate", label: "Rate" },
          { key: "effectiveFrom", label: "Effective From" },
          { key: "effectiveTo", label: "Effective To" },
        ];
      case "redberyl_accounts":
        return [
          { key: "id", label: "ID" },
          { key: "accountName", label: "Account Name" },
          { key: "accountNo", label: "Account No" },
          { key: "bankName", label: "Bank Name" },
        ];
      case "leads":
        return [
          { key: "id", label: "ID" },
          { key: "name", label: "Name" },
          { key: "email", label: "Email" },
          { key: "phone", label: "Phone" },
          { key: "status", label: "Status" },
        ];
      case "deals":
        return [
          { key: "id", label: "ID" },
          { key: "projectName", label: "Project Name" },
          { key: "lead.name", label: "Lead" },
          { key: "client.name", label: "Client" },
          { key: "status", label: "Status" },
        ];
      case "communications":
        return [
          { key: "id", label: "ID" },
          { key: "subject", label: "Subject" },
          { key: "method", label: "Method" },
          { key: "loggedAt", label: "Logged At" },
        ];
      case "document_templates":
        return [
          { key: "id", label: "ID" },
          { key: "name", label: "Name" },
          { key: "templateType", label: "Template Type" },
        ];
      case "document_template_versions":
        return [
          { key: "id", label: "ID" },
          { key: "template.name", label: "Template" },
          { key: "versionNumber", label: "Version" },
          { key: "isActive", label: "Active" },
        ];
      case "document_variables":
        return [
          { key: "id", label: "ID" },
          { key: "variableName", label: "Variable Name" },
          { key: "description", label: "Description" },
        ];
      default:
        return [
          { key: "id", label: "ID" },
          { key: "name", label: "Name" },
        ];
    }
  };

  const columns = getColumns();

  // Function to get nested property value
  const getNestedValue = (obj: any, path: string) => {
    return path.split('.').reduce((prev, curr) => {
      return prev && prev[curr] ? prev[curr] : null;
    }, obj);
  };

  // Fetch data from API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const response = await fetch(endpoint);
        if (!response.ok) {
          throw new Error(`Error fetching ${title}: ${response.statusText}`);
        }
        const result = await response.json();
        setData(result);
        setTotalPages(Math.ceil(result.length / itemsPerPage));
        setError(null);
      } catch (err: any) {
        setError(err.message);
        toast({
          title: "Error",
          description: `Failed to load ${title}: ${err.message}`,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [endpoint, title, toast]);

  // Filter data based on search term
  const filteredData = data.filter((item) => {
    return columns.some((column) => {
      const value = getNestedValue(item, column.key);
      return value && String(value).toLowerCase().includes(search.toLowerCase());
    });
  });

  // Paginate data
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Format date values
  const formatValue = (value: any) => {
    if (value === null || value === undefined) return "-";
    
    // Check if it's a date string
    if (typeof value === 'string' && (value.includes('T') || value.match(/^\d{4}-\d{2}-\d{2}$/))) {
      try {
        return new Date(value).toLocaleDateString();
      } catch (e) {
        return value;
      }
    }
    
    // Format boolean values
    if (typeof value === 'boolean') {
      return value ? 'Yes' : 'No';
    }
    
    return String(value);
  };

  return (
    <div>
      <div className="mb-6 flex items-center gap-4">
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <Input
              placeholder="Search..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="max-w-xs"
            />
            <Button variant="outline" size="icon">
              <Search className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Download CSV
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <Skeleton key={i} className="h-12 w-full" />
          ))}
        </div>
      ) : error ? (
        <div className="p-4 text-center text-red-500">
          {error}
        </div>
      ) : (
        <>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-20">Action</TableHead>
                  {columns.map((column) => (
                    <TableHead key={column.key}>{column.label}</TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={columns.length + 1} className="text-center">
                      No data available
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedData.map((row) => (
                    <TableRow key={row.id}>
                      <TableCell>
                        <div className="flex space-x-1">
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                      {columns.map((column) => (
                        <TableCell key={column.key}>
                          {formatValue(getNestedValue(row, column.key))}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {totalPages > 1 && (
            <div className="mt-4 flex justify-end">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious 
                      onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                  
                  {[...Array(Math.min(5, totalPages))].map((_, i) => {
                    const pageNumber = i + 1;
                    return (
                      <PaginationItem key={i}>
                        <PaginationLink
                          onClick={() => setCurrentPage(pageNumber)}
                          isActive={currentPage === pageNumber}
                        >
                          {pageNumber}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}
                  
                  {totalPages > 5 && (
                    <>
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                      <PaginationItem>
                        <PaginationLink
                          onClick={() => setCurrentPage(totalPages)}
                          isActive={currentPage === totalPages}
                        >
                          {totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    </>
                  )}
                  
                  <PaginationItem>
                    <PaginationNext 
                      onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                      className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default EntityTab;
