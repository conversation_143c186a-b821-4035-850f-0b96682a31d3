import React, { useState } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  <PERSON>vider,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  useToast,
  Card,
  CardHeader,
  CardBody,
  CardFooter,
  Alert,
  AlertIcon,
  SimpleGrid
} from '@chakra-ui/react';
import BdmDropdown from '../BdmDropdown';
import BdmDebugger from './BdmDebugger';

const BdmFixTest = () => {
  const toast = useToast();
  const [selectedBdmId, setSelectedBdmId] = useState(null);

  // Handle BDM selection
  const handleBdmChange = (bdmId) => {
    console.log('Selected BDM ID:', bdmId);
    setSelectedBdmId(bdmId);
    
    toast({
      title: 'BDM Selected',
      description: `You selected BDM with ID: ${bdmId}`,
      status: 'info',
      duration: 3060,
      isClosable: true
    });
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box>
          <Heading size="lg">BDM Dropdown Fix Test</Heading>
          <Text mt={2} color="gray.600">
            This page tests the fixed BDM dropdown component to ensure it correctly displays all BDM names from the database.
          </Text>
        </Box>
        
        <Tabs variant="enclosed">
          <TabList>
            <Tab>Dropdown Test</Tab>
            <Tab>BDM Debugger</Tab>
          </TabList>
          <TabPanels>
            <TabPanel>
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                <Card>
                  <CardHeader>
                    <Heading size="md">Fixed BDM Dropdown</Heading>
                  </CardHeader>
                  <CardBody>
                    <VStack spacing={4} align="stretch">
                      <Text>
                        The dropdown below should now correctly display all BDM names from the database.
                        Select a BDM to see its ID.
                      </Text>
                      
                      <BdmDropdown 
                        value={selectedBdmId} 
                        onChange={handleBdmChange}
                        required={true}
                        label="Select a BDM"
                      />
                      
                      {selectedBdmId && (
                        <Alert status="success" mt={4}>
                          <AlertIcon />
                          <Text>Selected BDM ID: <strong>{selectedBdmId}</strong></Text>
                        </Alert>
                      )}
                    </VStack>
                  </CardBody>
                </Card>
                
                <Card>
                  <CardHeader>
                    <Heading size="md">What Was Fixed</Heading>
                  </CardHeader>
                  <CardBody>
                    <VStack spacing={4} align="stretch">
                      <Text fontWeight="bold">The following issues were fixed:</Text>
                      
                      <Box bg="blue.50" p={3} borderRadius="md">
                        <Text fontWeight="bold">1. Improved Data Extraction</Text>
                        <Text>
                          Enhanced the logic to extract BDM data from various API response formats,
                          with better error handling and validation.
                        </Text>
                      </Box>
                      
                      <Box bg="blue.50" p={3} borderRadius="md">
                        <Text fontWeight="bold">2. Ensured Valid BDM Objects</Text>
                        <Text>
                          Added validation to ensure all BDM objects have at least id and name properties,
                          with fallbacks for missing values.
                        </Text>
                      </Box>
                      
                      <Box bg="blue.50" p={3} borderRadius="md">
                        <Text fontWeight="bold">3. Improved Rendering</Text>
                        <Text>
                          Updated the dropdown rendering to handle cases where BDM name might be missing,
                          using a fallback display format.
                        </Text>
                      </Box>
                      
                      <Alert status="info">
                        <AlertIcon />
                        <Text>
                          The dropdown now works reliably even with inconsistent data formats from the API.
                        </Text>
                      </Alert>
                    </VStack>
                  </CardBody>
                </Card>
              </SimpleGrid>
            </TabPanel>
            <TabPanel>
              <BdmDebugger />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </VStack>
    </Container>
  );
};

export default BdmFixTest;
