package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.CommunicationDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.CommunicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Tag(name = "Communications", description = "API for managing communications")
public class CommunicationController {

    @Autowired
    private CommunicationService communicationService;

    @GetMapping("/communications/getAll")
    @Operation(summary = "Get all communications")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Communications found"),
            @ApiResponse(responseCode = "204", description = "No communications found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<CommunicationDto>> getAllCommunications() {
        try {
            List<CommunicationDto> communications = communicationService.getAllCommunications();
            return ResponseEntity.ok(communications);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/communications/getById/{id}")
    @Operation(summary = "Get communication by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Communication found"),
            @ApiResponse(responseCode = "404", description = "Communication not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<CommunicationDto> getCommunicationById(@PathVariable Long id) {
        return ResponseEntity.ok(communicationService.getCommunicationById(id));
    }

    @GetMapping("/communications/getByClientId/{clientId}")
    @Operation(summary = "Get communications by client ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Communications found"),
            @ApiResponse(responseCode = "204", description = "No communications found for this client"),
            @ApiResponse(responseCode = "404", description = "Client not found"),
            @ApiResponse(responseCode = "400", description = "Invalid client ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<CommunicationDto>> getCommunicationsByClientId(@PathVariable Long clientId) {
        try {
            List<CommunicationDto> communications = communicationService.getCommunicationsByClientId(clientId);
            return ResponseEntity.ok(communications);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/communications/getByLeadId/{leadId}")
    @Operation(summary = "Get communications by lead ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Communications found"),
            @ApiResponse(responseCode = "204", description = "No communications found for this lead"),
            @ApiResponse(responseCode = "404", description = "Lead not found"),
            @ApiResponse(responseCode = "400", description = "Invalid lead ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<CommunicationDto>> getCommunicationsByLeadId(@PathVariable Long leadId) {
        try {
            List<CommunicationDto> communications = communicationService.getCommunicationsByLeadId(leadId);
            return ResponseEntity.ok(communications);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/communications/getByDealId/{dealId}")
    @Operation(summary = "Get communications by deal ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Communications found"),
            @ApiResponse(responseCode = "204", description = "No communications found for this deal"),
            @ApiResponse(responseCode = "404", description = "Deal not found"),
            @ApiResponse(responseCode = "400", description = "Invalid deal ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<CommunicationDto>> getCommunicationsByDealId(@PathVariable Long dealId) {
        try {
            List<CommunicationDto> communications = communicationService.getCommunicationsByDealId(dealId);
            return ResponseEntity.ok(communications);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/communications/create")
    @Operation(summary = "Create a new communication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Communication created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<CommunicationDto> createCommunication(@Valid @RequestBody CommunicationDto communicationDto) {
        CommunicationDto createdCommunication = communicationService.createCommunication(communicationDto);
        return new ResponseEntity<>(createdCommunication, HttpStatus.CREATED);
    }

    @PutMapping("/communications/update/{id}")
    @Operation(summary = "Update an existing communication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Communication updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "404", description = "Communication not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<CommunicationDto> updateCommunication(
            @PathVariable Long id,
            @Valid @RequestBody CommunicationDto communicationDto) {
        return ResponseEntity.ok(communicationService.updateCommunication(id, communicationDto));
    }

    @DeleteMapping("/communications/deleteById/{id}")
    @Operation(summary = "Delete a communication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Communication deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied or communication is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "Communication not found")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteCommunication(@PathVariable Long id) {
        communicationService.deleteCommunication(id);
        return ResponseEntity.noContent().build();
    }
}
