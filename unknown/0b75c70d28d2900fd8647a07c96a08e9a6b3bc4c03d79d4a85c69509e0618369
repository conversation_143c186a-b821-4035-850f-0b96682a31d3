package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.BdmDto;
import com.redberyl.invoiceapp.entity.Bdm;
import com.redberyl.invoiceapp.repository.BdmRepository;
import com.redberyl.invoiceapp.service.BdmService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class BdmServiceImpl implements BdmService {

    @Autowired
    private BdmRepository bdmRepository;

    @Override
    public List<BdmDto> getAllBdms() {
        return bdmRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public BdmDto getBdmById(Long id) {
        Bdm bdm = bdmRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("BDM not found with id: " + id));
        return convertToDto(bdm);
    }

    @Override
    @Transactional
    public BdmDto createBdm(BdmDto bdmDto) {
        Bdm bdm = convertToEntity(bdmDto);
        Bdm savedBdm = bdmRepository.save(bdm);
        return convertToDto(savedBdm);
    }

    @Override
    @Transactional
    public BdmDto updateBdm(Long id, BdmDto bdmDto) {
        Bdm existingBdm = bdmRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("BDM not found with id: " + id));
        
        existingBdm.setName(bdmDto.getName());
        existingBdm.setEmail(bdmDto.getEmail());
        existingBdm.setPhone(bdmDto.getPhone());
        existingBdm.setGstNumber(bdmDto.getGstNumber());
        existingBdm.setBillingAddress(bdmDto.getBillingAddress());
        
        Bdm updatedBdm = bdmRepository.save(existingBdm);
        return convertToDto(updatedBdm);
    }

    @Override
    @Transactional
    public void deleteBdm(Long id) {
        if (!bdmRepository.existsById(id)) {
            throw new EntityNotFoundException("BDM not found with id: " + id);
        }
        bdmRepository.deleteById(id);
    }

    private BdmDto convertToDto(Bdm bdm) {
        return BdmDto.builder()
                .id(bdm.getId())
                .name(bdm.getName())
                .email(bdm.getEmail())
                .phone(bdm.getPhone())
                .gstNumber(bdm.getGstNumber())
                .billingAddress(bdm.getBillingAddress())
                .build();
    }

    private Bdm convertToEntity(BdmDto bdmDto) {
        return Bdm.builder()
                .id(bdmDto.getId())
                .name(bdmDto.getName())
                .email(bdmDto.getEmail())
                .phone(bdmDto.getPhone())
                .gstNumber(bdmDto.getGstNumber())
                .billingAddress(bdmDto.getBillingAddress())
                .build();
    }
}
