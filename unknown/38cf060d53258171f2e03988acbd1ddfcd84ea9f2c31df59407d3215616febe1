package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.MessageResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@CrossOrigin(origins = { "http://localhost:3060", "http://127.0.0.1:3060" }, allowedHeaders = "*", methods = {
        RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT,
        RequestMethod.DELETE, RequestMethod.OPTIONS }, allowCredentials = "true", maxAge = 3600)
@RestController

@Tag(name = "Collection Field Config", description = "Collection Field Config API")
@SecurityRequirement(name = "bearer-jwt")
public class CollectionFieldConfigController {

    @GetMapping("/collection-field-configs/getById/{id}")
    @Operation(summary = "Get collection field config by ID", description = "Get collection field config by ID")
    public ResponseEntity<?> getById(
            @Parameter(description = "Field config ID", required = true) @PathVariable("id") Long id) {
        // This is a placeholder implementation
        Map<String, Object> response = new HashMap<>();
        response.put("id", id);
        response.put("name", "Sample Field Config");
        response.put("type", "text");
        response.put("required", true);
        response.put("collectionId", 1);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/collection-field-configs/getByFieldId/{collectionId}/{id}")
    @Operation(summary = "Get collection field configs by collection field ID", description = "Get collection field configs by collection field ID")
    public ResponseEntity<?> getByFieldId(
            @Parameter(description = "Collection ID", required = true) @PathVariable("collectionId") Long collectionId,
            @Parameter(description = "Field ID", required = true) @PathVariable("id") Long id) {
        // This is a placeholder implementation
        Map<String, Object> response = new HashMap<>();
        response.put("id", id);
        response.put("collectionId", collectionId);
        response.put("fields", new Object[] {
                Map.of("name", "Field 1", "type", "text", "required", true),
                Map.of("name", "Field 2", "type", "number", "required", false)
        });

        return ResponseEntity.ok(response);
    }

    @GetMapping("/collection-field-configs/getAll")
    @Operation(summary = "Get all collection field configs", description = "Get all collection field configs")
    public ResponseEntity<?> getAll() {
        // This is a placeholder implementation
        return ResponseEntity.ok(new Object[] {
                Map.of("id", 1, "name", "Client Name", "type", "text", "required", true, "collectionId", 1),
                Map.of("id", 2, "name", "Client Email", "type", "email", "required", true, "collectionId", 1),
                Map.of("id", 3, "name", "Project Title", "type", "text", "required", true, "collectionId", 2)
        });
    }

    @PostMapping("/collection-field-configs/create")
    @Operation(summary = "Create a new collection field config", description = "Create a new collection field config")
    public ResponseEntity<?> create(@RequestBody Map<String, Object> fieldConfig) {
        // This is a placeholder implementation
        Map<String, Object> response = new HashMap<>(fieldConfig);
        response.put("id", 999); // Simulated ID generation

        return ResponseEntity.ok(response);
    }

    @PostMapping("/collection-field-configs/createBulk")
    @Operation(summary = "Create multiple collection field configs", description = "Create multiple collection field configs")
    public ResponseEntity<?> createBulk(@RequestBody Object[] fieldConfigs) {
        // This is a placeholder implementation
        return ResponseEntity.ok(Map.of(
                "message", "Created " + fieldConfigs.length + " field configurations",
                "count", fieldConfigs.length));
    }

    @PutMapping("/collection-field-configs/update/{id}")
    @Operation(summary = "Update a collection field config", description = "Update a collection field config")
    public ResponseEntity<?> update(
            @Parameter(description = "Field config ID", required = true) @PathVariable("id") Long id,
            @RequestBody Map<String, Object> fieldConfig) {
        // This is a placeholder implementation
        Map<String, Object> response = new HashMap<>(fieldConfig);
        response.put("id", id);

        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/collection-field-configs/deleteById/{id}")
    @Operation(summary = "Delete a collection field config", description = "Delete a collection field config")
    public ResponseEntity<?> deleteById(
            @Parameter(description = "Field config ID", required = true) @PathVariable("id") Long id) {
        // This is a placeholder implementation
        return ResponseEntity.ok(new MessageResponseDto("Field config with ID " + id + " deleted successfully"));
    }
}
