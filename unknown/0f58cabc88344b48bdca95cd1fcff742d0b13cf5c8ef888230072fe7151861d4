package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.DocumentTemplateDto;
import com.redberyl.invoiceapp.entity.DocumentTemplate;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ForeignKeyViolationException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.DocumentTemplateRepository;
import com.redberyl.invoiceapp.service.DocumentTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DocumentTemplateServiceImpl implements DocumentTemplateService {

    @Autowired
    private DocumentTemplateRepository documentTemplateRepository;

    @Override
    public List<DocumentTemplateDto> getAllDocumentTemplates() {
        List<DocumentTemplate> documentTemplates = documentTemplateRepository.findAll();
        if (documentTemplates.isEmpty()) {
            throw new NoContentException("No document templates found");
        }
        return documentTemplates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public DocumentTemplateDto getDocumentTemplateById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Document template ID cannot be null");
        }

        DocumentTemplate documentTemplate = documentTemplateRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Document Template not found with id: " + id));
        return convertToDto(documentTemplate);
    }

    @Override
    public List<DocumentTemplateDto> getDocumentTemplatesByType(String templateType) {
        if (!StringUtils.hasText(templateType)) {
            throw new NullConstraintViolationException("templateType", "Template type cannot be empty");
        }

        List<DocumentTemplate> documentTemplates = documentTemplateRepository.findByTemplateType(templateType);
        if (documentTemplates.isEmpty()) {
            throw new NoContentException("No document templates found with type: " + templateType);
        }

        return documentTemplates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private void validateDocumentTemplateDto(DocumentTemplateDto documentTemplateDto) {
        if (documentTemplateDto == null) {
            throw new NullConstraintViolationException("documentTemplateDto", "Document template data cannot be null");
        }

        if (!StringUtils.hasText(documentTemplateDto.getName())) {
            throw new NullConstraintViolationException("name", "Template name cannot be empty");
        }

        if (!StringUtils.hasText(documentTemplateDto.getTemplateType())) {
            throw new NullConstraintViolationException("templateType", "Template type cannot be empty");
        }
    }

    @Override
    @Transactional
    public DocumentTemplateDto createDocumentTemplate(DocumentTemplateDto documentTemplateDto) {
        validateDocumentTemplateDto(documentTemplateDto);

        try {
            DocumentTemplate documentTemplate = convertToEntity(documentTemplateDto);
            DocumentTemplate savedDocumentTemplate = documentTemplateRepository.save(documentTemplate);
            return convertToDto(savedDocumentTemplate);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("name", "Document template with this name already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error creating document template: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating document template", e);
        }
    }

    @Override
    @Transactional
    public DocumentTemplateDto updateDocumentTemplate(Long id, DocumentTemplateDto documentTemplateDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Document template ID cannot be null");
        }

        DocumentTemplate existingDocumentTemplate = documentTemplateRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Document Template not found with id: " + id));

        try {
            if (StringUtils.hasText(documentTemplateDto.getName())) {
                existingDocumentTemplate.setName(documentTemplateDto.getName());
            }

            if (StringUtils.hasText(documentTemplateDto.getTemplateType())) {
                existingDocumentTemplate.setTemplateType(documentTemplateDto.getTemplateType());
            }

            if (StringUtils.hasText(documentTemplateDto.getFilePath())) {
                existingDocumentTemplate.setFilePath(documentTemplateDto.getFilePath());
            }

            DocumentTemplate updatedDocumentTemplate = documentTemplateRepository.save(existingDocumentTemplate);
            return convertToDto(updatedDocumentTemplate);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("name", "Document template with this name already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error updating document template: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error updating document template", e);
        }
    }

    @Override
    @Transactional
    public void deleteDocumentTemplate(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Document template ID cannot be null");
        }

        if (!documentTemplateRepository.existsById(id)) {
            throw new ResourceNotFoundException("Document Template not found with id: " + id);
        }

        try {
            documentTemplateRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete document template because it is referenced by other entities",
                        e);
            } else {
                throw new CustomException("Error deleting document template: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting document template", e);
        }
    }

    private DocumentTemplateDto convertToDto(DocumentTemplate documentTemplate) {
        return DocumentTemplateDto.builder()
                .id(documentTemplate.getId())
                .name(documentTemplate.getName())
                .templateType(documentTemplate.getTemplateType())
                .filePath(documentTemplate.getFilePath())
                .build();
    }

    private DocumentTemplate convertToEntity(DocumentTemplateDto documentTemplateDto) {
        if (documentTemplateDto.getId() != null) {
            // If updating an existing entity, fetch it first to preserve relationships
            return documentTemplateRepository.findById(documentTemplateDto.getId())
                    .map(existingTemplate -> {
                        existingTemplate.setName(documentTemplateDto.getName());
                        existingTemplate.setTemplateType(documentTemplateDto.getTemplateType());
                        existingTemplate.setFilePath(documentTemplateDto.getFilePath());
                        return existingTemplate;
                    })
                    .orElseGet(() -> createNewDocumentTemplate(documentTemplateDto));
        } else {
            return createNewDocumentTemplate(documentTemplateDto);
        }
    }

    private DocumentTemplate createNewDocumentTemplate(DocumentTemplateDto documentTemplateDto) {
        DocumentTemplate template = new DocumentTemplate();
        template.setName(documentTemplateDto.getName());
        template.setTemplateType(documentTemplateDto.getTemplateType());
        template.setFilePath(documentTemplateDto.getFilePath());
        return template;
    }
}
