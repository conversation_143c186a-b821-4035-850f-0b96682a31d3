package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.DocumentVariable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DocumentVariableRepository extends JpaRepository<DocumentVariable, Long> {
    List<DocumentVariable> findByVersionId(Long versionId);
}
