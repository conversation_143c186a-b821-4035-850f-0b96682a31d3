package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.ApiResponseDto;
import com.redberyl.invoiceapp.dto.BdmDto;
import com.redberyl.invoiceapp.dto.PagedResponse;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.service.BdmService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/v1")
@Tag(name = "BDM", description = "Business Development Manager API")
public class BdmController {

    @Autowired
    private BdmService bdmService;

    @GetMapping("/bdms")
    @Operation(
        summary = "Get all BDMs",
        description = "Get all BDMs with optional pagination, sorting, and filtering"
    )
    @ApiResponses(value = {
            @ApiResponse(
                responseCode = "200",
                description = "BDMs found",
                content = @Content(
                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                    schema = @Schema(implementation = PagedResponse.class)
                )
            ),
            @ApiResponse(
                responseCode = "204",
                description = "No BDMs found",
                content = @Content
            )
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<?> getBdms(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort field") @RequestParam(defaultValue = "name") String sort,
            @Parameter(description = "Sort direction (asc/desc)") @RequestParam(defaultValue = "asc") String direction,
            @Parameter(description = "Filter by name") @RequestParam(required = false) String name,
            @Parameter(description = "Filter by email") @RequestParam(required = false) String email,
            @Parameter(description = "Filter by phone") @RequestParam(required = false) String phone,
            @Parameter(description = "Filter by minimum commission rate") @RequestParam(required = false) String minCommissionRate,
            @Parameter(description = "Filter by maximum commission rate") @RequestParam(required = false) String maxCommissionRate
    ) {
        try {
            // Create pageable object
            Sort.Direction sortDirection = "desc".equalsIgnoreCase(direction) ? Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, sortDirection, sort);

            // Create filters map
            Map<String, String> filters = new HashMap<>();
            if (name != null && !name.isEmpty()) filters.put("name", name);
            if (email != null && !email.isEmpty()) filters.put("email", email);
            if (phone != null && !phone.isEmpty()) filters.put("phone", phone);
            if (minCommissionRate != null && !minCommissionRate.isEmpty()) filters.put("minCommissionRate", minCommissionRate);
            if (maxCommissionRate != null && !maxCommissionRate.isEmpty()) filters.put("maxCommissionRate", maxCommissionRate);

            // Get BDMs with filters
            Page<BdmDto> bdmsPage = bdmService.getBdmsWithFilters(filters, pageable);

            if (bdmsPage.isEmpty()) {
                return ResponseEntity.ok(ApiResponseDto.success("No BDMs found", PagedResponse.from(bdmsPage)));
            }

            return ResponseEntity.ok(ApiResponseDto.success("BDMs retrieved successfully", PagedResponse.from(bdmsPage)));
        } catch (NoContentException e) {
            return ResponseEntity.ok(ApiResponseDto.success("No BDMs found", new PagedResponse<>()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponseDto.error("Error retrieving BDMs: " + e.getMessage()));
        }
    }

    @GetMapping("/bdms/{id}")
    @Operation(
        summary = "Get BDM by ID",
        description = "Get a specific BDM by its ID"
    )
    @ApiResponses(value = {
            @ApiResponse(
                responseCode = "200",
                description = "BDM found",
                content = @Content(
                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                    schema = @Schema(implementation = ApiResponseDto.class)
                )
            ),
            @ApiResponse(
                responseCode = "404",
                description = "BDM not found",
                content = @Content(
                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                    schema = @Schema(implementation = ApiResponseDto.class)
                )
            ),
            @ApiResponse(
                responseCode = "400",
                description = "Invalid ID supplied",
                content = @Content(
                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                    schema = @Schema(implementation = ApiResponseDto.class)
                )
            )
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<BdmDto>> getBdmById(@PathVariable Long id) {
        try {
            BdmDto bdm = bdmService.getBdmById(id);
            return ResponseEntity.ok(ApiResponseDto.success("BDM retrieved successfully", bdm));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponseDto.error("BDM not found with id: " + id));
        } catch (NullConstraintViolationException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("Invalid BDM ID: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponseDto.error("Error retrieving BDM: " + e.getMessage()));
        }
    }

    @PostMapping("/bdms")
    @Operation(
        summary = "Create BDM",
        description = "Create a new BDM"
    )
    @ApiResponses(value = {
            @ApiResponse(
                responseCode = "201",
                description = "BDM created successfully",
                content = @Content(
                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                    schema = @Schema(implementation = ApiResponseDto.class)
                )
            ),
            @ApiResponse(
                responseCode = "400",
                description = "Invalid input",
                content = @Content(
                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                    schema = @Schema(implementation = ApiResponseDto.class)
                )
            )
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<BdmDto>> createBdm(@Valid @RequestBody BdmDto bdmDto) {
        try {
            // Ensure clientCount and projectCount are not set in the request
            bdmDto.setClientCount(null);
            bdmDto.setProjectCount(null);

            BdmDto createdBdm = bdmService.createBdm(bdmDto);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponseDto.success("BDM created successfully", createdBdm));
        } catch (NullConstraintViolationException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("Validation error: " + e.getMessage()));
        } catch (UniqueConstraintViolationException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("Duplicate data: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("Error creating BDM: " + e.getMessage()));
        }
    }

    @PutMapping("/bdms/{id}")
    @Operation(
        summary = "Update BDM",
        description = "Update an existing BDM"
    )
    @ApiResponses(value = {
            @ApiResponse(
                responseCode = "200",
                description = "BDM updated successfully",
                content = @Content(
                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                    schema = @Schema(implementation = ApiResponseDto.class)
                )
            ),
            @ApiResponse(
                responseCode = "400",
                description = "Invalid input",
                content = @Content(
                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                    schema = @Schema(implementation = ApiResponseDto.class)
                )
            ),
            @ApiResponse(
                responseCode = "404",
                description = "BDM not found",
                content = @Content(
                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                    schema = @Schema(implementation = ApiResponseDto.class)
                )
            )
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<BdmDto>> updateBdm(@PathVariable Long id, @Valid @RequestBody BdmDto bdmDto) {
        try {
            BdmDto updatedBdm = bdmService.updateBdm(id, bdmDto);
            return ResponseEntity.ok(ApiResponseDto.success("BDM updated successfully", updatedBdm));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponseDto.error("BDM not found with id: " + id));
        } catch (NullConstraintViolationException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("Validation error: " + e.getMessage()));
        } catch (UniqueConstraintViolationException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("Duplicate data: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponseDto.error("Error updating BDM: " + e.getMessage()));
        }
    }

    @DeleteMapping("/bdms/{id}")
    @Operation(
        summary = "Delete BDM",
        description = "Delete an existing BDM"
    )
    @ApiResponses(value = {
            @ApiResponse(
                responseCode = "200",
                description = "BDM deleted successfully",
                content = @Content(
                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                    schema = @Schema(implementation = ApiResponseDto.class)
                )
            ),
            @ApiResponse(
                responseCode = "400",
                description = "Invalid ID supplied or BDM is referenced by other entities",
                content = @Content(
                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                    schema = @Schema(implementation = ApiResponseDto.class)
                )
            ),
            @ApiResponse(
                responseCode = "404",
                description = "BDM not found",
                content = @Content(
                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                    schema = @Schema(implementation = ApiResponseDto.class)
                )
            )
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<Void>> deleteBdm(@PathVariable Long id) {
        try {
            bdmService.deleteBdm(id);
            return ResponseEntity.ok(ApiResponseDto.success("BDM deleted successfully", null));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponseDto.error("BDM not found with id: " + id));
        } catch (NullConstraintViolationException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("Invalid BDM ID: " + e.getMessage()));
        } catch (CustomException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponseDto.error("Error deleting BDM: " + e.getMessage()));
        }
    }
}
