#!/bin/bash

echo "Testing Nested Tax Rate API..."

echo "Creating a new tax rate with nested tax type..."
curl -X POST \
  "http://localhost:8081/api/api/tax-rates" \
  -H "accept: */*" \
  -H "Content-Type: application/json" \
  -d '{
  "taxTypeId": {
    "id": 2,
    "taxType": "GST",
    "taxTypeDescription": "Goods and Services Tax",
    "created_at": "2025-04-22T12:20:31.705",
    "updated_at": "2025-04-22T12:20:31.705"
  },
  "rate": 18,
  "effectiveFrom": "2025-04-17",
  "effectiveTo": "2025-06-22"
}'

echo -e "\n\nDone!"
