package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.DocumentTemplateDto;
import com.redberyl.invoiceapp.dto.DocumentTemplateVersionDto;
import com.redberyl.invoiceapp.entity.DocumentTemplate;
import com.redberyl.invoiceapp.entity.DocumentTemplateVersion;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ForeignKeyViolationException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.DocumentTemplateRepository;
import com.redberyl.invoiceapp.repository.DocumentTemplateVersionRepository;
import com.redberyl.invoiceapp.service.DocumentTemplateVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class DocumentTemplateVersionServiceImpl implements DocumentTemplateVersionService {

    @Autowired
    private DocumentTemplateVersionRepository documentTemplateVersionRepository;

    @Autowired
    private DocumentTemplateRepository documentTemplateRepository;

    @Override
    public List<DocumentTemplateVersionDto> getAllDocumentTemplateVersions() {
        List<DocumentTemplateVersion> documentTemplateVersions = documentTemplateVersionRepository.findAll();
        if (documentTemplateVersions.isEmpty()) {
            throw new NoContentException("No document template versions found");
        }
        return documentTemplateVersions.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public DocumentTemplateVersionDto getDocumentTemplateVersionById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Document template version ID cannot be null");
        }

        DocumentTemplateVersion documentTemplateVersion = documentTemplateVersionRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Document Template Version not found with id: " + id));
        return convertToDto(documentTemplateVersion);
    }

    @Override
    public List<DocumentTemplateVersionDto> getDocumentTemplateVersionsByTemplateId(Long templateId) {
        if (templateId == null) {
            throw new NullConstraintViolationException("templateId", "Template ID cannot be null");
        }

        // Check if template exists
        if (!documentTemplateRepository.existsById(templateId)) {
            throw new ResourceNotFoundException("Document Template not found with id: " + templateId);
        }

        List<DocumentTemplateVersion> documentTemplateVersions = documentTemplateVersionRepository
                .findByTemplateId(templateId);
        if (documentTemplateVersions.isEmpty()) {
            throw new NoContentException("No document template versions found for template with id: " + templateId);
        }

        return documentTemplateVersions.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public Optional<DocumentTemplateVersionDto> getDocumentTemplateVersionByTemplateIdAndVersionNumber(Long templateId,
            Integer versionNumber) {
        if (templateId == null) {
            throw new NullConstraintViolationException("templateId", "Template ID cannot be null");
        }

        if (versionNumber == null) {
            throw new NullConstraintViolationException("versionNumber", "Version number cannot be null");
        }

        // Check if template exists
        if (!documentTemplateRepository.existsById(templateId)) {
            throw new ResourceNotFoundException("Document Template not found with id: " + templateId);
        }

        return documentTemplateVersionRepository.findByTemplateIdAndVersionNumber(templateId, versionNumber)
                .map(this::convertToDto);
    }

    @Override
    public List<DocumentTemplateVersionDto> getActiveDocumentTemplateVersionsByTemplateId(Long templateId) {
        if (templateId == null) {
            throw new NullConstraintViolationException("templateId", "Template ID cannot be null");
        }

        // Check if template exists
        if (!documentTemplateRepository.existsById(templateId)) {
            throw new ResourceNotFoundException("Document Template not found with id: " + templateId);
        }

        List<DocumentTemplateVersion> documentTemplateVersions = documentTemplateVersionRepository
                .findByTemplateIdAndIsActiveTrue(templateId);
        if (documentTemplateVersions.isEmpty()) {
            throw new NoContentException(
                    "No active document template versions found for template with id: " + templateId);
        }

        return documentTemplateVersions.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private void validateDocumentTemplateVersionDto(DocumentTemplateVersionDto documentTemplateVersionDto) {
        if (documentTemplateVersionDto == null) {
            throw new NullConstraintViolationException("documentTemplateVersionDto",
                    "Document template version data cannot be null");
        }

        if (documentTemplateVersionDto.getTemplateId() == null) {
            throw new NullConstraintViolationException("templateId", "Template ID cannot be null");
        }

        if (documentTemplateVersionDto.getVersionNumber() == null) {
            throw new NullConstraintViolationException("versionNumber", "Version number cannot be null");
        }

        // Validate template ID
        if (!documentTemplateRepository.existsById(documentTemplateVersionDto.getTemplateId())) {
            throw new ForeignKeyViolationException("templateId",
                    "Document template not found with id: " + documentTemplateVersionDto.getTemplateId());
        }

        // Check for duplicate version number for the same template
        if (documentTemplateVersionDto.getId() == null &&
                documentTemplateVersionRepository.findByTemplateIdAndVersionNumber(
                        documentTemplateVersionDto.getTemplateId(),
                        documentTemplateVersionDto.getVersionNumber()).isPresent()) {
            throw new UniqueConstraintViolationException("versionNumber",
                    "Version number " + documentTemplateVersionDto.getVersionNumber() +
                            " already exists for template with id: " + documentTemplateVersionDto.getTemplateId());
        }
    }

    @Override
    @Transactional
    public DocumentTemplateVersionDto createDocumentTemplateVersion(
            DocumentTemplateVersionDto documentTemplateVersionDto) {
        validateDocumentTemplateVersionDto(documentTemplateVersionDto);

        try {
            DocumentTemplateVersion documentTemplateVersion = convertToEntity(documentTemplateVersionDto);
            DocumentTemplateVersion savedDocumentTemplateVersion = documentTemplateVersionRepository
                    .save(documentTemplateVersion);
            return convertToDto(savedDocumentTemplateVersion);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("versionNumber",
                        "Version number already exists for this template");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error creating document template version: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating document template version", e);
        }
    }

    @Override
    @Transactional
    public DocumentTemplateVersionDto updateDocumentTemplateVersion(Long id,
            DocumentTemplateVersionDto documentTemplateVersionDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Document template version ID cannot be null");
        }

        DocumentTemplateVersion existingDocumentTemplateVersion = documentTemplateVersionRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Document Template Version not found with id: " + id));

        try {
            // Check for duplicate version number if changing version number
            if (documentTemplateVersionDto.getVersionNumber() != null &&
                    !documentTemplateVersionDto.getVersionNumber()
                            .equals(existingDocumentTemplateVersion.getVersionNumber())) {

                if (documentTemplateVersionRepository.findByTemplateIdAndVersionNumber(
                        existingDocumentTemplateVersion.getTemplate().getId(),
                        documentTemplateVersionDto.getVersionNumber()).isPresent()) {
                    throw new UniqueConstraintViolationException("versionNumber",
                            "Version number " + documentTemplateVersionDto.getVersionNumber() +
                                    " already exists for this template");
                }

                existingDocumentTemplateVersion.setVersionNumber(documentTemplateVersionDto.getVersionNumber());
            }

            if (documentTemplateVersionDto.getContent() != null) {
                existingDocumentTemplateVersion.setContent(documentTemplateVersionDto.getContent());
            }

            if (documentTemplateVersionDto.getCreatedBy() != null) {
                existingDocumentTemplateVersion.setCreatedBy(documentTemplateVersionDto.getCreatedBy());
            }

            if (documentTemplateVersionDto.getIsActive() != null) {
                existingDocumentTemplateVersion.setIsActive(documentTemplateVersionDto.getIsActive());
            }

            DocumentTemplateVersion updatedDocumentTemplateVersion = documentTemplateVersionRepository
                    .save(existingDocumentTemplateVersion);
            return convertToDto(updatedDocumentTemplateVersion);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("versionNumber",
                        "Version number already exists for this template");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error updating document template version: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            if (e instanceof UniqueConstraintViolationException) {
                throw e;
            }
            throw new CustomException("Error updating document template version", e);
        }
    }

    @Override
    @Transactional
    public DocumentTemplateVersionDto activateDocumentTemplateVersion(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Document template version ID cannot be null");
        }

        DocumentTemplateVersion documentTemplateVersion = documentTemplateVersionRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Document Template Version not found with id: " + id));

        try {
            documentTemplateVersion.setIsActive(true);
            DocumentTemplateVersion updatedDocumentTemplateVersion = documentTemplateVersionRepository
                    .save(documentTemplateVersion);
            return convertToDto(updatedDocumentTemplateVersion);
        } catch (Exception e) {
            throw new CustomException("Error activating document template version: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public DocumentTemplateVersionDto deactivateDocumentTemplateVersion(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Document template version ID cannot be null");
        }

        DocumentTemplateVersion documentTemplateVersion = documentTemplateVersionRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Document Template Version not found with id: " + id));

        try {
            documentTemplateVersion.setIsActive(false);
            DocumentTemplateVersion updatedDocumentTemplateVersion = documentTemplateVersionRepository
                    .save(documentTemplateVersion);
            return convertToDto(updatedDocumentTemplateVersion);
        } catch (Exception e) {
            throw new CustomException("Error deactivating document template version: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void deleteDocumentTemplateVersion(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Document template version ID cannot be null");
        }

        if (!documentTemplateVersionRepository.existsById(id)) {
            throw new ResourceNotFoundException("Document Template Version not found with id: " + id);
        }

        try {
            documentTemplateVersionRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException(
                        "Cannot delete document template version because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting document template version: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting document template version", e);
        }
    }

    private DocumentTemplateVersionDto convertToDto(DocumentTemplateVersion documentTemplateVersion) {
        // Create a DocumentTemplateDto from the template entity
        DocumentTemplateDto templateDto = null;
        if (documentTemplateVersion.getTemplate() != null) {
            templateDto = DocumentTemplateDto.builder()
                    .id(documentTemplateVersion.getTemplate().getId())
                    .name(documentTemplateVersion.getTemplate().getName())
                    .templateType(documentTemplateVersion.getTemplate().getTemplateType())
                    .filePath(documentTemplateVersion.getTemplate().getFilePath())
                    .build();
        }

        // Build the DocumentTemplateVersionDto with the template object
        DocumentTemplateVersionDto.DocumentTemplateVersionDtoBuilder builder = DocumentTemplateVersionDto.builder()
                .id(documentTemplateVersion.getId())
                .versionNumber(documentTemplateVersion.getVersionNumber())
                .content(documentTemplateVersion.getContent())
                .createdBy(documentTemplateVersion.getCreatedBy())
                .isActive(documentTemplateVersion.getIsActive());

        if (documentTemplateVersion.getTemplate() != null) {
            builder.templateId(documentTemplateVersion.getTemplate().getId());
            builder.template(templateDto); // Include the full template object
        }

        return builder.build();
    }

    private DocumentTemplateVersion convertToEntity(DocumentTemplateVersionDto documentTemplateVersionDto) {
        DocumentTemplateVersion documentTemplateVersion;

        if (documentTemplateVersionDto.getId() != null) {
            // If updating an existing entity, fetch it first to preserve relationships
            documentTemplateVersion = documentTemplateVersionRepository.findById(documentTemplateVersionDto.getId())
                    .orElse(new DocumentTemplateVersion());
        } else {
            documentTemplateVersion = new DocumentTemplateVersion();
        }

        if (documentTemplateVersionDto.getTemplateId() != null) {
            DocumentTemplate documentTemplate = documentTemplateRepository
                    .findById(documentTemplateVersionDto.getTemplateId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Document Template not found with id: " + documentTemplateVersionDto.getTemplateId()));

            // Properly maintain bidirectional relationship
            documentTemplateVersion.setTemplate(documentTemplate);

            // Add this version to the template's versions collection if it's not already
            // there
            if (documentTemplateVersion.getId() == null) {
                documentTemplate.getVersions().add(documentTemplateVersion);
            }
        }

        documentTemplateVersion.setVersionNumber(documentTemplateVersionDto.getVersionNumber());
        documentTemplateVersion.setContent(documentTemplateVersionDto.getContent());
        documentTemplateVersion.setCreatedBy(documentTemplateVersionDto.getCreatedBy());
        documentTemplateVersion.setIsActive(documentTemplateVersionDto.getIsActive());

        return documentTemplateVersion;
    }
}
