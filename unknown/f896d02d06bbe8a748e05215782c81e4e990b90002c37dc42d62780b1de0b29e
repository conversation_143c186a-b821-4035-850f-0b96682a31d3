package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.TaxTypeDto;
import com.redberyl.invoiceapp.service.TaxTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/tax-types")
@Tag(name = "Tax Type", description = "Tax Type management API")
public class TaxTypeController {

    @Autowired
    private TaxTypeService taxTypeService;

    @GetMapping
    @Operation(summary = "Get all tax types", description = "Retrieve a list of all tax types")
    public ResponseEntity<List<TaxTypeDto>> getAllTaxTypes() {
        List<TaxTypeDto> taxTypes = taxTypeService.getAllTaxTypes();
        return new ResponseEntity<>(taxTypes, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get tax type by ID", description = "Retrieve a tax type by its ID")
    public ResponseEntity<TaxTypeDto> getTaxTypeById(@PathVariable Long id) {
        TaxTypeDto taxType = taxTypeService.getTaxTypeById(id);
        return new ResponseEntity<>(taxType, HttpStatus.OK);
    }

    @GetMapping("/type/{taxType}")
    @Operation(summary = "Get tax type by type", description = "Retrieve a tax type by its type")
    public ResponseEntity<TaxTypeDto> getTaxTypeByType(@PathVariable String taxType) {
        TaxTypeDto type = taxTypeService.getTaxTypeByType(taxType);
        return new ResponseEntity<>(type, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create tax type", description = "Create a new tax type")
    public ResponseEntity<TaxTypeDto> createTaxType(@Valid @RequestBody TaxTypeDto taxTypeDto) {
        TaxTypeDto createdTaxType = taxTypeService.createTaxType(taxTypeDto);
        return new ResponseEntity<>(createdTaxType, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update tax type", description = "Update an existing tax type")
    public ResponseEntity<TaxTypeDto> updateTaxType(@PathVariable Long id, @Valid @RequestBody TaxTypeDto taxTypeDto) {
        TaxTypeDto updatedTaxType = taxTypeService.updateTaxType(id, taxTypeDto);
        return new ResponseEntity<>(updatedTaxType, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete tax type", description = "Delete a tax type by its ID")
    public ResponseEntity<Void> deleteTaxType(@PathVariable Long id) {
        taxTypeService.deleteTaxType(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
