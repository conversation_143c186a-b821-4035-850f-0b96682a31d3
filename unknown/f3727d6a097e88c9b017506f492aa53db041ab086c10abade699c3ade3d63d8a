package com.redberyl.invoiceapp.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Tag(name = "Public", description = "Public API without authentication")
public class PublicController {

    @GetMapping("/public/getTaxTypes")
    @Operation(summary = "Get all tax types", description = "Get all tax types without authentication")
    public ResponseEntity<List<Map<String, Object>>> getAllTaxTypes() {
        List<Map<String, Object>> taxTypes = new ArrayList<>();

        Map<String, Object> gst = new HashMap<>();
        gst.put("id", 1);
        gst.put("taxType", "GST");
        gst.put("taxTypeDescription", "Goods and Services Tax");

        Map<String, Object> igst = new HashMap<>();
        igst.put("id", 2);
        igst.put("taxType", "IGST");
        igst.put("taxTypeDescription", "Integrated Goods and Services Tax");

        Map<String, Object> cgst = new HashMap<>();
        cgst.put("id", 3);
        cgst.put("taxType", "CGST");
        cgst.put("taxTypeDescription", "Central Goods and Services Tax");

        Map<String, Object> sgst = new HashMap<>();
        sgst.put("id", 4);
        sgst.put("taxType", "SGST");
        sgst.put("taxTypeDescription", "State Goods and Services Tax");

        taxTypes.add(gst);
        taxTypes.add(igst);
        taxTypes.add(cgst);
        taxTypes.add(sgst);

        return new ResponseEntity<>(taxTypes, HttpStatus.OK);
    }

    @GetMapping("/public/getTaxRates")
    @Operation(summary = "Get all tax rates", description = "Get all tax rates without authentication")
    public ResponseEntity<List<Map<String, Object>>> getAllTaxRates() {
        List<Map<String, Object>> taxRates = new ArrayList<>();

        Map<String, Object> gst18 = new HashMap<>();
        gst18.put("id", 1);
        gst18.put("taxTypeId", 1);
        gst18.put("taxType", "GST");
        gst18.put("rate", 18.0);
        gst18.put("effectiveFrom", "2023-04-01");
        gst18.put("effectiveTo", null);

        Map<String, Object> igst18 = new HashMap<>();
        igst18.put("id", 2);
        igst18.put("taxTypeId", 2);
        igst18.put("taxType", "IGST");
        igst18.put("rate", 18.0);
        igst18.put("effectiveFrom", "2023-04-01");
        igst18.put("effectiveTo", null);

        Map<String, Object> cgst9 = new HashMap<>();
        cgst9.put("id", 3);
        cgst9.put("taxTypeId", 3);
        cgst9.put("taxType", "CGST");
        cgst9.put("rate", 9.0);
        cgst9.put("effectiveFrom", "2023-04-01");
        cgst9.put("effectiveTo", null);

        Map<String, Object> sgst9 = new HashMap<>();
        sgst9.put("id", 4);
        sgst9.put("taxTypeId", 4);
        sgst9.put("taxType", "SGST");
        sgst9.put("rate", 9.0);
        sgst9.put("effectiveFrom", "2023-04-01");
        sgst9.put("effectiveTo", null);

        taxRates.add(gst18);
        taxRates.add(igst18);
        taxRates.add(cgst9);
        taxRates.add(sgst9);

        return new ResponseEntity<>(taxRates, HttpStatus.OK);
    }
}
