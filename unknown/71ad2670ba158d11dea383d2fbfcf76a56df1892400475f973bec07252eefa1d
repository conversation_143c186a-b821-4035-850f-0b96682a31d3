package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.InvoiceTaxDto;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.InvoiceTax;
import com.redberyl.invoiceapp.entity.TaxRate;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.repository.InvoiceTaxRepository;
import com.redberyl.invoiceapp.repository.TaxRateRepository;
import com.redberyl.invoiceapp.service.InvoiceTaxService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class InvoiceTaxServiceImpl implements InvoiceTaxService {

    @Autowired
    private InvoiceTaxRepository invoiceTaxRepository;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private TaxRateRepository taxRateRepository;

    @Override
    public List<InvoiceTaxDto> getAllInvoiceTaxes() {
        return invoiceTaxRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public InvoiceTaxDto getInvoiceTaxById(Long id) {
        InvoiceTax invoiceTax = invoiceTaxRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Invoice Tax not found with id: " + id));
        return convertToDto(invoiceTax);
    }

    @Override
    public List<InvoiceTaxDto> getInvoiceTaxesByInvoiceId(Long invoiceId) {
        return invoiceTaxRepository.findByInvoiceId(invoiceId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceTaxDto> getInvoiceTaxesByTaxRateId(Long taxRateId) {
        return invoiceTaxRepository.findByTaxRateId(taxRateId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public InvoiceTaxDto createInvoiceTax(InvoiceTaxDto invoiceTaxDto) {
        InvoiceTax invoiceTax = convertToEntity(invoiceTaxDto);
        InvoiceTax savedInvoiceTax = invoiceTaxRepository.save(invoiceTax);
        return convertToDto(savedInvoiceTax);
    }

    @Override
    @Transactional
    public InvoiceTaxDto updateInvoiceTax(Long id, InvoiceTaxDto invoiceTaxDto) {
        InvoiceTax existingInvoiceTax = invoiceTaxRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Invoice Tax not found with id: " + id));
        
        if (invoiceTaxDto.getInvoiceId() != null) {
            Invoice invoice = invoiceRepository.findById(invoiceTaxDto.getInvoiceId())
                    .orElseThrow(() -> new EntityNotFoundException("Invoice not found with id: " + invoiceTaxDto.getInvoiceId()));
            existingInvoiceTax.setInvoice(invoice);
        }
        
        if (invoiceTaxDto.getTaxRateId() != null) {
            TaxRate taxRate = taxRateRepository.findById(invoiceTaxDto.getTaxRateId())
                    .orElseThrow(() -> new EntityNotFoundException("Tax Rate not found with id: " + invoiceTaxDto.getTaxRateId()));
            existingInvoiceTax.setTaxRate(taxRate);
        }
        
        existingInvoiceTax.setAmount(invoiceTaxDto.getAmount());
        
        InvoiceTax updatedInvoiceTax = invoiceTaxRepository.save(existingInvoiceTax);
        return convertToDto(updatedInvoiceTax);
    }

    @Override
    @Transactional
    public void deleteInvoiceTax(Long id) {
        if (!invoiceTaxRepository.existsById(id)) {
            throw new EntityNotFoundException("Invoice Tax not found with id: " + id);
        }
        invoiceTaxRepository.deleteById(id);
    }

    private InvoiceTaxDto convertToDto(InvoiceTax invoiceTax) {
        return InvoiceTaxDto.builder()
                .id(invoiceTax.getId())
                .invoiceId(invoiceTax.getInvoice() != null ? invoiceTax.getInvoice().getId() : null)
                .taxRateId(invoiceTax.getTaxRate() != null ? invoiceTax.getTaxRate().getId() : null)
                .amount(invoiceTax.getAmount())
                .build();
    }

    private InvoiceTax convertToEntity(InvoiceTaxDto invoiceTaxDto) {
        InvoiceTax invoiceTax = new InvoiceTax();
        invoiceTax.setId(invoiceTaxDto.getId());
        
        if (invoiceTaxDto.getInvoiceId() != null) {
            Invoice invoice = invoiceRepository.findById(invoiceTaxDto.getInvoiceId())
                    .orElseThrow(() -> new EntityNotFoundException("Invoice not found with id: " + invoiceTaxDto.getInvoiceId()));
            invoiceTax.setInvoice(invoice);
        }
        
        if (invoiceTaxDto.getTaxRateId() != null) {
            TaxRate taxRate = taxRateRepository.findById(invoiceTaxDto.getTaxRateId())
                    .orElseThrow(() -> new EntityNotFoundException("Tax Rate not found with id: " + invoiceTaxDto.getTaxRateId()));
            invoiceTax.setTaxRate(taxRate);
        }
        
        invoiceTax.setAmount(invoiceTaxDto.getAmount());
        
        return invoiceTax;
    }
}
