<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>RedBeryl Tech Solutions - Invoice</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

        body {
            font-family: '<PERSON>o', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #f9f9f9;
            font-size: 14px;
            line-height: 1.5;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid #ddd;
            padding: 30px;
            background-color: white;
        }

        .invoice-header {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .invoice-details-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .invoice-details-left, .invoice-details-right {
            width: 48%;
        }

        .invoice-details-title {
            font-weight: bold;
            margin-bottom: 10px;
        }

        .invoice-details-row {
            margin-bottom: 5px;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .invoice-table th, .invoice-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }

        .invoice-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .net-payable {
            margin-bottom: 20px;
            font-weight: bold;
        }

        .payment-info-section {
            width: 100%;
            margin-bottom: 20px;
            border-collapse: collapse;
            border: 1px solid #000;
        }

        .payment-info-section td, .payment-info-section th {
            padding: 8px;
            vertical-align: top;
            border: 1px solid #000;
        }

        .payment-info-header {
            text-align: center;
            font-weight: bold;
            background-color: #ffffff;
            border-bottom: 1px solid #000;
        }

        .payment-info {
            width: 60%;
            vertical-align: top;
        }

        .authorized-signatory {
            width: 40%;
            text-align: center;
            vertical-align: middle;
        }

        .signatory-text {
            margin-top: 50px;
        }

        .payment-info-table {
            width: 100%;
            border-collapse: collapse;
        }

        .payment-info-table td {
            padding: 4px;
            border: none;
            text-align: left;
        }

        .payment-info-label {
            font-weight: normal;
            margin-bottom: 5px;
            text-align: left;
            display: block;
        }

        .thank-you {
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="invoice-header">INVOICE</div>

        <div class="invoice-details-section">
            <div class="invoice-details-left">
                <div class="invoice-details-title">Invoice Details :-</div>
                <div class="invoice-details-row">Invoice Date: <span th:text="${invoiceDate}">05/13/2025</span></div>
                <div class="invoice-details-row">Invoice No.: <span th:text="${invoiceNumber}">INV-001</span></div>
                <div class="invoice-details-row">Invoice Month: <span th:text="${invoiceMonth}">May 2025</span></div>
                <div class="invoice-details-row">Invoice For: <span th:text="${invoiceFor}">Services</span></div>
                <div class="invoice-details-row">HSN No.: <span th:text="${hsnCode}">998313</span></div>
                <div class="invoice-details-row">Employee Name: <span th:text="${candidateName}">John Doe</span></div>
                <div class="invoice-details-row">Employee Engagement Code: <span th:text="${employeeEngagementCode}">ENG-001</span></div>
            </div>
            <div class="invoice-details-right">
                <div class="invoice-details-title">Billed To :-</div>
                <div class="invoice-details-row" th:text="${clientName}">Client Name</div>
                <div class="invoice-details-row" th:if="${projectBillingAddress}" th:text="${projectBillingAddress}">Client Address</div>
                <div class="invoice-details-row">GST No: <span th:text="${clientGstNumber}">27AAAAA0000A1Z5</span></div>
            </div>
        </div>

        <table class="invoice-table">
            <thead>
                <tr>
                    <th>Employee Name</th>
                    <th>Joining Date</th>
                    <th>Rate</th>
                    <th>Bill Amount</th>
                    <th colspan="3">GST</th>
                    <th>Total Bill Amount</th>
                </tr>
                <tr>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th>CGST 9%</th>
                    <th>SGST 9%</th>
                    <th>IGST 18%</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td th:text="${candidateName}">John Doe</td>
                    <td th:text="${candidateJoiningDate}">01/01/2025</td>
                    <td th:text="${candidateRate}">$100/hr</td>
                    <td th:text="${billingAmount}">$3,000.00</td>
                    <td th:text="${cgstAmount}">$270.00</td>
                    <td th:text="${sgstAmount}">$270.00</td>
                    <td th:text="${igstAmount}">$540.00</td>
                    <td th:text="${totalAmount}">$3,540.00</td>
                </tr>
            </tbody>
        </table>

        <div class="net-payable">
            Net Payable: <span th:text="${totalAmount}">₹3,540.00</span> <span th:text="${totalAmountInWords}">(Three Thousand Five Hundred Forty Only)</span>
        </div>

        <table class="payment-info-section" border="1" cellspacing="0" cellpadding="8">
            <tr>
                <th class="payment-info-header">Payment Information</th>
                <th class="payment-info-header">Authorized Signatory</th>
            </tr>
            <tr>
                <td class="payment-info">
                    <div class="payment-info-label">Bank Name: <span th:text="${redberylAccountBankName}">hdfc</span></div>
                    <div class="payment-info-label">Branch Name: <span th:text="${redberylAccountBranchName}">Destination Centre, Magarpatta, Pune</span></div>
                    <div class="payment-info-label">Account Name: <span th:text="${redberylAccountName}"></span></div>
                    <div class="payment-info-label">Account No: <span th:text="${redberylAccountNumber}">***********</span></div>
                    <div class="payment-info-label">IFSC Code: <span th:text="${redberylAccountIfscCode}"></span></div>
                    <div class="payment-info-label">Account Type: <span th:text="${redberylAccountType}">Current Account</span></div>
                    <div class="payment-info-label">GSTN: <span th:text="${redberylAccountGstn}">27**********1Z5</span></div>
                    <div class="payment-info-label">CIN: <span th:text="${redberylAccountCin}">U72900PN2022PTC213381</span></div>
                    <div class="payment-info-label">PAN No: <span th:text="${redberylAccountPanNo}">**********</span></div>
                </td>
                <td class="authorized-signatory">
                    <div class="signatory-text">For Redberyl Tech Solutions Pvt. Ltd.</div>
                </td>
            </tr>
        </table>

        <div class="thank-you">
            Thank you for doing business with us.
        </div>
    </div>
</body>
</html>
