{"version": 3, "sources": ["../../@radix-ui/react-visually-hidden/src/VisuallyHidden.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * VisuallyHidden\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'VisuallyHidden';\n\ntype VisuallyHiddenElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface VisuallyHiddenProps extends PrimitiveSpanProps {}\n\nconst VisuallyHidden = React.forwardRef<VisuallyHiddenElement, VisuallyHiddenProps>(\n  (props, forwardedRef) => {\n    return (\n      <Primitive.span\n        {...props}\n        ref={forwardedRef}\n        style={{\n          // See: https://github.com/twbs/bootstrap/blob/master/scss/mixins/_screen-reader.scss\n          position: 'absolute',\n          border: 0,\n          width: 1,\n          height: 1,\n          padding: 0,\n          margin: -1,\n          overflow: 'hidden',\n          clip: 'rect(0, 0, 0, 0)',\n          whiteSpace: 'nowrap',\n          wordWrap: 'normal',\n          ...props.style,\n        }}\n      />\n    );\n  }\n);\n\nVisuallyHidden.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = VisuallyHidden;\n\nexport {\n  VisuallyHidden,\n  //\n  Root,\n};\nexport type { VisuallyHiddenProps };\n"], "mappings": ";;;;;;;;;;;;;;AAAA,YAAuB;AAgBjB,yBAAA;AATN,IAAM,OAAO;AAMb,IAAM,iBAAuB;EAC3B,CAAC,OAAO,iBAAiB;AACvB,eACE;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,KAAK;QACL,OAAO;;UAEL,UAAU;UACV,QAAQ;UACR,OAAO;UACP,QAAQ;UACR,SAAS;UACT,QAAQ;UACR,UAAU;UACV,MAAM;UACN,YAAY;UACZ,UAAU;UACV,GAAG,MAAM;QACX;MAAA;IACF;EAEJ;AACF;AAEA,eAAe,cAAc;AAI7B,IAAM,OAAO;", "names": []}