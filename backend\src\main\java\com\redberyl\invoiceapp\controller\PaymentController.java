package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.PaymentDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.PaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@Tag(name = "Payment", description = "Payment management API")
public class PaymentController {

    @Autowired
    private PaymentService paymentService;

    @GetMapping("/payments/getAll")
    @Operation(summary = "Get all payments", description = "Get all payments")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Payments found"),
            @ApiResponse(responseCode = "204", description = "No payments found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<PaymentDto>> getAllPayments() {
        try {
            List<PaymentDto> payments = paymentService.getAllPayments();
            return new ResponseEntity<>(payments, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/payments/getById/{id}")
    @Operation(summary = "Get payment by ID", description = "Get payment by ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<PaymentDto> getPaymentById(@PathVariable Long id) {
        PaymentDto payment = paymentService.getPaymentById(id);
        return new ResponseEntity<>(payment, HttpStatus.OK);
    }

    @GetMapping("/payments/getByInvoiceId/{invoiceId}")
    @Operation(summary = "Get payments by invoice ID", description = "Get payments by invoice ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<PaymentDto>> getPaymentsByInvoiceId(@PathVariable Long invoiceId) {
        List<PaymentDto> payments = paymentService.getPaymentsByInvoiceId(invoiceId);
        return new ResponseEntity<>(payments, HttpStatus.OK);
    }

    @GetMapping("/payments/getByDateRange")
    @Operation(summary = "Get payments by date range", description = "Get payments by date range")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<PaymentDto>> getPaymentsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        List<PaymentDto> payments = paymentService.getPaymentsByDateRange(startDate, endDate);
        return new ResponseEntity<>(payments, HttpStatus.OK);
    }

    @GetMapping("/payments/getByPaymentMode/{paymentMode}")
    @Operation(summary = "Get payments by payment mode", description = "Get payments by payment mode")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<PaymentDto>> getPaymentsByPaymentMode(@PathVariable String paymentMode) {
        List<PaymentDto> payments = paymentService.getPaymentsByPaymentMode(paymentMode);
        return new ResponseEntity<>(payments, HttpStatus.OK);
    }

    @PostMapping("/payments/create")
    @Operation(summary = "Create payment", description = "Create payment")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<PaymentDto> createPayment(@Valid @RequestBody PaymentDto paymentDto) {
        PaymentDto createdPayment = paymentService.createPayment(paymentDto);
        return new ResponseEntity<>(createdPayment, HttpStatus.CREATED);
    }

    @PutMapping("/payments/update/{id}")
    @Operation(summary = "Update payment", description = "Update payment")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<PaymentDto> updatePayment(@PathVariable Long id, @Valid @RequestBody PaymentDto paymentDto) {
        PaymentDto updatedPayment = paymentService.updatePayment(id, paymentDto);
        return new ResponseEntity<>(updatedPayment, HttpStatus.OK);
    }

    @DeleteMapping("/payments/deleteById/{id}")
    @Operation(summary = "Delete payment", description = "Delete payment")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deletePayment(@PathVariable Long id) {
        paymentService.deletePayment(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
