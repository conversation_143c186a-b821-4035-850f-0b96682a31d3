import { DEFAULT_HEADERS } from '@/config/api';
import { getAuthToken } from '@/utils/auth';
import { getProxiedUrl, getBasicAuthHeader } from '@/utils/apiUtils';

// Define the Candidate interface based on the entity model
export interface Candidate {
  id?: string;
  name: string;
  email: string;
  phone?: string;
  designation?: string;
  status?: string;
  clientId?: string | number;
  projectId?: string | number;
  // Add client and project objects
  client?: {
    id: number;
    name: string;
  };
  project?: {
    id: number;
    name: string;
    clientId?: number;
    description?: string;
    email?: string;
    phone?: string;
    gstNumber?: string;
    billingAddress?: string;
    shippingAddress?: string;
    engagementCode?: string;
    clientPartnerName?: string;
    clientPartnerEmail?: string;
    clientPartnerPhone?: string;
    bdmId?: number;
    commissionPercentage?: number;
    commissionAmount?: number;
    startDate?: string;
    endDate?: string;
    status?: string;
    value?: number;
    managerSpocId?: number;
    accountHeadSpocId?: number;
    businessHeadSpocId?: number;
    hrSpocId?: number;
    financeSpocId?: number;
  };
  joiningDate?: string;
  billingRate?: number;
  panNo?: string;
  aadharNo?: string;
  uanNo?: string;
  experienceInYrs?: number;
  bankAccountNo?: string;
  branchName?: string;
  ifscCode?: string;
  address?: string;
  salaryOffered?: number;
  skills?: string;
  notes?: string;
  // SPOC fields
  managerSpocId?: number;
  accountHeadSpocId?: number;
  businessHeadSpocId?: number;
  hrSpocId?: number;
  financeSpocId?: number;
  // SPOC objects
  managerSpoc?: {
    id: number;
    name: string;
  };
  accountHeadSpoc?: {
    id: number;
    name: string;
  };
  businessHeadSpoc?: {
    id: number;
    name: string;
  };
  hrSpoc?: {
    id: number;
    name: string;
  };
  financeSpoc?: {
    id: number;
    name: string;
  };
}

// Helper function to handle API requests
const apiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const token = getAuthToken();
  const headers = {
    ...DEFAULT_HEADERS,
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
    ...(options.headers || {}),
  };

  const apiUrl = getProxiedUrl(endpoint);
  const response = await fetch(apiUrl, {
    ...options,
    headers,
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(
      `API request failed: ${response.status} ${response.statusText} - ${errorText}`
    );
  }

  // For 204 No Content responses, return null
  if (response.status === 204) {
    return null as T;
  }

  return response.json();
};

// Candidate service with API methods
const candidateService = {
  // Get all candidates
  getAllCandidates: async (): Promise<Candidate[]> => {
    try {
      console.log('Fetching all candidates from API');

      // Try direct fetch with API prefix first (this is what the proxy in vite.config.ts expects)
      console.log('Trying direct fetch to /api/candidates/getAll');
      const response = await fetch('/api/candidates/getAll', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': getBasicAuthHeader()
        },
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Candidate data received:', data);

        if (Array.isArray(data)) {
          // Ensure each candidate has a valid name property
          return data.map((candidate: any) => ({
            ...candidate,
            id: candidate.id?.toString() || '0',
            name: candidate.name || 'Unknown Candidate'
          }));
        }
      }

      console.error(`Direct fetch failed with status: ${response.status}. Trying alternative endpoints...`);

      // Try alternative endpoints if the first one fails
      const proxyEndpoints = [
        '/api/candidates',
        '/candidates/getAll',
        '/candidates',
      ];

      for (const endpoint of proxyEndpoints) {
        try {
          console.log(`Trying alternative endpoint: ${endpoint}`);
          const altResponse = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': getBasicAuthHeader()
            },
            credentials: 'include'
          });

          if (!altResponse.ok) {
            console.error(`Fetch to ${endpoint} failed with status: ${altResponse.status}`);
            continue;
          }

          const data = await altResponse.json();
          console.log(`Successfully received data from ${endpoint}:`, data);

          if (Array.isArray(data)) {
            // Ensure each candidate has a valid name property
            return data.map((candidate: any) => ({
              ...candidate,
              id: candidate.id?.toString() || '0',
              name: candidate.name || 'Unknown Candidate'
            }));
          } else {
            console.error('Unexpected response format:', data);
          }
        } catch (directError) {
          console.error(`Fetch to ${endpoint} failed:`, directError);
        }
      }

      // If all attempts fail, try direct connection to backend
      console.log('Trying direct connection to backend at http://192.168.1.100:8091/candidates/getAll');
      const directResponse = await fetch('http://192.168.1.100:8091/candidates/getAll', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': getBasicAuthHeader()
        },
        credentials: 'include'
      });

      if (directResponse.ok) {
        const data = await directResponse.json();
        console.log('Successfully received data from direct backend connection:', data);

        if (Array.isArray(data)) {
          // Ensure each candidate has a valid name property
          return data.map((candidate: any) => ({
            ...candidate,
            id: candidate.id?.toString() || '0',
            name: candidate.name || 'Unknown Candidate'
          }));
        }
      }

      // If all attempts fail, return empty array to prevent UI errors
      console.error('All fetch attempts failed. Returning empty array.');
      return [];
    } catch (error) {
      console.error('Error fetching candidates:', error);
      // Return empty array instead of throwing to prevent UI errors
      return [];
    }
  },

  // Get candidate by ID
  getCandidate: async (id: string): Promise<Candidate> => {
    try {
      console.log(`Fetching candidate with ID ${id} from API`);

      // Try direct fetch with API prefix first (this is what the proxy in vite.config.ts expects)
      console.log(`Trying direct fetch to /api/candidates/getById/${id}`);
      const response = await fetch(`/api/candidates/getById/${id}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': getBasicAuthHeader()
        },
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Candidate data received:', data);
        return {
          ...data,
          id: data.id?.toString() || '0',
          name: data.name || 'Unknown Candidate'
        };
      }

      console.error(`Direct fetch failed with status: ${response.status}. Trying alternative endpoints...`);

      // Try alternative endpoints if the first one fails
      const proxyEndpoints = [
        `/candidates/getById/${id}`,
        `/api/candidates/${id}`,
        `/candidates/${id}`,
      ];

      for (const endpoint of proxyEndpoints) {
        try {
          console.log(`Trying alternative endpoint: ${endpoint}`);
          const altResponse = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': getBasicAuthHeader()
            },
            credentials: 'include'
          });

          if (!altResponse.ok) {
            console.error(`Fetch to ${endpoint} failed with status: ${altResponse.status}`);
            continue;
          }

          const data = await altResponse.json();
          console.log(`Successfully received data from ${endpoint}:`, data);
          return {
            ...data,
            id: data.id?.toString() || '0',
            name: data.name || 'Unknown Candidate'
          };
        } catch (directError) {
          console.error(`Fetch to ${endpoint} failed:`, directError);
        }
      }

      // If all attempts fail, try direct connection to backend
      console.log(`Trying direct connection to backend at http://192.168.1.100:8091/candidates/getById/${id}`);
      const directResponse = await fetch(`http://192.168.1.100:8091/candidates/getById/${id}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': getBasicAuthHeader()
        },
        credentials: 'include'
      });

      if (directResponse.ok) {
        const data = await directResponse.json();
        console.log('Successfully received data from direct backend connection:', data);
        return {
          ...data,
          id: data.id?.toString() || '0',
          name: data.name || 'Unknown Candidate'
        };
      }

      // If all attempts fail, return empty object to prevent UI errors
      console.error('All fetch attempts failed. Returning empty object.');
      return {} as Candidate;
    } catch (error) {
      console.error(`Error fetching candidate with ID ${id}:`, error);
      // Return empty object instead of throwing to prevent UI errors
      return {} as Candidate;
    }
  },

  // Get candidates by client ID
  getCandidatesByClientId: async (clientId: string): Promise<Candidate[]> => {
    try {
      console.log(`Fetching candidates for client ID ${clientId} from API`);
      return await apiRequest<Candidate[]>(`/candidates/getByClientId/${clientId}`);
    } catch (error) {
      console.error(`Error fetching candidates for client ID ${clientId}:`, error);
      // Try alternative endpoint if the first one fails
      try {
        console.log(`Trying alternative endpoint for candidates with client ID ${clientId}`);
        return await apiRequest<Candidate[]>(`/candidates/getByClientId/${clientId}`);
      } catch (alternativeError) {
        console.error(`Error fetching candidates for client ID ${clientId} from alternative endpoint:`, alternativeError);
        throw error;
      }
    }
  },

  // Get candidates by project ID
  getCandidatesByProjectId: async (projectId: string): Promise<Candidate[]> => {
    try {
      console.log(`Fetching candidates for project ID ${projectId} from API`);
      return await apiRequest<Candidate[]>(`/candidates/getByProjectId/${projectId}`);
    } catch (error) {
      console.error(`Error fetching candidates for project ID ${projectId}:`, error);
      // Try alternative endpoint if the first one fails
      try {
        console.log(`Trying alternative endpoint for candidates with project ID ${projectId}`);
        return await apiRequest<Candidate[]>(`/candidates/getByProjectId/${projectId}`);
      } catch (alternativeError) {
        console.error(`Error fetching candidates for project ID ${projectId} from alternative endpoint:`, alternativeError);
        throw error;
      }
    }
  },

  // Create a new candidate
  createCandidate: async (candidate: Candidate): Promise<Candidate> => {
    try {
      console.log('Creating new candidate:', candidate);

      // Try direct fetch through the proxy
      console.log('Trying direct fetch through proxy to /candidates/create');
      const response = await fetch('/candidates/create', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': getBasicAuthHeader()
        },
        body: JSON.stringify(candidate),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Direct fetch failed with status: ${response.status} - ${errorText}`);
        throw new Error(`Direct fetch failed with status: ${response.status} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating candidate with direct fetch:', error);

      // Try the API request approach as fallback
      try {
        console.log('Trying API request for creating candidate');
        return await apiRequest<Candidate>('/candidates/create', {
          method: 'POST',
          body: JSON.stringify(candidate),
        });
      } catch (apiError) {
        console.error('Error creating candidate with API request:', apiError);
        throw error;
      }
    }
  },

  // Update an existing candidate
  updateCandidate: async (id: string, candidate: Candidate): Promise<Candidate> => {
    try {
      console.log(`Updating candidate with ID ${id}:`, candidate);

      // Try direct fetch through the proxy first
      console.log(`Trying direct fetch through proxy to /candidates/update/${id}`);
      const response = await fetch(`/candidates/update/${id}`, {
        method: 'PUT',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': getBasicAuthHeader()
        },
        body: JSON.stringify(candidate),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Direct fetch failed with status: ${response.status} - ${errorText}`);
        throw new Error(`Direct fetch failed with status: ${response.status} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Error updating candidate with ID ${id} with direct fetch:`, error);

      // Try the API request approach as fallback
      try {
        console.log(`Trying API request for updating candidate with ID ${id}`);
        return await apiRequest<Candidate>(`/candidates/update/${id}`, {
          method: 'PUT',
          body: JSON.stringify(candidate),
        });
      } catch (apiError) {
        console.error(`Error updating candidate with ID ${id} with API request:`, apiError);
        throw error;
      }
    }
  },

  // Delete a candidate
  deleteCandidate: async (id: string): Promise<void> => {
    try {
      console.log(`Deleting candidate with ID ${id}`);
      await apiRequest<void>(`/candidates/deleteById/${id}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error(`Error deleting candidate with ID ${id}:`, error);
      // Try alternative endpoint if the first one fails
      try {
        console.log(`Trying alternative endpoint for deleting candidate with ID ${id}`);
        await apiRequest<void>(`/candidates/deleteById/${id}`, {
          method: 'DELETE',
        });
      } catch (alternativeError) {
        console.error(`Error deleting candidate with ID ${id} with alternative endpoint:`, alternativeError);
        throw error;
      }
    }
  },
};

export default candidateService;
