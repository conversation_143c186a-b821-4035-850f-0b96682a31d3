package com.redberyl.invoiceapp.exception;

import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.ConstraintViolation;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.exception.ConstraintViolationException;
import org.hibernate.exception.DataException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ErrorDetails> handleResourceNotFoundException(ResourceNotFoundException ex, WebRequest request) {
        ErrorDetails errorDetails = new ErrorDetails(
                LocalDateTime.now(),
                ex.getMessage(),
                request.getDescription(false),
                HttpStatus.NOT_FOUND.value()
        );
        return new ResponseEntity<>(errorDetails, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(NoContentException.class)
    public ResponseEntity<Void> handleNoContentException(NoContentException ex) {
        log.info("No content found: {}", ex.getMessage());
        return ResponseEntity.noContent().build();
    }

    @ExceptionHandler(UniqueConstraintViolationException.class)
    public ResponseEntity<ErrorDetails> handleUniqueConstraintViolation(
            UniqueConstraintViolationException ex, WebRequest request) {
        Map<String, String> errors = new HashMap<>();
        errors.put(ex.getFieldName(), ex.getMessage());

        ValidationErrorDetails errorDetails = new ValidationErrorDetails(
                LocalDateTime.now(),
                "Unique Constraint Violation",
                request.getDescription(false),
                701, // Custom status code for unique constraint violation
                errors
        );
        return ResponseEntity.status(701).body(errorDetails);
    }

    @ExceptionHandler(NullConstraintViolationException.class)
    public ResponseEntity<ErrorDetails> handleNullConstraintViolation(
            NullConstraintViolationException ex, WebRequest request) {
        Map<String, String> errors = new HashMap<>();
        errors.put(ex.getFieldName(), ex.getMessage());

        ValidationErrorDetails errorDetails = new ValidationErrorDetails(
                LocalDateTime.now(),
                "Null Constraint Violation",
                request.getDescription(false),
                700, // Custom status code for null constraint violation
                errors
        );
        return ResponseEntity.status(700).body(errorDetails);
    }

    @ExceptionHandler(ForeignKeyViolationException.class)
    public ResponseEntity<ErrorDetails> handleForeignKeyViolation(
            ForeignKeyViolationException ex, WebRequest request) {
        Map<String, String> errors = new HashMap<>();
        errors.put(ex.getFieldName(), ex.getMessage());

        ValidationErrorDetails errorDetails = new ValidationErrorDetails(
                LocalDateTime.now(),
                "Foreign Key Violation",
                request.getDescription(false),
                HttpStatus.BAD_REQUEST.value(),
                errors
        );
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorDetails);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, WebRequest request) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        ValidationErrorDetails errorDetails = new ValidationErrorDetails(
                LocalDateTime.now(),
                "Validation Failed",
                request.getDescription(false),
                HttpStatus.BAD_REQUEST.value(),
                errors
        );
        return new ResponseEntity<>(errorDetails, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(CustomException.class)
    public ResponseEntity<Object> handleCustomException(CustomException ex) {
        Throwable cause = ex.getCause();
        Map<String, Object> errorDetails = new HashMap<>();

        log.error("CustomException caught: {}", ex.getMessage(), ex);

        if (cause instanceof jakarta.validation.ConstraintViolationException validationEx) {
            Set<ConstraintViolation<?>> violations = validationEx.getConstraintViolations();
            StringBuilder errorMessage = new StringBuilder();

            for (ConstraintViolation<?> violation : violations) {
                errorMessage.append(violation.getPropertyPath())
                        .append(" ")
                        .append(violation.getMessage())
                        .append("; ");
            }
            errorDetails.put("error", errorMessage.toString().trim());

            log.warn("ConstraintViolationException handled with 701: {}", errorMessage);
            return ResponseEntity.status(701).body(errorDetails);
        }
        if (cause instanceof ConstraintViolationException) {
            String cleanMessage = extractDetailMessage(cause);
            errorDetails.put("error", cleanMessage);
            log.warn("ConstraintViolationException handled: {}", cleanMessage);
            if (cleanMessage.toLowerCase().contains("is not present in table")) {
                // Foreign key violation
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorDetails);
            }
            return ResponseEntity.status(700).body(errorDetails);
        }

        if (cause instanceof DataException) {
            String errorMsg = cause.getLocalizedMessage();
            errorDetails.put("error", errorMsg);
            log.warn("DataException (value too long etc.) handled: {}", errorMsg);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorDetails);
        }

        if (cause instanceof DataIntegrityViolationException) {
            String errorMsg = cause.getMessage();
            errorDetails.put("error", errorMsg);
            log.warn("DataIntegrityViolationException handled: {}", errorMsg);

            // Check if it's a unique constraint violation
            if (errorMsg != null &&
                (errorMsg.contains("unique constraint") ||
                 errorMsg.contains("duplicate key") ||
                 errorMsg.contains("Unique index"))) {
                return ResponseEntity.status(701).body(errorDetails); // Custom status code for unique constraint
            }

            return new ResponseEntity<>(errorDetails, HttpStatus.CONFLICT);
        }

        if (cause instanceof EntityNotFoundException) {
            String errorMsg = cause.getMessage();
            errorDetails.put("error", errorMsg);
            log.warn("EntityNotFoundException handled: {}", errorMsg);
            return new ResponseEntity<>(errorDetails, HttpStatus.NOT_FOUND);
        }

        String fallbackMessage = cause != null ? cause.getMessage() : ex.getMessage();
        errorDetails.put("error", fallbackMessage);
        log.error("Unhandled exception caught: {}", fallbackMessage, ex);
        return new ResponseEntity<>(errorDetails, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private String extractDetailMessage(Throwable e) {
        while (e != null) {
            String msg = e.getMessage();
            if (msg != null && msg.contains("Detail:")) {
                int start = msg.indexOf("Detail:");
                int end = msg.indexOf("Call getNextException");
                if (end > start) {
                    return msg.substring(start, end).trim();
                }
                return msg.substring(start).trim();
            }
            e = e.getCause();
        }
        return e != null ? e.getLocalizedMessage() : "Unknown error detail";
    }

    @ExceptionHandler(EmptyResultDataAccessException.class)
    public ResponseEntity<Void> handleEmptyResultDataAccessException(EmptyResultDataAccessException ex) {
        log.info("No data found: {}", ex.getMessage());
        return ResponseEntity.noContent().build();
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ErrorDetails> handleMethodArgumentTypeMismatch(
            MethodArgumentTypeMismatchException ex, WebRequest request) {
        String errorMessage = String.format("The parameter '%s' of value '%s' could not be converted to type '%s'",
                ex.getName(), ex.getValue(), ex.getRequiredType().getSimpleName());

        ErrorDetails errorDetails = new ErrorDetails(
                LocalDateTime.now(),
                errorMessage,
                request.getDescription(false),
                HttpStatus.BAD_REQUEST.value()
        );
        return new ResponseEntity<>(errorDetails, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(org.springframework.http.converter.HttpMessageNotReadableException.class)
    public ResponseEntity<ErrorDetails> handleHttpMessageNotReadable(
            org.springframework.http.converter.HttpMessageNotReadableException ex, WebRequest request) {

        log.error("JSON parsing error", ex);

        String errorMessage = "Invalid JSON format in request body";
        if (ex.getMessage() != null && ex.getMessage().contains("Unexpected character")) {
            errorMessage = "JSON syntax error: " + ex.getMessage();
        }

        ErrorDetails errorDetails = new ErrorDetails(
                LocalDateTime.now(),
                errorMessage,
                request.getDescription(false),
                HttpStatus.BAD_REQUEST.value()
        );
        return new ResponseEntity<>(errorDetails, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorDetails> handleGlobalException(Exception ex, WebRequest request) {
        log.error("Unhandled exception", ex);
        ErrorDetails errorDetails = new ErrorDetails(
                LocalDateTime.now(),
                ex.getMessage(),
                request.getDescription(false),
                HttpStatus.INTERNAL_SERVER_ERROR.value()
        );
        return new ResponseEntity<>(errorDetails, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}

