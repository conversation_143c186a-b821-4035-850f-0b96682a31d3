package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.DocumentVariableDto;

import java.util.List;

public interface DocumentVariableService {
    List<DocumentVariableDto> getAllDocumentVariables();
    DocumentVariableDto getDocumentVariableById(Long id);
    List<DocumentVariableDto> getDocumentVariablesByTemplateVersionId(Long templateVersionId);
    DocumentVariableDto createDocumentVariable(DocumentVariableDto documentVariableDto);
    DocumentVariableDto updateDocumentVariable(Long id, DocumentVariableDto documentVariableDto);
    void deleteDocumentVariable(Long id);
}
