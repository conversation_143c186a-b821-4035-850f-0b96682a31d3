package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Tax Rate creation request with nested tax type")
public class TaxRateCreateRequestDto {

    @Valid
    @NotNull(message = "Tax Type is required")
    @Schema(description = "Tax Type object with ID")
    private TaxTypeDto taxTypeId;

    @NotNull(message = "Rate is required")
    @Positive(message = "Rate must be positive")
    @Schema(description = "Tax rate percentage")
    private BigDecimal rate;

    @NotNull(message = "Effective From date is required")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "Date from which the tax rate is effective")
    private LocalDate effectiveFrom;

    @NotNull(message = "Effective To date is required")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "Date until which the tax rate is effective")
    private LocalDate effectiveTo;
}
