package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.TaxRateDto;
import com.redberyl.invoiceapp.service.TaxRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/tax-rates")
@Tag(name = "Tax Rate", description = "Tax Rate management API")
public class TaxRateController {

    @Autowired
    private TaxRateService taxRateService;

    @GetMapping
    @Operation(summary = "Get all tax rates", description = "Retrieve a list of all tax rates")
    public ResponseEntity<List<TaxRateDto>> getAllTaxRates() {
        List<TaxRateDto> taxRates = taxRateService.getAllTaxRates();
        return new ResponseEntity<>(taxRates, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get tax rate by ID", description = "Retrieve a tax rate by its ID")
    public ResponseEntity<TaxRateDto> getTaxRateById(@PathVariable Long id) {
        TaxRateDto taxRate = taxRateService.getTaxRateById(id);
        return new ResponseEntity<>(taxRate, HttpStatus.OK);
    }

    @GetMapping("/tax-type/{taxTypeId}")
    @Operation(summary = "Get tax rates by tax type ID", description = "Retrieve all tax rates for a specific tax type")
    public ResponseEntity<List<TaxRateDto>> getTaxRatesByTaxTypeId(@PathVariable Long taxTypeId) {
        List<TaxRateDto> taxRates = taxRateService.getTaxRatesByTaxTypeId(taxTypeId);
        return new ResponseEntity<>(taxRates, HttpStatus.OK);
    }

    @GetMapping("/effective-on")
    @Operation(summary = "Get tax rates effective on a date", description = "Retrieve all tax rates effective on a specific date")
    public ResponseEntity<List<TaxRateDto>> getTaxRatesEffectiveOnDate(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        List<TaxRateDto> taxRates = taxRateService.getTaxRatesEffectiveOnDate(date);
        return new ResponseEntity<>(taxRates, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create tax rate", description = "Create a new tax rate. Use the following format:\n\n```json\n{\n  \"taxTypeId\": 1,\n  \"rate\": 18.0,\n  \"effectiveFrom\": \"2025-04-17\",\n  \"effectiveTo\": \"2025-06-22\"\n}\n```\n\nNote: The ID is automatically generated and should not be included in the request.\nThe taxType object should not be included, only the taxTypeId.")
    public ResponseEntity<TaxRateDto> createTaxRate(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "Tax Rate creation request", required = true, content = @io.swagger.v3.oas.annotations.media.Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = TaxRateDto.class), examples = {
                    @io.swagger.v3.oas.annotations.media.ExampleObject(name = "Simple Tax Rate Example", summary = "Simple Tax Rate Example", description = "Example of creating a tax rate with only the required fields", value = "{\n  \"taxTypeId\": 1,\n  \"rate\": 18.0,\n  \"effectiveFrom\": \"2025-04-17\",\n  \"effectiveTo\": \"2025-06-22\"\n}")
            })) @Valid @RequestBody TaxRateDto taxRateDto) {
        TaxRateDto createdTaxRate = taxRateService.createTaxRate(taxRateDto);
        return new ResponseEntity<>(createdTaxRate, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update tax rate", description = "Update an existing tax rate")
    public ResponseEntity<TaxRateDto> updateTaxRate(@PathVariable Long id, @Valid @RequestBody TaxRateDto taxRateDto) {
        TaxRateDto updatedTaxRate = taxRateService.updateTaxRate(id, taxRateDto);
        return new ResponseEntity<>(updatedTaxRate, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete tax rate", description = "Delete a tax rate by its ID")
    public ResponseEntity<Void> deleteTaxRate(@PathVariable Long id) {
        taxRateService.deleteTaxRate(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
