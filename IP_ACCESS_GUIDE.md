# Accessing the Application via Localhost and IP Address

This guide explains how to access the application using both localhost and your machine's IP address (such as ************).

## Flexible Access Configuration

The application has been configured to support multiple access methods:

1. **Localhost Access**: You can access the application on your local machine using:
   - Frontend: `http://localhost:3060`
   - Backend: `http://*************:8091`

2. **IP Address Access**: You can access the application from other devices using your machine's IP address:
   - Frontend: `http://<YOUR_IP_ADDRESS>:3060` (e.g., `http://************:3060`)
   - Backend: `http://<YOUR_IP_ADDRESS>:8091` (e.g., `http://************:8091`)

Make sure your machine has this IP address assigned to one of its network interfaces. If not, you'll need to configure your network settings to add this IP address.

### Configuring the IP Address

We've provided scripts to help you configure the IP address:

#### Using the Setup Scripts

1. **For Windows**:
   - Right-click on `scripts/setup-ip.bat` and select "Run as administrator"
   - Follow the prompts to select your network interface

2. **For macOS/Linux**:
   - Open a terminal
   - Run `chmod +x scripts/setup-ip.sh` to make the script executable
   - Run `sudo ./scripts/setup-ip.sh`
   - Follow the prompts to select your network interface

#### Manual Configuration

If you prefer to configure the IP address manually:

##### Windows
1. Open Command Prompt as Administrator
2. Run the following command to add the IP address to your network interface:
   ```
   netsh interface ip add address "Ethernet" ************ *************
   ```
   Replace "Ethernet" with your actual network interface name (e.g., "Wi-Fi" if you're using Wi-Fi)

##### macOS
1. Open System Preferences > Network
2. Select your active network connection (e.g., Wi-Fi or Ethernet)
3. Click "Advanced" > "TCP/IP" tab
4. Click the "+" button under the IP addresses list
5. Add ************ and set the subnet mask to *************

##### Linux
1. Open a terminal
2. Run the following command to add the IP address temporarily:
   ```
   sudo ip addr add ************/24 dev eth0
   ```
   Replace eth0 with your actual network interface name (e.g., wlan0 for Wi-Fi)

You can verify the IP address is configured correctly by running:
- Windows: `ipconfig`
- macOS/Linux: `ifconfig` or `ip addr show`

## Starting the Application

1. **Start the Backend Server**:
   ```bash
   cd backend
   mvn spring-boot:run
   ```

2. **Start the Frontend Server**:
   ```bash
   cd frontend
   npm run dev
   ```

## Accessing from Other Devices

Once both servers are running, you can access the application from other devices on the same network:

- **Frontend**: `http://<YOUR_IP_ADDRESS>:3060`
- **Backend API**: `http://<YOUR_IP_ADDRESS>:8091`

Replace `<YOUR_IP_ADDRESS>` with your actual IP address (e.g., ************).

You can find your IP address by running:
- Windows: `ipconfig` in Command Prompt
- macOS: `ifconfig` in Terminal
- Linux: `ip addr show` in Terminal

Or use our helper script:
```bash
# From the frontend directory
npm run find-ip
```

## Troubleshooting

### Firewall Issues

If you can't access the application from other devices, check your firewall settings:

- Make sure ports 3060 (frontend) and 8091 (backend) are allowed through your firewall
- On Windows, you can check Windows Defender Firewall settings
- On macOS, check System Preferences > Security & Privacy > Firewall
- On Linux, check your distribution's firewall settings (e.g., `ufw` or `firewalld`)

### Network Issues

- Ensure both devices are on the same network
- Some public or corporate networks may block device-to-device communication
- Try using a mobile hotspot if you're having trouble with your main network

### CORS Issues

If the frontend can load but API calls fail, it might be a CORS issue:

1. Check the browser console for CORS errors
2. Verify that the backend CORS configuration is correctly set up
3. Make sure you're using the correct IP address in your API calls

## Technical Details

The application has been configured to support both localhost and IP address access:

1. **Backend Configuration**:
   - Listens on all network interfaces (0.0.0.0)
   - CORS is configured to allow requests from both localhost and specific IP addresses
   - All API endpoints are accessible from both localhost and IP addresses

2. **Frontend Configuration**:
   - Listens on all network interfaces (0.0.0.0)
   - Dynamically detects the current hostname for API calls
   - Automatically uses the correct backend URL based on how you access the frontend
   - Works seamlessly whether accessed via localhost or an IP address

3. **Smart API Routing**:
   - When you access the frontend via localhost, API calls go to localhost
   - When you access the frontend via IP address, API calls go to the same IP address
   - This ensures that cookies and authentication work correctly in all scenarios

This flexible configuration allows you to develop locally using localhost while also being able to access the application from other devices using your IP address.
