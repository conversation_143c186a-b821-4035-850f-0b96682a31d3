package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.TaxRateCreateRequestDto;
import com.redberyl.invoiceapp.dto.TaxRateDto;
import com.redberyl.invoiceapp.service.TaxRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/api/tax-rates")
@Tag(name = "Tax Rate Nested API", description = "API for managing tax rates with nested tax type objects")
public class TaxRateNestedController {

    @Autowired
    private TaxRateService taxRateService;

    @PostMapping
    @Operation(summary = "Create tax rate with nested tax type", description = "Create a new tax rate using a nested tax type object. Use the following format:\n\n```json\n{\n"
            +
            "  \"taxTypeId\": {\n" +
            "    \"id\": 2,\n" +
            "    \"taxType\": \"GST\",\n" +
            "    \"taxTypeDescription\": \"Goods and Services Tax\",\n" +
            "    \"created_at\": \"2025-04-22T12:20:31.705\",\n" +
            "    \"updated_at\": \"2025-04-22T12:20:31.705\"\n" +
            "  },\n" +
            "  \"rate\": 18,\n" +
            "  \"effectiveFrom\": \"2025-04-17\",\n" +
            "  \"effectiveTo\": \"2025-06-22\"\n" +
            "}\n```\n\n" +
            "Note: The tax type object will be included in the response.")
    public ResponseEntity<TaxRateDto> createTaxRateWithNestedTaxType(
            @Valid @RequestBody TaxRateCreateRequestDto requestDto) {

        // Convert the nested request to a standard TaxRateDto
        TaxRateDto taxRateDto = new TaxRateDto();
        taxRateDto.setTaxTypeId(requestDto.getTaxTypeId().getId());
        taxRateDto.setRate(requestDto.getRate());
        taxRateDto.setEffectiveFrom(requestDto.getEffectiveFrom());
        taxRateDto.setEffectiveTo(requestDto.getEffectiveTo());

        // Use the existing service to create the tax rate
        TaxRateDto createdTaxRate = taxRateService.createTaxRate(taxRateDto);

        // Set the full tax type object in the response
        createdTaxRate.setTaxType(requestDto.getTaxTypeId());

        return new ResponseEntity<>(createdTaxRate, HttpStatus.CREATED);
    }
}
