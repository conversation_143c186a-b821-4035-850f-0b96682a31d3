package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;

import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "spocs")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Spoc extends BaseEntity {

    @Id
    @SequenceGenerator(name = "spoc_seq", sequenceName = "spoc_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "spoc_seq")
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "email_id", nullable = false)
    private String emailId;

    @Column(name = "contact_no", nullable = false)
    private String contactNo;

    @OneToMany(mappedBy = "managerSpoc")
    private Set<Candidate> managedCandidates = new HashSet<>();

    @OneToMany(mappedBy = "accountHeadSpoc")
    private Set<Candidate> accountHeadCandidates = new HashSet<>();

    @OneToMany(mappedBy = "businessHeadSpoc")
    private Set<Candidate> businessHeadCandidates = new HashSet<>();

    @OneToMany(mappedBy = "hrSpoc")
    private Set<Candidate> hrCandidates = new HashSet<>();

    @OneToMany(mappedBy = "financeSpoc")
    private Set<Candidate> financeCandidates = new HashSet<>();
}
