package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.BdmPaymentDto;

import java.util.List;

public interface BdmPaymentService {
    List<BdmPaymentDto> getAllBdmPayments();
    BdmPaymentDto getBdmPaymentById(Long id);
    List<BdmPaymentDto> getBdmPaymentsByBdmId(Long bdmId);
    List<BdmPaymentDto> getBdmPaymentsByInvoiceId(Long invoiceId);
    BdmPaymentDto createBdmPayment(BdmPaymentDto bdmPaymentDto);
    BdmPaymentDto updateBdmPayment(Long id, BdmPaymentDto bdmPaymentDto);
    void deleteBdmPayment(Long id);
}
