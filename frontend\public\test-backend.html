<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <h1>Backend Test</h1>
    
    <div class="form-group">
        <label for="endpoint">Endpoint:</label>
        <input type="text" id="endpoint" value="/api/test/ping">
    </div>
    
    <div class="form-group">
        <label for="method">Method:</label>
        <select id="method">
            <option value="GET">GET</option>
            <option value="POST">POST</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="body">Request Body (JSON):</label>
        <textarea id="body" rows="5" style="width: 100%"></textarea>
    </div>
    
    <div class="form-group">
        <label for="credentials">Include Credentials:</label>
        <select id="credentials">
            <option value="include">include</option>
            <option value="same-origin">same-origin</option>
            <option value="omit">omit</option>
        </select>
    </div>
    
    <button onclick="testEndpoint()">Test Endpoint</button>
    <button onclick="testRoles()">Test Roles</button>
    <button onclick="createRoles()">Create Roles</button>
    <button onclick="testSignup()">Test Signup</button>
    
    <div id="result">
        <p>Results will appear here...</p>
    </div>
    
    <script>
        async function testEndpoint() {
            const endpoint = document.getElementById('endpoint').value;
            const method = document.getElementById('method').value;
            const body = document.getElementById('body').value;
            const credentials = document.getElementById('credentials').value;
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Sending request...</p>';
            
            try {
                const options = {
                    method: method,
                    credentials: credentials,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };
                
                if (method !== 'GET' && body.trim()) {
                    options.body = body;
                }
                
                const response = await fetch(endpoint, options);
                
                let responseText;
                try {
                    const responseJson = await response.json();
                    responseText = JSON.stringify(responseJson, null, 2);
                } catch (e) {
                    responseText = await response.text();
                }
                
                resultDiv.innerHTML = `
                    <h3>Response (${response.status} ${response.statusText})</h3>
                    <pre>${responseText}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>Error</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }
        
        function testRoles() {
            document.getElementById('endpoint').value = '/api/test/roles';
            document.getElementById('method').value = 'GET';
            document.getElementById('body').value = '';
            testEndpoint();
        }
        
        function createRoles() {
            document.getElementById('endpoint').value = '/api/test/create-roles';
            document.getElementById('method').value = 'GET';
            document.getElementById('body').value = '';
            testEndpoint();
        }
        
        function testSignup() {
            document.getElementById('endpoint').value = '/api/auth/signup';
            document.getElementById('method').value = 'POST';
            document.getElementById('body').value = JSON.stringify({
                username: "testuser" + Math.floor(Math.random() * 1000),
                email: "test" + Math.floor(Math.random() * 1000) + "@example.com",
                password: "password123",
                roles: ["user"]
            }, null, 2);
            testEndpoint();
        }
    </script>
</body>
</html>
