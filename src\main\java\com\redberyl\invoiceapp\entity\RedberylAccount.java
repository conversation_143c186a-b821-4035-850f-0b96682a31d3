package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;

import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "redberyl_accounts")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RedberylAccount extends BaseEntity {

    @Id
    @SequenceGenerator(name = "redberyl_account_seq", sequenceName = "redberyl_account_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "redberyl_account_seq")
    private Long id;

    @Column(name = "gl_code")
    private String glCode;

    @Column(name = "cost_center")
    private String costCenter;

    @Column(name = "accounting_notes")
    private String accountingNotes;

    @Column(name = "bank_name")
    private String bankName;

    @Column(name = "branch_name")
    private String branchName;

    @Column(name = "account_name")
    private String accountName;

    @Column(name = "account_no")
    private String accountNo;

    @Column(name = "ifsc_code")
    private String ifscCode;

    @Column(name = "account_type")
    private String accountType;

    @Column(name = "gstn")
    private String gstn;

    @Column(name = "cin")
    private String cin;

    @Column(name = "pan_no")
    private String panNo;

    @OneToMany(mappedBy = "redberylAccount")
    private Set<Invoice> invoices = new HashSet<>();
}
