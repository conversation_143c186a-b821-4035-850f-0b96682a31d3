package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.Candidate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CandidateRepository extends JpaRepository<Candidate, Long> {
    List<Candidate> findByClientId(Long clientId);
    List<Candidate> findByProjectId(Long projectId);
}
