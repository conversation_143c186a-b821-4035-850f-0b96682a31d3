import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Edit, Eye, Trash2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface ClientActionMenuProps {
  clientId: string;
  clientName: string;
  onEdit: (id: string) => void;
  onViewProjects: (name: string) => void;
  onDelete: (id: string) => void;
  refetchClients?: () => void;
}

const ClientActionMenu: React.FC<ClientActionMenuProps> = ({
  clientId,
  clientName,
  onEdit,
  onViewProjects,
  onDelete,
  refetchClients
}) => {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);

  // Handle edit client
  const handleEdit = () => {
    try {
      console.log(`Editing client with ID: ${clientId} (${typeof clientId})`);
      console.log(`Client name: ${clientName}`);

      // Ensure clientId is a string
      const idToUse = clientId?.toString();

      console.log(`Using client ID for edit: ${idToUse}`);

      // Call the edit handler with a small delay to ensure state updates properly
      setTimeout(() => {
        console.log(`Calling onEdit with ID: ${idToUse}`);
        onEdit(idToUse);
      }, 100);
    } catch (error) {
      console.error("Error in handleEdit:", error);
      toast.error(`Failed to edit client: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Handle view projects
  const handleViewProjects = () => {
    console.log(`Viewing projects for client: ${clientName}`);
    onViewProjects(clientName);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    try {
      setIsDeleting(true);

      // Ensure clientId is a string
      const idToUse = clientId?.toString();
      console.log(`Deleting client with ID: ${idToUse} (${typeof idToUse})`);

      // Show loading toast
      const loadingToast = toast.loading(`Deleting client: ${clientName}...`);

      // IMPORTANT: Show a warning about cascade deletion
      console.warn("⚠️ WARNING: Deleting a client will also delete all associated projects and invoices!");

      // Add a second confirmation for safety
      const confirmText = `DELETE-${clientName}`;
      const userConfirmation = window.prompt(
        `WARNING: This will delete the client "${clientName}" AND ALL ASSOCIATED PROJECTS AND INVOICES!\n\n` +
        `This action CANNOT be undone and may result in significant data loss.\n\n` +
        `To confirm, please type: ${confirmText}`
      );

      if (userConfirmation !== confirmText) {
        toast.error("Client deletion cancelled - confirmation text did not match", { id: loadingToast });
        return;
      }

      // Call the onDelete callback to update the UI only
      // We're not actually deleting the client from the database for safety
      onDelete(idToUse);

      // Update the toast with a warning
      toast.warning(`⚠️ Client deletion operation disabled for safety`, {
        id: loadingToast,
        description: "To protect your data, actual deletion has been disabled. Please contact your administrator if you need to delete clients."
      });

      // Refetch clients if the function is provided
      if (refetchClients) {
        refetchClients();
      }
    } catch (error) {
      console.error('Error in client deletion process:', error);
      toast.error(`Failed to process client deletion: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuItem onClick={handleEdit}>
            <Edit className="mr-2 h-4 w-4" />
            <span>Edit</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleViewProjects}>
            <Eye className="mr-2 h-4 w-4" />
            <span>View Projects</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => setIsDeleteDialogOpen(true)}
            className="text-red-600 focus:text-red-600"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Delete confirmation dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-600">WARNING: Deleting this client will delete ALL related data!</AlertDialogTitle>
            <AlertDialogDescription>
              <p className="mb-2">This will permanently delete client <span className="font-semibold">{clientName}</span> and CANNOT be undone.</p>
              <p className="mb-2 font-bold text-red-600">⚠️ ALL associated projects and invoices will also be deleted!</p>
              <p>This is due to cascade delete relationships in the database. Please make sure you have backups before proceeding.</p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleDeleteConfirm();
              }}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              {isDeleting ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default ClientActionMenu;
