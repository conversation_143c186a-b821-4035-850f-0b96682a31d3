import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIdentityReference;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import java.math.BigDecimal;
import java.time.LocalDate;

public class TaxRateExample {

    public static void main(String[] args) throws Exception {
        // Create a tax type
        TaxType taxType = new TaxType();
        taxType.setId(1L);
        taxType.setTaxType("GST");
        taxType.setTaxTypeDescription("Goods and Services Tax");

        // Create a tax rate
        TaxRate taxRate = new TaxRate();
        taxRate.setId(1L);
        taxRate.setRate(new BigDecimal("18.0"));
        taxRate.setEffectiveFrom(LocalDate.of(2025, 4, 17));
        taxRate.setEffectiveTo(LocalDate.of(2025, 6, 22));
        taxRate.setTaxType(taxType);

        // Create an ObjectMapper
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
        objectMapper.findAndRegisterModules(); // Register modules for LocalDate

        // Serialize the tax rate to JSON
        String json = objectMapper.writeValueAsString(taxRate);
        System.out.println(json);
    }

    // Tax Rate class
    @JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
    public static class TaxRate {
        private Long id;
        private BigDecimal rate;
        private LocalDate effectiveFrom;
        private LocalDate effectiveTo;
        
        @JsonProperty("taxType")
        private TaxType taxType;

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public BigDecimal getRate() { return rate; }
        public void setRate(BigDecimal rate) { this.rate = rate; }
        
        public LocalDate getEffectiveFrom() { return effectiveFrom; }
        public void setEffectiveFrom(LocalDate effectiveFrom) { this.effectiveFrom = effectiveFrom; }
        
        public LocalDate getEffectiveTo() { return effectiveTo; }
        public void setEffectiveTo(LocalDate effectiveTo) { this.effectiveTo = effectiveTo; }
        
        public TaxType getTaxType() { return taxType; }
        public void setTaxType(TaxType taxType) { this.taxType = taxType; }
    }

    // Tax Type class
    @JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
    public static class TaxType {
        private Long id;
        private String taxType;
        private String taxTypeDescription;

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getTaxType() { return taxType; }
        public void setTaxType(String taxType) { this.taxType = taxType; }
        
        public String getTaxTypeDescription() { return taxTypeDescription; }
        public void setTaxTypeDescription(String taxTypeDescription) { this.taxTypeDescription = taxTypeDescription; }
    }
}
