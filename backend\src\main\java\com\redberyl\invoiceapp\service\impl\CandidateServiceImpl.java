package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.CandidateDto;
import com.redberyl.invoiceapp.dto.ClientDto;
import com.redberyl.invoiceapp.dto.ProjectDto;
import com.redberyl.invoiceapp.entity.Candidate;
import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.entity.Project;
import com.redberyl.invoiceapp.entity.Spoc;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ForeignKeyViolationException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.CandidateRepository;
import com.redberyl.invoiceapp.repository.ClientRepository;
import com.redberyl.invoiceapp.repository.ProjectRepository;
import com.redberyl.invoiceapp.repository.SpocRepository;
import com.redberyl.invoiceapp.service.CandidateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class CandidateServiceImpl implements CandidateService {

    @Autowired
    private CandidateRepository candidateRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private SpocRepository spocRepository;

    @Override
    public List<CandidateDto> getAllCandidates() {
        List<Candidate> candidates = candidateRepository.findAll();
        if (candidates.isEmpty()) {
            throw new NoContentException("No candidates found");
        }
        return candidates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public CandidateDto getCandidateById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Candidate ID cannot be null");
        }

        return candidateRepository.findById(id)
                .map(this::convertToDto)
                .orElseThrow(() -> new ResourceNotFoundException("Candidate not found with id: " + id));
    }

    @Override
    public List<CandidateDto> getCandidatesByClientId(Long clientId) {
        if (clientId == null) {
            throw new NullConstraintViolationException("clientId", "Client ID cannot be null");
        }

        // Check if client exists
        if (!clientRepository.existsById(clientId)) {
            throw new ResourceNotFoundException("Client not found with id: " + clientId);
        }

        List<Candidate> candidates = candidateRepository.findByClientId(clientId);
        if (candidates.isEmpty()) {
            throw new NoContentException("No candidates found for client with id: " + clientId);
        }

        return candidates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<CandidateDto> getCandidatesByProjectId(Long projectId) {
        if (projectId == null) {
            throw new NullConstraintViolationException("projectId", "Project ID cannot be null");
        }

        // Check if project exists
        if (!projectRepository.existsById(projectId)) {
            throw new ResourceNotFoundException("Project not found with id: " + projectId);
        }

        List<Candidate> candidates = candidateRepository.findByProjectId(projectId);
        if (candidates.isEmpty()) {
            throw new NoContentException("No candidates found for project with id: " + projectId);
        }

        return candidates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private void validateCandidateDto(CandidateDto candidateDto) {
        if (candidateDto == null) {
            throw new NullConstraintViolationException("candidateDto", "Candidate data cannot be null");
        }

        if (!StringUtils.hasText(candidateDto.getName())) {
            throw new NullConstraintViolationException("name", "Candidate name cannot be empty");
        }

        // Validate client ID if provided
        if (candidateDto.getClientId() != null && !clientRepository.existsById(candidateDto.getClientId())) {
            throw new ForeignKeyViolationException("clientId",
                    "Client not found with id: " + candidateDto.getClientId());
        }

        // Validate project ID if provided
        if (candidateDto.getProjectId() != null && !projectRepository.existsById(candidateDto.getProjectId())) {
            throw new ForeignKeyViolationException("projectId",
                    "Project not found with id: " + candidateDto.getProjectId());
        }

        // SPOC validations removed as SPOC fields have been removed from Candidate entity
    }

    @Override
    @Transactional
    public CandidateDto createCandidate(CandidateDto candidateDto) {
        validateCandidateDto(candidateDto);

        try {
            Candidate candidate = convertToEntity(candidateDto);
            Candidate savedCandidate = candidateRepository.save(candidate);
            return convertToDto(savedCandidate);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("name", "Candidate name cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("name", "Candidate with this name already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error creating candidate: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating candidate", e);
        }
    }

    @Override
    @Transactional
    public CandidateDto updateCandidate(Long id, CandidateDto candidateDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Candidate ID cannot be null");
        }

        validateCandidateDto(candidateDto);

        Candidate existingCandidate = candidateRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Candidate not found with id: " + id));

        try {
            updateCandidateFromDto(existingCandidate, candidateDto);
            Candidate updatedCandidate = candidateRepository.save(existingCandidate);
            return convertToDto(updatedCandidate);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("name", "Candidate name cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("name", "Candidate with this name already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error updating candidate: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error updating candidate", e);
        }
    }

    @Override
    @Transactional
    public void deleteCandidate(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Candidate ID cannot be null");
        }

        if (!candidateRepository.existsById(id)) {
            throw new ResourceNotFoundException("Candidate not found with id: " + id);
        }

        try {
            candidateRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete candidate because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting candidate: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting candidate", e);
        }
    }

    private CandidateDto convertToDto(Candidate candidate) {
        CandidateDto dto = new CandidateDto();
        dto.setId(candidate.getId());

        // Set client ID and client object
        if (candidate.getClient() != null) {
            dto.setClientId(candidate.getClient().getId());

            // Create and set the client DTO
            ClientDto clientDto = ClientDto.builder()
                    .id(candidate.getClient().getId())
                    .name(candidate.getClient().getName())
                    .build();

            // Set audit fields for client
            clientDto.setCreatedAt(candidate.getClient().getCreatedAt());
            clientDto.setUpdatedAt(candidate.getClient().getModifiedAt());

            dto.setClient(clientDto);
        }

        // Set project ID and project object
        if (candidate.getProject() != null) {
            dto.setProjectId(candidate.getProject().getId());

            // Create and set the project DTO
            ProjectDto projectDto = ProjectDto.builder()
                    .id(candidate.getProject().getId())
                    .name(candidate.getProject().getName())
                    .build();

            // Set client ID in project if available
            if (candidate.getProject().getClient() != null) {
                projectDto.setClientId(candidate.getProject().getClient().getId());
            }

            // Set audit fields for project
            projectDto.setCreatedAt(candidate.getProject().getCreatedAt());
            projectDto.setUpdatedAt(candidate.getProject().getModifiedAt());

            dto.setProject(projectDto);
        }

        dto.setName(candidate.getName());
        dto.setJoiningDate(candidate.getJoiningDate());
        dto.setBillingRate(candidate.getBillingRate());
        dto.setDesignation(candidate.getDesignation());
        dto.setPanNo(candidate.getPanNo());
        dto.setAadharNo(candidate.getAadharNo());
        dto.setUanNo(candidate.getUanNo());
        dto.setExperienceInYrs(candidate.getExperienceInYrs());
        dto.setBankAccountNo(candidate.getBankAccountNo());
        dto.setBranchName(candidate.getBranchName());
        dto.setIfscCode(candidate.getIfscCode());
        dto.setAddress(candidate.getAddress());
        dto.setSalaryOffered(candidate.getSalaryOffered());
        dto.setSkills(candidate.getSkills());
        dto.setNotes(candidate.getNotes());
        return dto;
    }

    private Candidate convertToEntity(CandidateDto dto) {
        Candidate candidate = new Candidate();
        candidate.setId(dto.getId());

        if (dto.getClientId() != null) {
            Client client = clientRepository.findById(dto.getClientId())
                    .orElseThrow(() -> new ForeignKeyViolationException("clientId",
                            "Client not found with id: " + dto.getClientId()));
            candidate.setClient(client);
        }

        if (dto.getProjectId() != null) {
            Project project = projectRepository.findById(dto.getProjectId())
                    .orElseThrow(() -> new ForeignKeyViolationException("projectId",
                            "Project not found with id: " + dto.getProjectId()));
            candidate.setProject(project);
        }

        updateCandidateFromDto(candidate, dto);

        return candidate;
    }

    private void updateCandidateFromDto(Candidate candidate, CandidateDto dto) {
        candidate.setName(dto.getName());
        candidate.setJoiningDate(dto.getJoiningDate());
        candidate.setBillingRate(dto.getBillingRate());
        candidate.setDesignation(dto.getDesignation());
        candidate.setPanNo(dto.getPanNo());
        candidate.setAadharNo(dto.getAadharNo());
        candidate.setUanNo(dto.getUanNo());
        candidate.setExperienceInYrs(dto.getExperienceInYrs());
        candidate.setBankAccountNo(dto.getBankAccountNo());
        candidate.setBranchName(dto.getBranchName());
        candidate.setIfscCode(dto.getIfscCode());
        candidate.setAddress(dto.getAddress());
        candidate.setSalaryOffered(dto.getSalaryOffered());
        candidate.setSkills(dto.getSkills());
        candidate.setNotes(dto.getNotes());

        // Update client relationship
        if (dto.getClientId() != null) {
            Client client = clientRepository.findById(dto.getClientId())
                    .orElseThrow(() -> new ResourceNotFoundException("Client not found with id: " + dto.getClientId()));
            candidate.setClient(client);
        }

        // Update project relationship
        if (dto.getProjectId() != null) {
            Project project = projectRepository.findById(dto.getProjectId())
                    .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + dto.getProjectId()));
            candidate.setProject(project);
        }
    }
}
