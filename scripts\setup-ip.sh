#!/bin/bash

echo "==================================="
echo "IP Address Configuration for Unix"
echo "==================================="
echo ""
echo "This script will configure the IP address ************ on your network interface."
echo ""

# Check if running as root
if [ "$EUID" -ne 0 ]; then
  echo "Please run this script as root (sudo ./setup-ip.sh)"
  exit 1
fi

# Detect OS
if [[ "$OSTYPE" == "darwin"* ]]; then
  # macOS
  OS="macOS"
  INTERFACES=$(networksetup -listallnetworkservices | grep -v "An asterisk" | grep -v "Bluetooth")
else
  # Linux
  OS="Linux"
  INTERFACES=$(ip -o link show | awk -F': ' '{print $2}' | grep -v "lo")
fi

echo "Available network interfaces:"
echo ""

# Display interfaces
i=1
declare -a INTERFACE_ARRAY
for interface in $INTERFACES; do
  echo "$i. $interface"
  INTERFACE_ARRAY[$i]=$interface
  i=$((i+1))
done

echo ""
read -p "Enter your choice (1-$((i-1))): " choice

if [[ $choice -lt 1 || $choice -ge $i ]]; then
  echo "Invalid choice. Exiting."
  exit 1
fi

SELECTED_INTERFACE=${INTERFACE_ARRAY[$choice]}
echo ""
echo "You selected: $SELECTED_INTERFACE"
echo ""
echo "This will add the IP address ************ to your $SELECTED_INTERFACE interface."
echo ""
read -p "Do you want to continue? (Y/N): " confirm

if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
  echo "Operation cancelled. Exiting."
  exit 0
fi

echo ""
echo "Adding IP address ************ to $SELECTED_INTERFACE..."

if [[ "$OS" == "macOS" ]]; then
  # macOS
  networksetup -setmanualwithdhcprouter "$SELECTED_INTERFACE" ************ *************
  if [ $? -ne 0 ]; then
    echo ""
    echo "Failed to add IP address. Please check your network settings."
    exit 1
  fi
else
  # Linux
  ip addr add ************/24 dev "$SELECTED_INTERFACE"
  if [ $? -ne 0 ]; then
    echo ""
    echo "Failed to add IP address. Please check your network settings."
    exit 1
  fi
fi

echo ""
echo "IP address ************ has been added to $SELECTED_INTERFACE."
echo ""
echo "Verifying configuration..."

if [[ "$OS" == "macOS" ]]; then
  # macOS
  ifconfig | grep "************"
else
  # Linux
  ip addr show | grep "************"
fi

echo ""
echo "Configuration complete. You can now start the application."
echo ""
