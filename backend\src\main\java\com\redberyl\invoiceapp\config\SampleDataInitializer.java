package com.redberyl.invoiceapp.config;

import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.entity.Deal;
import com.redberyl.invoiceapp.entity.Lead;
import com.redberyl.invoiceapp.repository.ClientRepository;
import com.redberyl.invoiceapp.repository.DealRepository;
import com.redberyl.invoiceapp.repository.LeadRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

@Configuration
public class SampleDataInitializer {

    @Autowired
    private LeadRepository leadRepository;

    @Autowired
    private DealRepository dealRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Bean
    @Order(2) // Run after the main DataInitializer
    public CommandLineRunner initSampleData() {
        return args -> {
            try {
                // Initialize sample leads
                initializeLeads();

                // Initialize sample deals
                initializeDeals();

            } catch (Exception e) {
                System.err.println("Error initializing sample data: " + e.getMessage());
                e.printStackTrace();
            }
        };
    }

    private void initializeLeads() {
        // Only add sample data if the leads table is empty
        if (leadRepository.count() == 0) {
            System.out.println("Initializing sample lead data...");

            // Create sample leads
            List<Lead> sampleLeads = Arrays.asList(
                createLead("John Smith", "Tech Solutions Inc.", "<EMAIL>", "+****************", "Website", "New"),
                createLead("Sarah Johnson", "Digital Innovations", "<EMAIL>", "+****************", "Referral", "Contacted"),
                createLead("Robert Wilson", "GrowthCorp", "<EMAIL>", "+****************", "Trade Show", "Qualified"),
                createLead("Maria Garcia", "InnoTech Solutions", "<EMAIL>", "+****************", "LinkedIn", "New"),
                createLead("James Johnson", "Future Systems", "<EMAIL>", "+****************", "Email Campaign", "Not Interested")
            );

            // Save all leads
            leadRepository.saveAll(sampleLeads);
            System.out.println("Sample lead data initialized successfully!");
        } else {
            System.out.println("Leads table already has data, skipping sample lead initialization.");
        }
    }

    private void initializeDeals() {
        // Only add sample data if the deals table is empty
        if (dealRepository.count() == 0) {
            System.out.println("Initializing sample deal data...");

            // Create sample deals for each status
            createSampleDeal("Acme Corp Software Implementation", "lead", new BigDecimal("12500.00"));
            createSampleDeal("Globex IT Infrastructure", "lead", new BigDecimal("24000.00"));
            createSampleDeal("Wayne Enterprises Website Redesign", "qualified", new BigDecimal("18750.00"));
            createSampleDeal("Stark Industries Cloud Migration", "proposal", new BigDecimal("45000.00"));
            createSampleDeal("Oscorp ERP Implementation", "negotiation", new BigDecimal("32000.00"));
            createSampleDeal("LexCorp Security Audit", "closed", new BigDecimal("15000.00"));

            System.out.println("Sample deal data initialized successfully!");
        } else {
            System.out.println("Deals table already has data, skipping sample deal initialization.");
        }
    }

    private Lead createLead(String name, String company, String email, String phone, String source, String status) {
        Lead lead = new Lead();
        lead.setName(name);
        lead.setCompany(company);
        lead.setEmail(email);
        lead.setPhone(phone);
        lead.setSource(source);
        lead.setStatus(status);
        return lead;
    }

    /**
     * Helper method to create a sample deal
     */
    private void createSampleDeal(String projectName, String status, BigDecimal value) {
        Deal deal = new Deal();
        deal.setProjectName(projectName);
        deal.setStatus(status);
        deal.setValueEstimate(value);
        deal.setExpectedClosureDate(LocalDate.now().plusMonths(1));
        deal.setNotes("This is a sample deal created for testing purposes");

        // Try to find a client to associate with the deal
        List<Client> clients = clientRepository.findAll();
        if (!clients.isEmpty()) {
            deal.setClient(clients.get(0));
        }

        // Try to find a lead to associate with the deal
        List<Lead> leads = leadRepository.findAll();
        if (!leads.isEmpty()) {
            deal.setLead(leads.get(0));
        }

        dealRepository.save(deal);
    }
}
