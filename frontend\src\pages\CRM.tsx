
import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  Plus,
  PhoneCall,
  Mail,
  Calendar,
  Users,
  Building,
  User,
  MoreHorizontal,
  Edit,
  Trash,
  Loader2,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import KanbanBoard, { Deal as KanbanDeal, ColumnData } from "@/components/crm/KanbanBoard";
import LeadStatusDropdown from "@/components/leads/LeadStatusDropdown";
import LeadFormDialog, { LeadFormData } from "@/components/leads/LeadFormDialog";
import LeadActionMenu, { Lead as LeadType } from "@/components/leads/LeadActionMenu";
import DealFormDialog, { DealFormData } from "@/components/deals/DealFormDialog";
import { toast } from "sonner";
import { leadService } from "@/services/leadService";
import { dealService, Deal } from "@/services/dealService";

const CRM = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [leads, setLeads] = useState<LeadType[]>([]);
  const [filteredLeads, setFilteredLeads] = useState<LeadType[]>([]);
  const [isAddLeadDialogOpen, setIsAddLeadDialogOpen] = useState(false);
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);
  const [currentLead, setCurrentLead] = useState<LeadType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch leads from the backend
  useEffect(() => {
    const fetchLeads = async () => {
      try {
        setIsLoading(true);
        const data = await leadService.getLeads();
        setLeads(data);
        setFilteredLeads(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching leads:', err);
        setError('Failed to load leads. Please try again later.');
        toast.error('Failed to load leads');
      } finally {
        setIsLoading(false);
      }
    };

    fetchLeads();
  }, []);

  // Deal management state
  const [isDealFormOpen, setIsDealFormOpen] = useState(false);
  const [currentDeal, setCurrentDeal] = useState<KanbanDeal | null>(null);
  const [isLoadingDeals, setIsLoadingDeals] = useState(true);
  const [dealError, setDealError] = useState<string | null>(null);

  // Initialize dealColumns with an empty structure
  const initialDealColumns: ColumnData = {
    lead: [],
    qualified: [],
    proposal: [],
    negotiation: [],
    closed: []
  };

  const [dealColumns, setDealColumns] = useState<ColumnData>(initialDealColumns);

  // Fetch deals from the backend
  useEffect(() => {
    const fetchDeals = async () => {
      try {
        setIsLoadingDeals(true);
        console.log('Fetching deals from the database...');

        try {
          // Get deals from the database via API
          const deals = await dealService.getAllDeals();
          console.log('Fetched deals from database:', deals);

          // Group deals by status
          const groupedDeals: ColumnData = {
            lead: [],
            qualified: [],
            proposal: [],
            negotiation: [],
            closed: []
          };

          // Check if deals is an array
          if (Array.isArray(deals)) {
            console.log(`Processing ${deals.length} deals from the database`);

            // Convert database deals to UI deals and group by status
            deals.forEach(deal => {
              // Determine the status - use the status from the deal or default to 'lead'
              // Also normalize the status to match our column keys
              let status = (deal.status || 'lead').toLowerCase();

              // Map any similar statuses to our standard ones
              if (status.includes('lead') || status === 'new' || status === 'open') {
                status = 'lead';
              } else if (status.includes('qual')) {
                status = 'qualified';
              } else if (status.includes('prop') || status.includes('offer')) {
                status = 'proposal';
              } else if (status.includes('neg') || status.includes('disc')) {
                status = 'negotiation';
              } else if (status.includes('clos') || status.includes('won') || status.includes('comp')) {
                status = 'closed';
              }

              // Create UI deal object
              const uiDeal: KanbanDeal = {
                id: deal.id?.toString() || '',
                title: deal.projectName || deal.project_name || 'Untitled Deal',
                client: typeof deal.client === 'string' ? deal.client :
                       deal.client?.name || 'Client', // Try to get client name from nested object
                value: typeof deal.valueEstimate === 'number'
                  ? `$${deal.valueEstimate.toLocaleString()}`
                  : typeof deal.valueEstimate === 'object' && deal.valueEstimate !== null
                    ? `$${parseFloat(deal.valueEstimate.toString()).toLocaleString()}`
                    : deal.valueEstimate?.toString() || '$0',
                dueDate: deal.expectedClosureDate || new Date().toISOString().split('T')[0],
                assignedTo: deal.lead?.name || 'Unassigned', // Try to get lead name
                notes: deal.notes || '',
                status: status
              };

              console.log(`Processing deal: ${uiDeal.title} with status: ${status}`);

              // Add to the appropriate column
              if (groupedDeals[status]) {
                groupedDeals[status].push(uiDeal);
              } else {
                // If status doesn't match any predefined column, add to lead
                if (!groupedDeals.lead) groupedDeals.lead = [];
                groupedDeals.lead.push(uiDeal);
              }
            });

            // Log the grouped deals for debugging
            Object.keys(groupedDeals).forEach(column => {
              console.log(`Column ${column} has ${groupedDeals[column].length} deals`);
            });

          }

          // Always set the columns and clear the error, even if the array is empty
          setDealColumns(groupedDeals);
          setDealError(null);
        } catch (apiError) {
          console.error('Error fetching deals from database:', apiError);
          setDealError('Failed to load deals from database. Please check if the backend is running.');
          toast.error('Failed to load deals from database');
        }
      } catch (err) {
        console.error('Unexpected error in fetchDeals:', err);
        setDealError('An unexpected error occurred. Please try again later.');
        toast.error('Failed to load deals');
      } finally {
        setIsLoadingDeals(false);
      }
    };

    fetchDeals();
  }, []);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value.toLowerCase();
    setSearchTerm(term);

    const filtered = leads.filter(
      (lead) =>
        lead.name.toLowerCase().includes(term) ||
        lead.company.toLowerCase().includes(term) ||
        lead.email.toLowerCase().includes(term) ||
        lead.status.toLowerCase().includes(term) ||
        lead.source.toLowerCase().includes(term)
    );

    setFilteredLeads(filtered);
  };

  const handleStatusChange = async (id: string, newStatus: string) => {
    try {
      // Show loading toast
      toast.loading('Updating lead status...');

      // Call the API to update the lead status
      await leadService.updateLeadStatus(id, newStatus);

      // Update the lead status in the state
      const updatedLeads = leads.map((lead) =>
        lead.id === id ? { ...lead, status: newStatus } : lead
      );

      setLeads(updatedLeads);
      setFilteredLeads(
        filteredLeads.map((lead) =>
          lead.id === id ? { ...lead, status: newStatus } : lead
        )
      );

      // Show a success toast
      toast.success(`Lead status updated to "${newStatus}"`, {
        description: `Lead ID: ${id}`,
      });
    } catch (error) {
      console.error('Error updating lead status:', error);
      toast.error('Failed to update lead status. Please try again.');
    }
  };

  const handleAddLead = (leadData: LeadFormData) => {
    // The lead is already saved to the backend by the LeadFormDialog component
    // We just need to update our local state with the returned lead that includes the ID

    // Add the new lead to the state
    const updatedLeads = [leadData, ...leads];
    setLeads(updatedLeads);

    // Update filtered leads if search is active
    if (searchTerm) {
      const updatedFiltered = [leadData, ...filteredLeads];
      setFilteredLeads(updatedFiltered);
    } else {
      setFilteredLeads(updatedLeads);
    }
  };

  const handleEditLead = (lead: Lead) => {
    // Set the current lead and open the edit form dialog
    setCurrentLead(lead);
    setIsEditFormOpen(true);

    // Show a toast notification that we're editing the lead
    toast.info(`Editing lead: ${lead.name}`, {
      description: `Lead ID: ${lead.id}`,
    });
  };

  const handleConvertToDeal = (lead: Lead) => {
    // In a real app, you would create a new deal from the lead
    toast.success(`Lead ${lead.name} converted to deal`, {
      description: `A new deal has been created from this lead.`,
    });

    // Remove the lead from our leads list (or mark as converted)
    const updatedLeads = leads.filter((l) => l.id !== lead.id);
    setLeads(updatedLeads);
    setFilteredLeads(filteredLeads.filter((l) => l.id !== lead.id));

    // Navigate to the deals tab
    const dealsTab = document.querySelector('[data-value="deals"]') as HTMLElement;
    if (dealsTab) {
      dealsTab.click();
    }
  };

  const handleDeleteLead = async (leadId: string) => {
    try {
      // Show loading toast
      toast.loading('Deleting lead...');

      // Call the API to delete the lead
      await leadService.deleteLead(leadId);

      // Remove the lead from our state
      const updatedLeads = leads.filter((lead) => lead.id !== leadId);
      setLeads(updatedLeads);
      setFilteredLeads(filteredLeads.filter((lead) => lead.id !== leadId));

      toast.success("Lead deleted successfully", {
        description: `Lead ID: ${leadId} has been removed.`,
      });
    } catch (error) {
      console.error('Error deleting lead:', error);
      toast.error('Failed to delete lead. Please try again.');
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case "new":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "contacted":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "qualified":
        return "bg-green-100 text-green-800 border-green-200";
      case "not interested":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  // Deal management functions
  const handleAddDeal = async (dealData: DealFormData) => {
    try {
      // Show loading toast
      const loadingToast = toast.loading('Creating new deal...');

      // Prepare the deal data for the API
      const dealToCreate: Partial<Deal> = {
        projectName: dealData.title,
        clientId: dealData.client_id,
        valueEstimate: dealData.value,
        expectedClosureDate: dealData.dueDate,
        status: dealData.status || 'lead',
        notes: dealData.notes
      };

      // Add client data if available
      if (dealData.client_name) {
        dealToCreate.client = {
          name: dealData.client_name
        };
      }

      console.log('Sending deal data to API:', dealToCreate);

      try {
        // Call the API to create the deal
        const createdDeal = await dealService.createDeal(dealToCreate);
        console.log('Deal created successfully:', createdDeal);

        // Dismiss the loading toast
        toast.dismiss(loadingToast);

        // Create the UI deal object
        const newDeal: KanbanDeal = {
          id: createdDeal.id?.toString() || `deal-${Date.now()}`,
          title: dealData.title,
          client: dealData.client_name || `Client #${dealData.client_id}`,
          value: dealData.value,
          dueDate: dealData.dueDate,
          assignedTo: dealData.assignedTo || "Unassigned",
          notes: dealData.notes,
          status: dealData.status || 'lead'
        };

        // Add the new deal to the appropriate column
        const status = newDeal.status || 'lead';
        const updatedColumns = { ...(dealColumns || {}) };

        if (!updatedColumns[status]) {
          updatedColumns[status] = [];
        }

        updatedColumns[status] = [newDeal, ...(updatedColumns[status] || [])];
        setDealColumns(updatedColumns);

        // Show success toast
        toast.success("Deal added successfully", {
          description: `${dealData.title} - ${dealData.value}`,
        });

        // Refresh the deals data to ensure we have the latest data from the server
        refreshDeals();
      } catch (apiError) {
        console.error('API Error creating deal:', apiError);
        toast.dismiss(loadingToast);
        toast.error('Failed to create deal. Please check the console for details.');
      }
    } catch (error) {
      console.error('Error in handleAddDeal:', error);
      toast.error('An unexpected error occurred. Please try again.');
    }
  };

  // Function to refresh deals data from the server
  const refreshDeals = async () => {
    try {
      setIsLoadingDeals(true);
      console.log('Refreshing deals from database...');

      try {
        // Get deals from the database via API
        const deals = await dealService.getAllDeals();
        console.log('Refreshed deals from database:', deals);

        // Group deals by status
        const groupedDeals: ColumnData = {
          lead: [],
          qualified: [],
          proposal: [],
          negotiation: [],
          closed: []
        };

        // Check if deals is an array
        if (Array.isArray(deals)) {
          console.log(`Processing ${deals.length} deals from the database`);

          // Convert database deals to UI deals and group by status
          deals.forEach(deal => {
            // Determine the status - use the status from the deal or default to 'lead'
            // Also normalize the status to match our column keys
            let status = (deal.status || 'lead').toLowerCase();

            // Map any similar statuses to our standard ones
            if (status.includes('lead') || status === 'new' || status === 'open') {
              status = 'lead';
            } else if (status.includes('qual')) {
              status = 'qualified';
            } else if (status.includes('prop') || status.includes('offer')) {
              status = 'proposal';
            } else if (status.includes('neg') || status.includes('disc')) {
              status = 'negotiation';
            } else if (status.includes('clos') || status.includes('won') || status.includes('comp')) {
              status = 'closed';
            }

            // Create UI deal object
            const uiDeal: KanbanDeal = {
              id: deal.id?.toString() || '',
              title: deal.projectName || deal.project_name || 'Untitled Deal',
              client: typeof deal.client === 'string' ? deal.client :
                     deal.client?.name || 'Client', // Try to get client name from nested object
              value: typeof deal.valueEstimate === 'number'
                ? `$${deal.valueEstimate.toLocaleString()}`
                : typeof deal.valueEstimate === 'object' && deal.valueEstimate !== null
                  ? `$${parseFloat(deal.valueEstimate.toString()).toLocaleString()}`
                  : deal.valueEstimate?.toString() || '$0',
              dueDate: deal.expectedClosureDate || new Date().toISOString().split('T')[0],
              assignedTo: deal.lead?.name || 'Unassigned', // Try to get lead name
              notes: deal.notes || '',
              status: status
            };

            // Add to the appropriate column
            if (groupedDeals[status]) {
              groupedDeals[status].push(uiDeal);
            } else {
              // If status doesn't match any predefined column, add to lead
              if (!groupedDeals.lead) groupedDeals.lead = [];
              groupedDeals.lead.push(uiDeal);
            }
          });

        }

        // Always set the columns and clear the error, even if the array is empty
        setDealColumns(groupedDeals);
        setDealError(null);
      } catch (apiError) {
        console.error('Error refreshing deals from database:', apiError);
        setDealError('Failed to refresh deals from database. Please check if the backend is running.');
      }
    } catch (err) {
      console.error('Unexpected error in refreshDeals:', err);
      // Don't show an error toast here to avoid annoying the user
    } finally {
      setIsLoadingDeals(false);
    }
  };

  const handleEditDeal = (deal: KanbanDeal) => {
    try {
      console.log('Edit deal handler called in CRM page:', deal);

      // Make sure we have a valid deal object
      if (!deal || !deal.id) {
        console.error('Invalid deal object:', deal);
        toast.error('Cannot edit deal: Invalid deal data');
        return;
      }

      // Set the current deal and open the edit form dialog
      setCurrentDeal(deal);
      setIsDealFormOpen(true);

      console.log('Edit form should be open now, isDealFormOpen:', true);
    } catch (error) {
      console.error('Error in handleEditDeal:', error);
      toast.error('Failed to open edit form', {
        description: 'Please try again or refresh the page',
        duration: 5000
      });
    }
  };

  const handleUpdateDeal = async (updatedDealData: DealFormData) => {
    if (!currentDeal) return;

    try {
      // Show loading toast
      const loadingToast = toast.loading('Updating deal...');

      // Prepare the deal data for the API
      const dealToUpdate: Partial<Deal> = {
        projectName: updatedDealData.title,
        clientId: updatedDealData.client_id,
        valueEstimate: updatedDealData.value,
        expectedClosureDate: updatedDealData.dueDate,
        status: updatedDealData.status || currentDeal.status,
        notes: updatedDealData.notes
      };

      // Add client data if available
      if (updatedDealData.client_name) {
        dealToUpdate.client = {
          name: updatedDealData.client_name
        };
      }

      console.log(`Updating deal with ID ${currentDeal.id}:`, dealToUpdate);

      try {
        // Call the API to update the deal
        const updatedDealFromApi = await dealService.updateDeal(currentDeal.id, dealToUpdate);
        console.log('Deal updated successfully:', updatedDealFromApi);

        // Dismiss the loading toast
        toast.dismiss(loadingToast);

        // Create the updated UI deal object
        const updatedDeal: KanbanDeal = {
          ...currentDeal,
          title: updatedDealData.title,
          client: updatedDealData.client_name || `Client #${updatedDealData.client_id}`,
          value: updatedDealData.value,
          dueDate: updatedDealData.dueDate,
          assignedTo: updatedDealData.assignedTo || currentDeal.assignedTo || "Unassigned",
          notes: updatedDealData.notes,
          status: updatedDealData.status || currentDeal.status
        };

        // Update the deal in the columns
        const oldStatus = currentDeal.status || 'lead';
        const newStatus = updatedDeal.status || 'lead';
        const updatedColumns = { ...(dealColumns || {}) };

        // If status changed, move to new column
        if (oldStatus !== newStatus) {
          // Remove from old column
          updatedColumns[oldStatus] = updatedColumns[oldStatus].filter(
            (d) => d.id !== currentDeal.id
          );

          // Add to new column
          if (!updatedColumns[newStatus]) {
            updatedColumns[newStatus] = [];
          }
          updatedColumns[newStatus] = [...updatedColumns[newStatus], updatedDeal];
        } else {
          // Update in the same column
          updatedColumns[oldStatus] = updatedColumns[oldStatus].map((d) =>
            d.id === currentDeal.id ? updatedDeal : d
          );
        }

        setDealColumns(updatedColumns);

        // Show success toast
        toast.success(`Deal updated successfully`, {
          description: `${updatedDeal.title} - ${updatedDeal.value}`,
        });

        // Reset current deal
        setCurrentDeal(null);

        // Refresh the deals data to ensure we have the latest data from the server
        refreshDeals();
      } catch (apiError) {
        console.error(`API Error updating deal with ID ${currentDeal.id}:`, apiError);
        toast.dismiss(loadingToast);
        toast.error('Failed to update deal. Please check the console for details.');
      }
    } catch (error) {
      console.error(`Error in handleUpdateDeal for deal ID ${currentDeal.id}:`, error);
      toast.error('An unexpected error occurred. Please try again.');
    }
  };

  const handleDeleteDeal = async (dealId: string) => {
    try {
      // Find which column contains the deal
      let dealColumn = '';
      let dealTitle = '';

      // Search through all columns to find the deal
      Object.keys(dealColumns).forEach((column) => {
        const foundDeal = dealColumns[column].find((deal) => deal.id === dealId);
        if (foundDeal) {
          dealColumn = column;
          dealTitle = foundDeal.title;
        }
      });

      if (!dealColumn) {
        toast.error("Could not find deal to delete");
        return;
      }

      // Show loading toast
      const loadingToast = toast.loading('Deleting deal...');

      // Try multiple endpoints for deleting the deal
      // Avoid public endpoint as it may return sample data
      const endpoints = [
        // Use authenticated endpoints only
        `http://*************:8091/api/deals/${dealId}`,
        `http://*************:8091/api/deals/deleteById/${dealId}`,
        `http://*************:8091/api/v1/deals/${dealId}`,
        `http://*************:8091/api/v1/deals/deleteById/${dealId}`
      ];

      let deleteSuccess = false;

      // Try each endpoint until one succeeds
      for (const endpoint of endpoints) {
        try {
          // Try both with and without authentication
          const headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          };

          // Add auth header
          const authHeaders = {
            ...headers,
            'Authorization': 'Basic ' + btoa('admin:admin123')
          };

          // First try with auth for regular endpoints, without auth for public endpoints
          const useAuth = !endpoint.includes('/public');

          console.log(`DealService: Using ${useAuth ? 'authenticated' : 'public'} request for ${endpoint}`);

          const response = await fetch(endpoint, {
            method: 'DELETE',
            headers: useAuth ? authHeaders : headers,
            credentials: useAuth ? 'include' : 'omit'
          });

          if (response.ok) {
            console.log(`Deal with ID ${dealId} deleted successfully from API using endpoint ${endpoint}`);
            deleteSuccess = true;
            break;
          } else {
            console.warn(`Failed to delete deal with ID ${dealId} using endpoint ${endpoint}: ${response.status}`);
          }
        } catch (endpointError) {
          console.error(`Error deleting deal with ID ${dealId} using endpoint ${endpoint}:`, endpointError);
        }
      }

      // Dismiss the loading toast
      toast.dismiss(loadingToast);

      if (!deleteSuccess) {
        console.warn(`Could not delete deal with ID ${dealId} from API, but will update UI anyway`);
      }

      // Remove the deal from our state
      const updatedColumns = { ...dealColumns };
      updatedColumns[dealColumn] = updatedColumns[dealColumn].filter(
        (deal) => deal.id !== dealId
      );

      setDealColumns(updatedColumns);

      toast.success("Deal deleted successfully", {
        description: dealTitle ? `"${dealTitle}" has been removed` : `Deal ID: ${dealId} has been removed`,
      });

      // Refresh the deals data to ensure we have the latest data from the server
      refreshDeals();
    } catch (error) {
      console.error(`Error in handleDeleteDeal for deal ID ${dealId}:`, error);
      toast.error('An error occurred while deleting the deal');
    }
  };

  const handleDealMoved = async (deal: KanbanDeal, sourceColumn: string, destColumn: string) => {
    try {
      // Show loading toast
      const loadingToast = toast.loading(`Moving deal to ${destColumn}...`);

      // Prepare the deal data for the API
      const dealToUpdate: Partial<Deal> = {
        projectName: deal.title,
        status: destColumn,
        notes: deal.notes,
        // We need to include these fields to maintain the existing data
        valueEstimate: deal.value,
        expectedClosureDate: deal.dueDate
      };

      console.log(`Updating deal status for ID ${deal.id} from ${sourceColumn} to ${destColumn}`);

      // Call the API to update the deal status
      try {
        await dealService.updateDeal(deal.id, dealToUpdate);
        console.log(`Deal status updated successfully to ${destColumn}`);

        // Dismiss the loading toast
        toast.dismiss(loadingToast);

        // Show success toast
        toast.success(`Deal moved to ${destColumn}`, {
          description: `${deal.title} - ${deal.value}`,
        });

        // Refresh the deals data to ensure we have the latest data from the server
        // Use a slight delay to allow the UI to update first
        setTimeout(() => {
          refreshDeals();
        }, 1000);
      } catch (apiError) {
        console.error(`Error updating deal status in API:`, apiError);

        // Dismiss the loading toast
        toast.dismiss(loadingToast);

        // Show error toast
        toast.error('Failed to update deal status in the database, but UI has been updated.');
      }
    } catch (error) {
      console.error(`Error moving deal:`, error);
      toast.error('Failed to update deal status. The UI may be out of sync with the database.');
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">CRM</h2>
        <p className="text-muted-foreground">Manage your leads, deals, and customer relationships.</p>
      </div>

      {/* Add Lead Form Dialog */}
      <LeadFormDialog
        open={isAddLeadDialogOpen}
        onOpenChange={setIsAddLeadDialogOpen}
        onSave={handleAddLead}
      />

      {/* Edit Lead Form Dialog */}
      {currentLead && (
        <LeadFormDialog
          open={isEditFormOpen}
          onOpenChange={setIsEditFormOpen}
          onSave={(updatedLead) => {
            // The lead is already updated in the backend by the LeadFormDialog component
            // We just need to update our local state with the returned lead

            // Update the lead in the state
            const updatedLeads = leads.map((lead) =>
              lead.id === currentLead.id ? updatedLead : lead
            );
            setLeads(updatedLeads);
            setFilteredLeads(
              filteredLeads.map((lead) =>
                lead.id === currentLead.id ? updatedLead : lead
              )
            );

            // Reset current lead
            setCurrentLead(null);
          }}
          initialData={currentLead}
          title="Edit Lead"
        />
      )}

      {/* Add Deal Form Dialog */}
      <DealFormDialog
        open={isDealFormOpen}
        onOpenChange={setIsDealFormOpen}
        onSave={currentDeal ? handleUpdateDeal : handleAddDeal}
        initialData={currentDeal ? {
          title: currentDeal.title,
          client_id: 0, // We'll try to find the client ID based on the name
          client_name: currentDeal.client,
          value: currentDeal.value,
          dueDate: currentDeal.dueDate,
          status: currentDeal.status,
          notes: currentDeal.notes,
          assignedTo: currentDeal.assignedTo
        } : undefined}
        title={currentDeal ? "Edit Deal" : "Add New Deal"}
      />

      <Tabs defaultValue="deals">
        <TabsList className="w-full sm:w-auto">
          <TabsTrigger value="leads" className="flex-1 sm:flex-initial">Leads</TabsTrigger>
          <TabsTrigger value="deals" className="flex-1 sm:flex-initial">Deals</TabsTrigger>
        </TabsList>

        <TabsContent value="leads" className="space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search leads..."
                className="pl-8 w-full md:w-[300px]"
                value={searchTerm}
                onChange={handleSearch}
              />
            </div>
            <Button
              onClick={() => setIsAddLeadDialogOpen(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Lead
            </Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Leads</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-hidden">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Company</TableHead>
                        <TableHead>Contact</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Source</TableHead>
                        <TableHead>Date Added</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-10">
                            <div className="flex flex-col items-center justify-center">
                              <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                              <span className="text-muted-foreground">Loading leads...</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : error ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-10 text-red-500">
                            <div className="flex flex-col items-center justify-center">
                              <span>{error}</span>
                              <Button
                                variant="outline"
                                className="mt-4"
                                onClick={() => window.location.reload()}
                              >
                                Retry
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : filteredLeads.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                            {searchTerm ? "No leads match your search" : "No leads found. Add your first lead!"}
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredLeads.map((lead) => (
                          <TableRow key={lead.id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center">
                                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center mr-2">
                                  <User className="h-4 w-4 text-primary" />
                                </div>
                                {lead.name}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Building className="h-4 w-4 mr-1 text-muted-foreground" />
                                {lead.company}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-col space-y-1">
                                <div className="flex items-center">
                                  <Mail className="h-3 w-3 mr-1 text-muted-foreground" />
                                  <span className="text-xs">{lead.email}</span>
                                </div>
                                <div className="flex items-center">
                                  <PhoneCall className="h-3 w-3 mr-1 text-muted-foreground" />
                                  <span className="text-xs">{lead.phone}</span>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <LeadStatusDropdown
                                currentStatus={lead.status}
                                onStatusChange={(newStatus) => handleStatusChange(String(lead.id), newStatus)}
                              />
                            </TableCell>
                            <TableCell>{lead.source}</TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Calendar className="h-3 w-3 mr-1 text-muted-foreground" />
                                {new Date(lead.dateAdded || lead.createdAt || '').toLocaleDateString()}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <LeadActionMenu
                                lead={lead}
                                onEdit={handleEditLead}
                                onConvertToDeal={handleConvertToDeal}
                                onDelete={handleDeleteLead}
                              />
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="deals">
          <Card>
            <CardHeader className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
              <CardTitle>Deals Pipeline</CardTitle>
              <Button onClick={() => {
                setCurrentDeal(null);
                setIsDealFormOpen(true);
              }}>
                <Plus className="mr-2 h-4 w-4" />
                New Deal
              </Button>
            </CardHeader>
            <CardContent className="px-2 sm:px-6 pb-6 overflow-x-auto">
              {isLoadingDeals ? (
                <div className="flex flex-col items-center justify-center py-10">
                  <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                  <span className="text-muted-foreground">Loading deals...</span>
                </div>
              ) : dealError ? (
                <div className="flex flex-col items-center justify-center py-10 text-red-500">
                  <span>{dealError}</span>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => {
                      setIsLoadingDeals(true);
                      // Force a refresh of the deals data
                      dealService.getAllDeals()
                        .then(deals => {
                          console.log('Retry: Fetched deals from API:', deals);

                          // Group deals by status
                          const groupedDeals: ColumnData = {
                            lead: [],
                            qualified: [],
                            proposal: [],
                            negotiation: [],
                            closed: []
                          };

                          // Check if deals is an array
                          if (Array.isArray(deals)) {
                            // Convert API deals to UI deals and group by status
                            deals.forEach(deal => {
                              // Determine the status - use the status from the deal or default to 'lead'
                              // Also normalize the status to match our column keys
                              let status = (deal.status || 'lead').toLowerCase();

                              // Map any similar statuses to our standard ones
                              if (status.includes('lead') || status === 'new' || status === 'open') {
                                status = 'lead';
                              } else if (status.includes('qual')) {
                                status = 'qualified';
                              } else if (status.includes('prop') || status.includes('offer')) {
                                status = 'proposal';
                              } else if (status.includes('neg') || status.includes('disc')) {
                                status = 'negotiation';
                              } else if (status.includes('clos') || status.includes('won') || status.includes('comp')) {
                                status = 'closed';
                              }

                              // Create UI deal object
                              const uiDeal: KanbanDeal = {
                                id: deal.id?.toString() || '',
                                title: deal.projectName || '',
                                client: deal.client?.name || 'Client',
                                value: typeof deal.valueEstimate === 'number'
                                  ? `$${deal.valueEstimate.toLocaleString()}`
                                  : deal.valueEstimate?.toString() || '$0',
                                dueDate: deal.expectedClosureDate || '',
                                assignedTo: deal.lead?.name || 'Unassigned',
                                notes: deal.notes || '',
                                status: status
                              };

                              // Add to the appropriate column
                              if (groupedDeals[status]) {
                                groupedDeals[status].push(uiDeal);
                              } else {
                                // If status doesn't match any predefined column, add to lead
                                if (!groupedDeals.lead) groupedDeals.lead = [];
                                groupedDeals.lead.push(uiDeal);
                              }
                            });
                          }

                          // Always set the columns and clear the error, even if the array is empty
                          setDealColumns(groupedDeals);
                          setDealError(null);
                        })
                        .catch(err => {
                          console.error('Error retrying deals fetch:', err);
                          setDealError('Failed to load deals. Please try again later.');
                          toast.error('Failed to load deals');
                        })
                        .finally(() => {
                          setIsLoadingDeals(false);
                        });
                    }}
                  >
                    Retry
                  </Button>
                </div>
              ) : Object.values(dealColumns).flat().length === 0 ? (
                <div className="flex flex-col items-center justify-center py-10">
                  <div className="text-center mb-6">
                    <h3 className="text-lg font-semibold mb-2">No Deals Found</h3>
                    <p className="text-muted-foreground mb-4">
                      There are no deals in the database. Create your first deal to get started.
                    </p>
                    <Button
                      onClick={() => {
                        setCurrentDeal(null);
                        setIsDealFormOpen(true);
                      }}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Create First Deal
                    </Button>
                  </div>
                </div>
              ) : (
                <KanbanBoard
                  externalDeals={dealColumns}
                  onEditDeal={handleEditDeal}
                  onDeleteDeal={handleDeleteDeal}
                  onDealMoved={handleDealMoved}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CRM;
