package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.InvoiceTypeDto;

import java.util.List;

public interface InvoiceTypeService {
    List<InvoiceTypeDto> getAllInvoiceTypes();
    InvoiceTypeDto getInvoiceTypeById(Long id);
    InvoiceTypeDto getInvoiceTypeByType(String invoiceType);
    InvoiceTypeDto createInvoiceType(InvoiceTypeDto invoiceTypeDto);
    InvoiceTypeDto updateInvoiceType(Long id, InvoiceTypeDto invoiceTypeDto);
    void deleteInvoiceType(Long id);
}
