import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Edit } from "lucide-react";
import { Deal } from '@/components/crm/KanbanBoard';
import './edit-deal-button.css';

interface EditDealButtonProps {
  deal: Deal;
  onEdit: (deal: Deal) => void;
}

const EditDealButton: React.FC<EditDealButtonProps> = ({ deal, onEdit }) => {
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();

    console.log('Edit button clicked for deal:', deal);

    // Call the edit handler
    onEdit(deal);
  };

  return (
    <Button
      variant="outline"
      size="sm"
      className="edit-deal-button h-8 px-3 text-amber-600 hover:bg-amber-50 hover:text-amber-700 border-amber-300 hover:border-amber-500"
      onClick={handleClick}
      title={`Edit ${deal.title}`}
      data-deal-id={deal.id}
    >
      <Edit className="h-4 w-4 mr-1" />
      <span>Edit</span>
    </Button>
  );
};

export default EditDealButton;
