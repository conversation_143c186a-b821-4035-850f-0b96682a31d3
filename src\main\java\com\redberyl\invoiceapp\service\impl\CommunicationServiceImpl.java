package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.ClientDto;
import com.redberyl.invoiceapp.dto.CommunicationDto;
import com.redberyl.invoiceapp.dto.LeadDto;
import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.entity.Communication;
import com.redberyl.invoiceapp.entity.Deal;
import com.redberyl.invoiceapp.entity.Lead;
import com.redberyl.invoiceapp.exception.EntityNotFoundException;
import com.redberyl.invoiceapp.repository.ClientRepository;
import com.redberyl.invoiceapp.repository.CommunicationRepository;
import com.redberyl.invoiceapp.repository.DealRepository;
import com.redberyl.invoiceapp.repository.LeadRepository;
import com.redberyl.invoiceapp.service.CommunicationService;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class CommunicationServiceImpl implements CommunicationService {

    private final CommunicationRepository communicationRepository;
    private final ClientRepository clientRepository;
    private final LeadRepository leadRepository;
    private final DealRepository dealRepository;

    @Autowired
    public CommunicationServiceImpl(CommunicationRepository communicationRepository,
            ClientRepository clientRepository,
            LeadRepository leadRepository,
            DealRepository dealRepository) {
        this.communicationRepository = communicationRepository;
        this.clientRepository = clientRepository;
        this.leadRepository = leadRepository;
        this.dealRepository = dealRepository;
    }

    @Override
    public List<CommunicationDto> getAllCommunications() {
        List<Communication> communications = communicationRepository.findAll();
        return communications.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public CommunicationDto getCommunicationById(Long id) {
        Communication communication = communicationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Communication not found with id: " + id));
        return convertToDto(communication);
    }

    @Override
    public List<CommunicationDto> getCommunicationsByClientId(Long clientId) {
        List<Communication> communications = communicationRepository.findByClientId(clientId);
        return communications.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<CommunicationDto> getCommunicationsByLeadId(Long leadId) {
        List<Communication> communications = communicationRepository.findByLeadId(leadId);
        return communications.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<CommunicationDto> getCommunicationsByDealId(Long dealId) {
        List<Communication> communications = communicationRepository.findByDealId(dealId);
        return communications.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public CommunicationDto createCommunication(CommunicationDto communicationDto) {
        Communication communication = convertToEntity(communicationDto);
        Communication savedCommunication = communicationRepository.save(communication);
        return convertToDto(savedCommunication);
    }

    @Override
    @Transactional
    public CommunicationDto updateCommunication(Long id, CommunicationDto communicationDto) {
        Communication existingCommunication = communicationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Communication not found with id: " + id));

        updateCommunicationFromDto(existingCommunication, communicationDto);

        Communication updatedCommunication = communicationRepository.save(existingCommunication);
        return convertToDto(updatedCommunication);
    }

    @Override
    @Transactional
    public void deleteCommunication(Long id) {
        if (!communicationRepository.existsById(id)) {
            throw new EntityNotFoundException("Communication not found with id: " + id);
        }

        communicationRepository.deleteById(id);
    }

    private CommunicationDto convertToDto(Communication communication) {
        CommunicationDto dto = new CommunicationDto();
        dto.setId(communication.getId());
        dto.setClientId(communication.getClient() != null ? communication.getClient().getId() : null);
        dto.setLeadId(communication.getLead() != null ? communication.getLead().getId() : null);
        dto.setDealId(communication.getDeal() != null ? communication.getDeal().getId() : null);
        dto.setSubject(communication.getSubject());
        dto.setMethod(communication.getMethod());
        dto.setContent(communication.getContent());
        dto.setFollowUpDate(communication.getFollowUpDate());
        dto.setLoggedAt(communication.getLoggedAt());

        // Set client object
        if (communication.getClient() != null) {
            ClientDto clientDto = ClientDto.builder()
                    .id(communication.getClient().getId())
                    .name(communication.getClient().getName())
                    .build();

            // Set audit fields for client
            clientDto.setCreatedAt(communication.getClient().getCreatedAt());
            clientDto.setUpdatedAt(communication.getClient().getModifiedAt());

            dto.setClient(clientDto);
        }

        // Set lead object
        if (communication.getLead() != null) {
            LeadDto leadDto = LeadDto.builder()
                    .id(communication.getLead().getId())
                    .name(communication.getLead().getName())
                    .email(communication.getLead().getEmail())
                    .phone(communication.getLead().getPhone())
                    .build();

            // Set audit fields for lead
            leadDto.setCreatedAt(communication.getLead().getCreatedAt());
            leadDto.setUpdatedAt(communication.getLead().getModifiedAt());

            dto.setLead(leadDto);
        }

        // Deal relationship removed as requested - no DealDto field to set

        // Set audit fields
        dto.setCreatedAt(communication.getCreatedAt());
        dto.setUpdatedAt(communication.getModifiedAt());

        return dto;
    }

    private Communication convertToEntity(CommunicationDto communicationDto) {
        Communication communication = new Communication();
        communication.setId(communicationDto.getId());

        updateCommunicationFromDto(communication, communicationDto);

        return communication;
    }

    private void updateCommunicationFromDto(Communication communication, CommunicationDto communicationDto) {
        if (communicationDto.getClientId() != null) {
            Client client = clientRepository.findById(communicationDto.getClientId())
                    .orElseThrow(() -> new EntityNotFoundException(
                            "Client not found with id: " + communicationDto.getClientId()));
            communication.setClient(client);
        }

        if (communicationDto.getLeadId() != null) {
            Lead lead = leadRepository.findById(communicationDto.getLeadId())
                    .orElseThrow(() -> new EntityNotFoundException(
                            "Lead not found with id: " + communicationDto.getLeadId()));
            communication.setLead(lead);
        }

        if (communicationDto.getDealId() != null) {
            Deal deal = dealRepository.findById(communicationDto.getDealId())
                    .orElseThrow(() -> new EntityNotFoundException(
                            "Deal not found with id: " + communicationDto.getDealId()));
            communication.setDeal(deal);
        }

        communication.setSubject(communicationDto.getSubject());
        communication.setMethod(communicationDto.getMethod());
        communication.setContent(communicationDto.getContent());
        communication.setFollowUpDate(communicationDto.getFollowUpDate());
        communication.setLoggedAt(communicationDto.getLoggedAt());
    }
}

