package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.ClientDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.ClientService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * Public controller for clients that doesn't require authentication
 * This is useful for testing and development purposes
 */
@RestController
@RequestMapping("/clients/public")
@Tag(name = "Public Client API", description = "Public API for clients (no authentication required)")
@CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060"}, maxAge = 3600)
public class PublicClientController {

    @Autowired
    private ClientService clientService;

    @GetMapping
    @Operation(summary = "Get all clients (public)", description = "Get all clients without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Clients found"),
            @ApiResponse(responseCode = "204", description = "No clients found", content = @Content)
    })
    public ResponseEntity<List<ClientDto>> getAllClients() {
        try {
            List<ClientDto> clients = clientService.getAllClients();
            return new ResponseEntity<>(clients, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            // Return empty list instead of error for better user experience
            return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get client by ID (public)", description = "Get client by ID without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Client found"),
            @ApiResponse(responseCode = "404", description = "Client not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    public ResponseEntity<ClientDto> getClientById(@PathVariable Long id) {
        try {
            ClientDto client = clientService.getClientById(id);
            return new ResponseEntity<>(client, HttpStatus.OK);
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/sample")
    @Operation(summary = "Get sample clients", description = "This endpoint no longer returns sample data")
    public ResponseEntity<List<ClientDto>> getSampleClients() {
        System.out.println("Sample clients endpoint called, returning empty list");
        // Return empty list instead of sample data
        return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
    }
}
