<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Signup Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, select {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 100px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>Direct Signup Test</h1>
    <p>This is a simplified signup form that directly communicates with the backend.</p>
    
    <form id="signupForm">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" required>
        </div>
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" required>
        </div>
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" required>
        </div>
        <div class="form-group">
            <label for="confirmPassword">Confirm Password:</label>
            <input type="password" id="confirmPassword" required>
        </div>
        <div class="form-group">
            <label for="role">Role:</label>
            <select id="role">
                <option value="user">User</option>
                <option value="admin">Admin</option>
            </select>
        </div>
        <div class="form-group">
            <label for="backendUrl">Backend URL:</label>
            <select id="backendUrl">
                <option value="http://*************:8091/api/auth/signup">localhost:8091</option>
                <option value="http://127.0.0.1:8091/api/auth/signup">127.0.0.1:8091</option>
                <option value="http://************:8091/api/auth/signup">************:8091</option>
            </select>
        </div>
        <button type="submit">Sign Up</button>
    </form>
    
    <div id="result" class="result"></div>
    
    <script>
        document.getElementById('signupForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Processing...';
            
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const role = document.getElementById('role').value;
            const backendUrl = document.getElementById('backendUrl').value;
            
            // Validate passwords match
            if (password !== confirmPassword) {
                resultDiv.innerHTML = '<p class="error">Passwords do not match!</p>';
                return;
            }
            
            try {
                resultDiv.innerHTML += `<p>Sending request to: ${backendUrl}</p>`;
                
                const response = await fetch(backendUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Origin': window.location.origin
                    },
                    mode: 'cors',
                    credentials: 'omit',
                    body: JSON.stringify({
                        username,
                        email,
                        password,
                        roles: [role]
                    })
                });
                
                resultDiv.innerHTML += `<p>Response status: ${response.status} ${response.statusText}</p>`;
                
                // Log all response headers for debugging
                const headers = {};
                response.headers.forEach((value, key) => {
                    headers[key] = value;
                });
                resultDiv.innerHTML += `<p>Response headers: ${JSON.stringify(headers)}</p>`;
                
                // Try to parse the response body
                try {
                    const data = await response.json();
                    resultDiv.innerHTML += `<p class="${response.ok ? 'success' : 'error'}">Response: ${JSON.stringify(data)}</p>`;
                } catch (parseError) {
                    resultDiv.innerHTML += `<p class="error">Error parsing response: ${parseError.message}</p>`;
                    
                    // Try to get the response text instead
                    try {
                        const text = await response.text();
                        resultDiv.innerHTML += `<p>Response text: ${text}</p>`;
                    } catch (textError) {
                        resultDiv.innerHTML += `<p class="error">Error getting response text: ${textError.message}</p>`;
                    }
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">Error: ${error.message}</p>`;
                console.error('Fetch error:', error);
            }
        });
    </script>
</body>
</html>
