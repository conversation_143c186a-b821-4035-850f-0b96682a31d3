package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.SpocDto;
import com.redberyl.invoiceapp.entity.Spoc;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.SpocRepository;
import com.redberyl.invoiceapp.service.SpocService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class SpocServiceImpl implements SpocService {

    @Autowired
    private SpocRepository spocRepository;

    @Override
    public List<SpocDto> getAllSpocs() {
        List<Spoc> spocs = spocRepository.findAll();
        if (spocs.isEmpty()) {
            throw new NoContentException("No SPOCs found");
        }
        return spocs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public SpocDto getSpocById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "SPOC ID cannot be null");
        }

        Spoc spoc = spocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("SPOC not found with id: " + id));
        return convertToDto(spoc);
    }

    @Override
    public SpocDto getSpocByEmailId(String emailId) {
        if (!StringUtils.hasText(emailId)) {
            throw new NullConstraintViolationException("emailId", "Email ID cannot be empty");
        }

        Spoc spoc = spocRepository.findByEmailId(emailId)
                .orElseThrow(() -> new ResourceNotFoundException("SPOC not found with email: " + emailId));
        return convertToDto(spoc);
    }

    private void validateSpocDto(SpocDto spocDto) {
        if (spocDto == null) {
            throw new NullConstraintViolationException("spocDto", "SPOC data cannot be null");
        }

        if (!StringUtils.hasText(spocDto.getName())) {
            throw new NullConstraintViolationException("name", "SPOC name cannot be empty");
        }

        if (!StringUtils.hasText(spocDto.getEmailId())) {
            throw new NullConstraintViolationException("emailId", "SPOC email ID cannot be empty");
        }

        // Check for duplicate email if it's a new SPOC
        if (spocDto.getId() == null &&
                spocRepository.findByEmailId(spocDto.getEmailId()).isPresent()) {
            throw new UniqueConstraintViolationException("emailId",
                    "SPOC with email " + spocDto.getEmailId() + " already exists");
        }
    }

    @Override
    @Transactional
    public SpocDto createSpoc(SpocDto spocDto) {
        validateSpocDto(spocDto);

        try {
            Spoc spoc = convertToEntity(spocDto);
            Spoc savedSpoc = spocRepository.save(spoc);
            return convertToDto(savedSpoc);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("emailId", "SPOC with this email already exists");
            } else {
                throw new CustomException("Error creating SPOC: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating SPOC", e);
        }
    }

    @Override
    @Transactional
    public SpocDto updateSpoc(Long id, SpocDto spocDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "SPOC ID cannot be null");
        }

        if (spocDto == null) {
            throw new NullConstraintViolationException("spocDto", "SPOC data cannot be null");
        }

        Spoc existingSpoc = spocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("SPOC not found with id: " + id));

        // Check for duplicate email if it's being changed
        if (StringUtils.hasText(spocDto.getEmailId()) &&
                !spocDto.getEmailId().equals(existingSpoc.getEmailId()) &&
                spocRepository.findByEmailId(spocDto.getEmailId()).isPresent()) {
            throw new UniqueConstraintViolationException("emailId",
                    "SPOC with email " + spocDto.getEmailId() + " already exists");
        }

        try {
            if (StringUtils.hasText(spocDto.getName())) {
                existingSpoc.setName(spocDto.getName());
            }

            if (StringUtils.hasText(spocDto.getEmailId())) {
                existingSpoc.setEmailId(spocDto.getEmailId());
            }

            if (spocDto.getContactNo() != null) {
                existingSpoc.setContactNo(spocDto.getContactNo());
            }

            Spoc updatedSpoc = spocRepository.save(existingSpoc);
            return convertToDto(updatedSpoc);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("emailId", "SPOC with this email already exists");
            } else {
                throw new CustomException("Error updating SPOC: " + e.getMessage(), e);
            }
        } catch (ResourceNotFoundException | NullConstraintViolationException | UniqueConstraintViolationException e) {
            throw e;
        } catch (Exception e) {
            throw new CustomException("Error updating SPOC", e);
        }
    }

    @Override
    @Transactional
    public void deleteSpoc(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "SPOC ID cannot be null");
        }

        if (!spocRepository.existsById(id)) {
            throw new ResourceNotFoundException("SPOC not found with id: " + id);
        }

        try {
            spocRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete SPOC because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting SPOC: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting SPOC", e);
        }
    }

    private SpocDto convertToDto(Spoc spoc) {
        return SpocDto.builder()
                .id(spoc.getId())
                .name(spoc.getName())
                .emailId(spoc.getEmailId())
                .contactNo(spoc.getContactNo())
                .build();
    }

    private Spoc convertToEntity(SpocDto spocDto) {
        return Spoc.builder()
                .id(spocDto.getId())
                .name(spocDto.getName())
                .emailId(spocDto.getEmailId())
                .contactNo(spocDto.getContactNo())
                .build();
    }
}
