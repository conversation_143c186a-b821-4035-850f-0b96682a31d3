package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.ReminderDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.ReminderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Tag(name = "Reminder", description = "Reminder management API")
public class ReminderController {

    @Autowired
    private ReminderService reminderService;

    @GetMapping("/reminders/getAll")
    @Operation(summary = "Get all reminders", description = "Get all reminders")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Reminders found"),
            @ApiResponse(responseCode = "204", description = "No reminders found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<ReminderDto>> getAllReminders() {
        try {
            List<ReminderDto> reminders = reminderService.getAllReminders();
            return new ResponseEntity<>(reminders, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/reminders/getById/{id}")
    @Operation(summary = "Get reminder by ID", description = "Get reminder by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Reminder found"),
            @ApiResponse(responseCode = "404", description = "Reminder not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<ReminderDto> getReminderById(@PathVariable Long id) {
        ReminderDto reminder = reminderService.getReminderById(id);
        return new ResponseEntity<>(reminder, HttpStatus.OK);
    }

    @GetMapping("/reminders/getByInvoiceId/{invoiceId}")
    @Operation(summary = "Get reminders by invoice ID", description = "Get reminders by invoice ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<ReminderDto>> getRemindersByInvoiceId(@PathVariable Long invoiceId) {
        List<ReminderDto> reminders = reminderService.getRemindersByInvoiceId(invoiceId);
        return new ResponseEntity<>(reminders, HttpStatus.OK);
    }

    @GetMapping("/reminders/getByMethod/{method}")
    @Operation(summary = "Get reminders by method", description = "Get reminders by method")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<ReminderDto>> getRemindersByMethod(@PathVariable String method) {
        List<ReminderDto> reminders = reminderService.getRemindersByMethod(method);
        return new ResponseEntity<>(reminders, HttpStatus.OK);
    }

    @GetMapping("/reminders/getByStatus/{status}")
    @Operation(summary = "Get reminders by status", description = "Get reminders by status")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<ReminderDto>> getRemindersByStatus(@PathVariable String status) {
        List<ReminderDto> reminders = reminderService.getRemindersByStatus(status);
        return new ResponseEntity<>(reminders, HttpStatus.OK);
    }

    @PostMapping("/reminders/create")
    @Operation(summary = "Create reminder", description = "Create reminder")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<ReminderDto> createReminder(@Valid @RequestBody ReminderDto reminderDto) {
        ReminderDto createdReminder = reminderService.createReminder(reminderDto);
        return new ResponseEntity<>(createdReminder, HttpStatus.CREATED);
    }

    @PutMapping("/reminders/update/{id}")
    @Operation(summary = "Update reminder", description = "Update reminder")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<ReminderDto> updateReminder(@PathVariable Long id,
            @Valid @RequestBody ReminderDto reminderDto) {
        ReminderDto updatedReminder = reminderService.updateReminder(id, reminderDto);
        return new ResponseEntity<>(updatedReminder, HttpStatus.OK);
    }

    @DeleteMapping("/reminders/deleteById/{id}")
    @Operation(summary = "Delete reminder", description = "Delete reminder")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteReminder(@PathVariable Long id) {
        reminderService.deleteReminder(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
