import React from "react";
import { toast } from "sonner";

interface ExactActionMenuProps {
  onDownload?: () => void;
  onEdit?: () => void;
  onViewHistory?: () => void;
  onDelete?: () => void;
  isVisible: boolean;
}

const ExactActionMenu: React.FC<ExactActionMenuProps> = ({
  onDownload,
  onEdit,
  onViewHistory,
  onDelete,
  isVisible
}) => {
  if (!isVisible) return null;

  const handleDownload = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDownload) {
      onDownload();
    } else {
      toast.success("Downloading document");
    }
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEdit) {
      onEdit();
    } else {
      toast.info("Editing variables");
    }
  };

  const handleViewHistory = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onViewHistory) {
      onViewHistory();
    } else {
      toast.info("Viewing version history");
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete();
    } else {
      toast.success("Document deleted");
    }
  };

  return (
    <div 
      className="bg-white rounded-md shadow-lg border p-0 z-50 w-48 action-menu"
      onClick={(e) => e.stopPropagation()}
      style={{ 
        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        position: "absolute",
        right: "0",
        top: "100%",
        marginTop: "8px"
      }}
    >
      <div className="text-sm font-medium p-2 border-b">Actions</div>
      <div className="flex flex-col py-1">
        <button
          className="flex items-center text-sm hover:bg-gray-50 px-4 py-2 w-full text-left"
          onClick={handleDownload}
        >
          <svg className="h-4 w-4 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
          </svg>
          Download
        </button>
        
        <button
          className="flex items-center text-sm hover:bg-gray-50 px-4 py-2 w-full text-left"
          onClick={handleEdit}
        >
          <svg className="h-4 w-4 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
          </svg>
          Edit Variables
        </button>
        
        <button
          className="flex items-center text-sm hover:bg-gray-50 px-4 py-2 w-full text-left"
          onClick={handleViewHistory}
        >
          <svg className="h-4 w-4 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
          Version History
        </button>
        
        <button
          className="flex items-center text-sm text-red-600 hover:bg-gray-50 px-4 py-2 w-full text-left"
          onClick={handleDelete}
        >
          <svg className="h-4 w-4 mr-3 text-red-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <polyline points="3 6 5 6 21 6"></polyline>
            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
            <line x1="10" y1="11" x2="10" y2="17"></line>
            <line x1="14" y1="11" x2="14" y2="17"></line>
          </svg>
          Delete
        </button>
      </div>
    </div>
  );
};

export default ExactActionMenu;
