package com.redberyl.invoiceapp.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.PathItem;
import io.swagger.v3.oas.models.Paths;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Custom OpenAPI customizer to fix path issues in Swagger documentation
 */
@Configuration
public class SwaggerPathCustomizer {
    
    private static final Logger logger = Logger.getLogger(SwaggerPathCustomizer.class.getName());
    
    @Bean
    public OpenApiCustomizer pathCustomizer() {
        return openApi -> {
            // Get the existing paths
            Paths paths = openApi.getPaths();
            if (paths == null) {
                return;
            }
            
            // Create a new map to store corrected paths
            Map<String, PathItem> correctedPaths = new HashMap<>();
            
            // Check for and fix specific path issues
            paths.forEach((path, pathItem) -> {
                // Fix the "leasds" typo if it exists
                if (path.contains("/leasds")) {
                    String correctedPath = path.replace("/leasds", "/leads");
                    logger.info("Correcting Swagger path from " + path + " to " + correctedPath);
                    correctedPaths.put(correctedPath, pathItem);
                } else {
                    // Keep the original path
                    correctedPaths.put(path, pathItem);
                }
            });
            
            // Replace the paths with the corrected ones
            openApi.setPaths(new Paths());
            correctedPaths.forEach((path, pathItem) -> openApi.getPaths().addPathItem(path, pathItem));
            
            logger.info("Swagger paths have been corrected");
        };
    }
}
