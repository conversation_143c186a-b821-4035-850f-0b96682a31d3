package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.InvoiceTypeDto;
import com.redberyl.invoiceapp.entity.InvoiceType;
import com.redberyl.invoiceapp.repository.InvoiceTypeRepository;
import com.redberyl.invoiceapp.service.InvoiceTypeService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class InvoiceTypeServiceImpl implements InvoiceTypeService {

    @Autowired
    private InvoiceTypeRepository invoiceTypeRepository;

    @Override
    public List<InvoiceTypeDto> getAllInvoiceTypes() {
        return invoiceTypeRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public InvoiceTypeDto getInvoiceTypeById(Long id) {
        InvoiceType invoiceType = invoiceTypeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Invoice Type not found with id: " + id));
        return convertToDto(invoiceType);
    }

    @Override
    public InvoiceTypeDto getInvoiceTypeByType(String invoiceType) {
        InvoiceType type = invoiceTypeRepository.findByInvoiceType(invoiceType)
                .orElseThrow(() -> new EntityNotFoundException("Invoice Type not found with type: " + invoiceType));
        return convertToDto(type);
    }

    @Override
    @Transactional
    public InvoiceTypeDto createInvoiceType(InvoiceTypeDto invoiceTypeDto) {
        InvoiceType invoiceType = convertToEntity(invoiceTypeDto);
        InvoiceType savedInvoiceType = invoiceTypeRepository.save(invoiceType);
        return convertToDto(savedInvoiceType);
    }

    @Override
    @Transactional
    public InvoiceTypeDto updateInvoiceType(Long id, InvoiceTypeDto invoiceTypeDto) {
        InvoiceType existingInvoiceType = invoiceTypeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Invoice Type not found with id: " + id));
        
        existingInvoiceType.setInvoiceType(invoiceTypeDto.getInvoiceType());
        existingInvoiceType.setTypeDesc(invoiceTypeDto.getTypeDesc());
        
        InvoiceType updatedInvoiceType = invoiceTypeRepository.save(existingInvoiceType);
        return convertToDto(updatedInvoiceType);
    }

    @Override
    @Transactional
    public void deleteInvoiceType(Long id) {
        if (!invoiceTypeRepository.existsById(id)) {
            throw new EntityNotFoundException("Invoice Type not found with id: " + id);
        }
        invoiceTypeRepository.deleteById(id);
    }

    private InvoiceTypeDto convertToDto(InvoiceType invoiceType) {
        return InvoiceTypeDto.builder()
                .id(invoiceType.getId())
                .invoiceType(invoiceType.getInvoiceType())
                .typeDesc(invoiceType.getTypeDesc())
                .build();
    }

    private InvoiceType convertToEntity(InvoiceTypeDto invoiceTypeDto) {
        return InvoiceType.builder()
                .id(invoiceTypeDto.getId())
                .invoiceType(invoiceTypeDto.getInvoiceType())
                .typeDesc(invoiceTypeDto.getTypeDesc())
                .build();
    }
}
