package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class DealDto extends BaseDto {
    @Schema(description = "Deal ID", accessMode = Schema.AccessMode.READ_ONLY)
    private Long id;
    
    @Schema(description = "Lead ID", example = "1")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long leadId;
    
    @Schema(description = "Client ID", example = "1")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long clientId;
    
    // Include lead object in responses
    @Schema(description = "Lead details")
    private LeadDto lead;
    
    // Show full client object in responses
    @Schema(description = "Client details")
    private ClientDto client;
    
    @NotBlank(message = "Project name is required")
    @Schema(description = "Project name", example = "New Project")
    private String projectName;
    
    @Schema(description = "Value estimate", example = "10000.00")
    private BigDecimal valueEstimate;
    
    @Schema(description = "Expected closure date", example = "2025-05-30")
    private LocalDate expectedClosureDate;
    
    @Schema(description = "Status", example = "open")
    private String status;
    
    @Schema(description = "Notes", example = "This is a sample deal")
    private String notes;
}
