// vite.config.ts
import { defineConfig } from "file:///C:/Users/<USER>/Desktop/Invoiceapp/frontend/node_modules/vite/dist/node/index.js";
import react from "file:///C:/Users/<USER>/Desktop/Invoiceapp/frontend/node_modules/@vitejs/plugin-react-swc/index.mjs";
import path from "path";
import { componentTagger } from "file:///C:/Users/<USER>/Desktop/Invoiceapp/frontend/node_modules/lovable-tagger/dist/index.js";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\Desktop\\Invoiceapp\\frontend";
var vite_config_default = defineConfig(({ mode }) => ({
  server: {
    host: "0.0.0.0",
    // Listen on all interfaces
    port: 3e3,
    strictPort: true,
    open: true,
    cors: true,
    proxy: {
      "/api": {
        target: "http://*************:8091",
        // Try localhost first
        changeOrigin: true,
        secure: false,
        rewrite: (path2) => path2,
        // Log more details about the connection
        onProxyReq: (proxyReq, req, res) => {
          console.log(`Proxying ${req.method} request to: ${req.url}`);
        },
        onError: (err, req, res) => {
          console.error("Proxy error details:", err);
          console.log("Attempting to connect to backend at IP address instead of localhost");
        },
        configure: (proxy, _options) => {
          proxy.on("error", (err, req, res) => {
            console.log("proxy error", err);
            if (req && req.url) {
              console.log("Retrying with IP address...");
              const ipTarget = "http://************:8080";
              const newPath = req.url.replace("/api", "");
              const newUrl = `${ipTarget}${newPath}`;
              console.log(`Redirecting to: ${newUrl}`);
              if (res && !res.headersSent) {
                res.writeHead(307, { "Location": newUrl });
                res.end();
              }
            }
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log("Sending Request to the Target:", req.method, req.url);
          });
          proxy.on("proxyRes", (proxyRes, req, _res) => {
            console.log("Received Response from the Target:", proxyRes.statusCode, req.url);
          });
        }
      }
    }
  },
  base: "/",
  plugins: [
    react(),
    mode === "development" && componentTagger()
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__vite_injected_original_dirname, "./src")
    }
  }
}));
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
