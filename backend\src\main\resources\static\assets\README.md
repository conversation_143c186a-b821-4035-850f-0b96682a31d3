# RedBeryl Logo Assets

## 📁 Logo Files

Place your RedBeryl logo files in this directory:

- `redberyl-logo.png` - Main logo file (recommended: 600x240px)
- `redberyl-logo.jpg` - Alternative JPG format
- `redberyl-logo.svg` - Vector format (if available)

## 🎯 Current Status

✅ **SVG Logo**: Embedded base64 version is working in PDF generation
⚠️ **Image Logo**: Place your actual logo file here to use it instead

## 📋 Instructions

1. **Save your RedBeryl logo** as `redberyl-logo.png` in this folder
2. **Test the logo** at: http://localhost:3000/test-logo.html
3. **Generate PDF** at: http://localhost:3000/invoice-pdf-demo

## 🔧 Technical Details

- **Current Implementation**: Base64 SVG embedded in PDF template
- **Fallback System**: SVG → Image file → Default placeholder
- **PDF Compatible**: Uses data URLs for reliable embedding
- **High Quality**: Vector-based for crisp appearance

## 🎨 Logo Specifications

- **Format**: PNG (transparent background preferred)
- **Size**: 600x240 pixels (2.5:1 aspect ratio)
- **Colors**: 
  - Blue: #1565C0, #3678FF
  - Pink/Red: #EA336A, #E91E63
  - Purple: #9C27B0, #8E44AD
- **Usage**: Invoice headers, PDF generation, branding

## 🚀 Next Steps

1. Replace the SVG with your actual logo image
2. Update the invoice template to use `useImage={true}` if needed
3. Test PDF generation to ensure logo appears correctly
4. Customize sizing and positioning as required
