import React, { useState } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  useToast,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Button,
  VStack,
  HStack,
  Divider,
  Code
} from '@chakra-ui/react';
import ClientForm from './ClientForm';

const CreateClientPage = () => {
  const toast = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [createdClient, setCreatedClient] = useState(null);
  const [error, setError] = useState(null);

  const handleSubmit = async (clientData) => {
    try {
      setIsSubmitting(true);
      setError(null);
      
      console.log('Creating client with data:', clientData);
      
      // Create basic auth header if needed
      const authHeader = 'Basic ' + btoa('admin:admin123');
      
      // Make the API call to create the client
      const response = await fetch('http://localhost:8091/api/clients', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': authHeader
        },
        body: JSON.stringify(clientData),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to create client: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Client created successfully:', data);
      
      // Store the created client
      setCreatedClient(data);
      
      // Show success toast
      toast({
        title: 'Client created successfully',
        description: `Client "${data.name}" has been created`,
        status: 'success',
        duration: 5000,
        isClosable: true
      });
      
      return data;
    } catch (err) {
      console.error('Error creating client:', err);
      setError(err.message || 'An unexpected error occurred');
      
      // Show error toast
      toast({
        title: 'Error creating client',
        description: err.message || 'An unexpected error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true
      });
      
      throw err;
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCreateAnother = () => {
    setCreatedClient(null);
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box>
          <Heading size="lg">Create New Client</Heading>
          <Text mt={2} color="gray.600">
            Fill in the client details below to add them to your system.
          </Text>
        </Box>
        
        {error && (
          <Alert status="error">
            <AlertIcon />
            <AlertTitle mr={2}>Error!</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {createdClient ? (
          <Box>
            <Alert status="success" mb={4}>
              <AlertIcon />
              <AlertTitle mr={2}>Success!</AlertTitle>
              <AlertDescription>
                Client "{createdClient.name}" has been created successfully.
              </AlertDescription>
            </Alert>
            
            <Box p={4} borderWidth="1px" borderRadius="md" bg="gray.50">
              <Heading size="sm" mb={2}>Client Details:</Heading>
              <VStack align="stretch" spacing={2}>
                <HStack>
                  <Text fontWeight="bold" width="150px">ID:</Text>
                  <Text>{createdClient.id}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold" width="150px">Name:</Text>
                  <Text>{createdClient.name}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold" width="150px">Email:</Text>
                  <Text>{createdClient.email}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold" width="150px">Phone:</Text>
                  <Text>{createdClient.phone}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold" width="150px">Contact Person:</Text>
                  <Text>{createdClient.contactPerson}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold" width="150px">BDM ID:</Text>
                  <Text>{createdClient.bdmId}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold" width="150px">BDM Name:</Text>
                  <Text>{createdClient.bdmName}</Text>
                </HStack>
              </VStack>
              
              <Divider my={4} />
              
              <Heading size="sm" mb={2}>Full Response:</Heading>
              <Box overflowX="auto">
                <Code p={2} display="block" whiteSpace="pre">
                  {JSON.stringify(createdClient, null, 2)}
                </Code>
              </Box>
            </Box>
            
            <HStack mt={4} spacing={4}>
              <Button colorScheme="blue" onClick={handleCreateAnother}>
                Create Another Client
              </Button>
              <Button variant="outline" onClick={() => window.history.back()}>
                Go Back
              </Button>
            </HStack>
          </Box>
        ) : (
          <Box p={6} borderWidth="1px" borderRadius="md" bg="white">
            <ClientForm onSubmit={handleSubmit} />
          </Box>
        )}
      </VStack>
    </Container>
  );
};

export default CreateClientPage;
