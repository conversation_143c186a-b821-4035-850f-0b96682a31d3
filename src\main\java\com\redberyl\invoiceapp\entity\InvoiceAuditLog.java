package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "invoice_audit_log")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InvoiceAuditLog extends BaseEntity {

    @Id
    @SequenceGenerator(name = "invoice_audit_log_seq", sequenceName = "invoice_audit_log_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "invoice_audit_log_seq")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "invoice_id")
    private Invoice invoice;

    @Column(name = "action")
    private String action;

    @Column(name = "performed_by")
    private String performedBy;

    // Add missing fields
    @Column(name = "action_by")
    private String actionBy;

    @Column(name = "action_date")
    private java.time.LocalDateTime actionDate;
}
