# Server Configuration
server.port=8091
server.servlet.context-path=/
server.address=0.0.0.0

# Static Resources Configuration
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.add-mappings=true
spring.mvc.static-path-pattern=/static/**

# Disable default error page to prevent conflicts
server.error.whitelabel.enabled=false


# CORS Configuration
spring.web.cors.allowed-origin-patterns=*
spring.web.cors.allowed-methods=GET,POST,PUT,PATCH,DELETE,OPTIONS,HEAD
spring.web.cors.allowed-headers=*
spring.web.cors.exposed-headers=Access-Control-Allow-Origin,Access-Control-Allow-Credentials,Access-Control-Allow-Methods,Access-Control-Allow-Headers,Access-Control-Max-Age,Authorization,X-Auth-Token
spring.web.cors.allow-credentials=true
spring.web.cors.max-age=3600

# Enable CORS for development
spring.mvc.dispatch-options-request=true

# Allow bean definition overriding
spring.main.allow-bean-definition-overriding=true

# Database Configuration
spring.datasource.url=*******************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver


# Logging Configuration
logging.level.root=INFO
logging.level.org.springframework=DEBUG
logging.level.org.hibernate=DEBUG
logging.level.com.redberyl.invoiceapp=TRACE

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Enable more detailed logging
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.com.redberyl=DEBUG
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Flyway Configuration
spring.flyway.enabled=false

# JWT Configuration
jwt.secret=redberylSecretKey123456789012345678901234567890
jwt.expiration=86400000

# Swagger Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.default-produces-media-type=application/json
springdoc.default-consumes-media-type=application/json
springdoc.swagger-ui.default-models-expand-depth=-1
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.swagger-ui.doc-expansion=list
springdoc.swagger-ui.display-request-duration=true
springdoc.swagger-ui.filter=true
springdoc.swagger-ui.deep-linking=true
springdoc.swagger-ui.syntax-highlight.activated=true
springdoc.swagger-ui.syntax-highlight.theme=monokai
springdoc.swagger-ui.try-it-out-enabled=true
springdoc.swagger-ui.persist-authorization=true
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true
springdoc.packages-to-scan=com.redberyl.invoiceapp.controller
springdoc.swagger-ui.with-credentials=true
springdoc.swagger-ui.csrf.enabled=true
springdoc.swagger-ui.layout=BaseLayout
springdoc.swagger-ui.default-model-rendering=model
springdoc.paths-to-match=/**
springdoc.writer-with-default-pretty-printer=true
