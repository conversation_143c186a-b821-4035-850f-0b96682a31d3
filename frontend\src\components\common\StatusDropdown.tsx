import React, { useState, useRef, useEffect } from 'react';
import { Check } from 'lucide-react';

interface StatusDropdownProps {
  currentStatus: string;
  onStatusChange: (newStatus: string) => void;
}

const StatusDropdown: React.FC<StatusDropdownProps> = ({ 
  currentStatus, 
  onStatusChange 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  const statuses = [
    { value: 'Completed', color: 'bg-green-100 text-green-800' },
    { value: 'In Progress', color: 'bg-blue-100 text-blue-800' },
    { value: 'Pending', color: 'bg-yellow-100 text-yellow-800' }
  ];

  // Get the color for the current status
  const getCurrentStatusColor = () => {
    const status = statuses.find(s => s.value === currentStatus);
    return status ? status.color : 'bg-gray-100 text-gray-800';
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleStatusClick = (status: string) => {
    onStatusChange(status);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold ${getCurrentStatusColor()} cursor-pointer hover:opacity-80`}
        onClick={() => setIsOpen(!isOpen)}
      >
        {currentStatus}
      </button>

      {isOpen && (
        <div className="absolute z-10 mt-1 w-40 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="py-1">
            {statuses.map((status) => (
              <button
                key={status.value}
                type="button"
                className={`flex w-full items-center px-3 py-2 text-sm ${status.value === currentStatus ? 'bg-gray-100' : 'hover:bg-gray-50'}`}
                onClick={() => handleStatusClick(status.value)}
              >
                <div className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-semibold ${status.color} mr-2`}>
                  {status.value}
                </div>
                {status.value === currentStatus && (
                  <Check className="h-4 w-4 ml-auto text-green-500" />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default StatusDropdown;
