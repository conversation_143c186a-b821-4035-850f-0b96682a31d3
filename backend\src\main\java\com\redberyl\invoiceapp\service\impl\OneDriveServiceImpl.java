package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.config.OneDriveConfig;
import com.redberyl.invoiceapp.dto.OneDriveUploadResponse;
import com.redberyl.invoiceapp.service.OneDriveService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.UUID;

/**
 * Implementation of OneDrive service using Microsoft Graph API
 */
@Service
public class OneDriveServiceImpl implements OneDriveService {

    private static final Logger logger = LoggerFactory.getLogger(OneDriveServiceImpl.class);
    
    @Autowired
    private OneDriveConfig oneDriveConfig;
    
    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public String getAuthorizationUrl() {
        try {
            String state = UUID.randomUUID().toString();
            
            return UriComponentsBuilder
                    .fromHttpUrl("https://login.microsoftonline.com/" + oneDriveConfig.getTenantId() + "/oauth2/v2.0/authorize")
                    .queryParam("client_id", oneDriveConfig.getClientId())
                    .queryParam("response_type", "code")
                    .queryParam("redirect_uri", URLEncoder.encode(oneDriveConfig.getRedirectUri(), StandardCharsets.UTF_8))
                    .queryParam("scope", URLEncoder.encode(oneDriveConfig.getScope(), StandardCharsets.UTF_8))
                    .queryParam("state", state)
                    .queryParam("response_mode", "query")
                    .build()
                    .toUriString();
        } catch (Exception e) {
            logger.error("Error generating authorization URL", e);
            throw new RuntimeException("Failed to generate authorization URL", e);
        }
    }

    @Override
    public String handleCallback(String code, String state) {
        try {
            String tokenUrl = "https://login.microsoftonline.com/" + oneDriveConfig.getTenantId() + "/oauth2/v2.0/token";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("client_id", oneDriveConfig.getClientId());
            body.add("client_secret", oneDriveConfig.getClientSecret());
            body.add("code", code);
            body.add("redirect_uri", oneDriveConfig.getRedirectUri());
            body.add("grant_type", "authorization_code");
            body.add("scope", oneDriveConfig.getScope());
            
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(tokenUrl, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> tokenResponse = response.getBody();
                return (String) tokenResponse.get("access_token");
            } else {
                throw new RuntimeException("Failed to get access token");
            }
        } catch (Exception e) {
            logger.error("Error handling OAuth callback", e);
            throw new RuntimeException("Failed to handle OAuth callback", e);
        }
    }

    @Override
    public OneDriveUploadResponse uploadFile(String accessToken, byte[] fileContent, String fileName, String folderPath) {
        try {
            // Create folder path if it doesn't exist
            String encodedPath = URLEncoder.encode(folderPath, StandardCharsets.UTF_8);
            String uploadUrl = oneDriveConfig.getGraphEndpoint() + "/me/drive/root:" + encodedPath + "/" + fileName + ":/content";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentLength(fileContent.length);
            
            HttpEntity<byte[]> request = new HttpEntity<>(fileContent, headers);
            
            ResponseEntity<Map> response = restTemplate.exchange(uploadUrl, HttpMethod.PUT, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                Map<String, Object> responseBody = response.getBody();
                if (responseBody != null) {
                    String fileId = (String) responseBody.get("id");
                    String webUrl = (String) responseBody.get("webUrl");
                    String downloadUrl = (String) ((Map<String, Object>) responseBody.get("@microsoft.graph.downloadUrl")).get("@microsoft.graph.downloadUrl");
                    Long fileSize = ((Number) responseBody.get("size")).longValue();
                    
                    return OneDriveUploadResponse.success(fileId, fileName, webUrl, downloadUrl, fileSize);
                }
            }
            
            return OneDriveUploadResponse.error("Upload failed with status: " + response.getStatusCode());
        } catch (Exception e) {
            logger.error("Error uploading file to OneDrive", e);
            return OneDriveUploadResponse.error("Upload failed: " + e.getMessage());
        }
    }

    @Override
    public OneDriveUploadResponse uploadInvoicePdf(String accessToken, byte[] pdfContent, String invoiceNumber) {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));
            String fileName = String.format("Invoice_%s_%s.pdf", invoiceNumber, timestamp);
            String folderPath = oneDriveConfig.getBasePath() + "/Invoices";
            
            return uploadFile(accessToken, pdfContent, fileName, folderPath);
        } catch (Exception e) {
            logger.error("Error uploading invoice PDF to OneDrive", e);
            return OneDriveUploadResponse.error("Failed to upload invoice PDF: " + e.getMessage());
        }
    }

    @Override
    public boolean isAuthenticated(String accessToken) {
        try {
            if (accessToken == null || accessToken.trim().isEmpty()) {
                return false;
            }
            
            String testUrl = oneDriveConfig.getGraphEndpoint() + "/me";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);
            
            HttpEntity<String> request = new HttpEntity<>(headers);
            
            ResponseEntity<Map> response = restTemplate.exchange(testUrl, HttpMethod.GET, request, Map.class);
            
            return response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            logger.debug("Token validation failed", e);
            return false;
        }
    }

    @Override
    public String refreshAccessToken(String refreshToken) {
        try {
            String tokenUrl = "https://login.microsoftonline.com/" + oneDriveConfig.getTenantId() + "/oauth2/v2.0/token";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("client_id", oneDriveConfig.getClientId());
            body.add("client_secret", oneDriveConfig.getClientSecret());
            body.add("refresh_token", refreshToken);
            body.add("grant_type", "refresh_token");
            body.add("scope", oneDriveConfig.getScope());
            
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(tokenUrl, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> tokenResponse = response.getBody();
                return (String) tokenResponse.get("access_token");
            } else {
                throw new RuntimeException("Failed to refresh access token");
            }
        } catch (Exception e) {
            logger.error("Error refreshing access token", e);
            throw new RuntimeException("Failed to refresh access token", e);
        }
    }
}
