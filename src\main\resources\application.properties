# Server Configuration
server.port=8091
server.servlet.context-path=/api

# Database Configuration
spring.datasource.url=*******************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=create
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Logging Configuration
logging.level.root=INFO
logging.level.org.springframework=DEBUG
logging.level.org.hibernate=DEBUG
logging.level.com.redberyl.invoiceapp=TRACE

# Flyway Configuration
spring.flyway.enabled=false

# JWT Configuration
jwt.secret=redberylSecretKey123456789012345678901234567890
jwt.expiration=86400000

# Swagger Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui
springdoc.swagger-ui.operationsSorter=alpha
springdoc.swagger-ui.tagsSorter=alpha
springdoc.default-produces-media-type=application/json
springdoc.default-consumes-media-type=application/json
springdoc.swagger-ui.default-models-expand-depth=-1
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.swagger-ui.doc-expansion=list
springdoc.swagger-ui.display-request-duration=true
springdoc.swagger-ui.filter=true
springdoc.swagger-ui.deep-linking=true
springdoc.swagger-ui.syntax-highlight.activated=true
springdoc.swagger-ui.syntax-highlight.theme=monokai
springdoc.swagger-ui.try-it-out-enabled=true
springdoc.swagger-ui.persist-authorization=true
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true
springdoc.packages-to-scan=com.redberyl.invoiceapp.controller
springdoc.swagger-ui.with-credentials=true
springdoc.swagger-ui.csrf.enabled=true
springdoc.swagger-ui.layout=BaseLayout
springdoc.swagger-ui.default-model-rendering=model
springdoc.swagger-ui.show-extensions=true
springdoc.swagger-ui.show-common-extensions=true
