import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  Divider,
  Code,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Spinner,
  useToast,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  CardFooter,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel
} from '@chakra-ui/react';
import BdmDropdown from '../BdmDropdown';
import BdmDetails from '../BdmDetails';
import ClientWithBdmDetails from './ClientWithBdmDetails';

const CompleteBdmRelationTest = () => {
  const toast = useToast();
  
  // State for BDM data
  const [selectedBdmId, setSelectedBdmId] = useState(null);
  const [selectedBdm, setSelectedBdm] = useState(null);
  const [bdmLoading, setBdmLoading] = useState(false);
  
  // State for client data
  const [clientData, setClientData] = useState({
    name: 'ABC Technologies Pvt Ltd',
    email: '<EMAIL>',
    phone: '+91-9876543210',
    contactPerson: 'Rahul Sharma',
    website: 'https://www.abctech.com',
    bdmId: null,
    commissionPercentage: '5',
    billingAddress: '123, Tech Park, Sector 45, Bangalore, Karnataka, India - 560045',
    shippingAddress: '456, Industrial Area, Sector 67, Noida, Uttar Pradesh, India - 201301',
    gstNumber: '29**********1Z5',
    panNumber: '**********',
    cinNumber: 'U12345KA2010PTC012345',
    notes: 'Preferred vendor for software development and consulting services.'
  });
  
  // State for API responses
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [createdClient, setCreatedClient] = useState(null);
  const [error, setError] = useState(null);
  
  // Handle BDM selection
  const handleBdmChange = (bdmId) => {
    console.log('BDM selected:', bdmId);
    setSelectedBdmId(bdmId);
    
    if (bdmId) {
      fetchBdmDetails(bdmId);
    } else {
      setSelectedBdm(null);
    }
    
    // Update client data
    setClientData(prev => ({ ...prev, bdmId }));
  };
  
  // Fetch BDM details
  const fetchBdmDetails = async (bdmId) => {
    try {
      setBdmLoading(true);
      
      // Create basic auth header if needed
      const authHeader = 'Basic ' + btoa('admin:admin123');
      
      const response = await fetch(`http://*************:8091/api/v1/bdms/${bdmId}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': authHeader
        },
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch BDM details: ${response.status}`);
      }
      
      const responseData = await response.json();
      console.log('BDM details response:', responseData);
      
      // Extract BDM data from the response
      let bdmData = null;
      if (responseData.success && responseData.data) {
        // Format from /api/v1/bdms/{id}
        bdmData = responseData.data;
      } else {
        // Direct format from /api/bdms/{id}
        bdmData = responseData;
      }
      
      console.log('Extracted BDM data:', bdmData);
      setSelectedBdm(bdmData);
    } catch (err) {
      console.error('Error fetching BDM details:', err);
      toast({
        title: 'Error fetching BDM details',
        description: err.message,
        status: 'error',
        duration: 3060,
        isClosable: true
      });
    } finally {
      setBdmLoading(false);
    }
  };
  
  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setClientData(prev => ({ ...prev, [name]: value }));
  };
  
  // Create client with BDM relationship
  const handleCreateClient = async () => {
    try {
      setIsSubmitting(true);
      setError(null);
      
      // Validate required fields
      if (!clientData.name || !clientData.email || !clientData.phone || !clientData.contactPerson) {
        throw new Error('Please fill in all required fields');
      }
      
      // Validate BDM selection
      if (!clientData.bdmId) {
        throw new Error('Please select a BDM');
      }
      
      console.log('Creating client with data:', clientData);
      
      // Create basic auth header if needed
      const authHeader = 'Basic ' + btoa('admin:admin123');
      
      // Make the API call to create the client
      const response = await fetch('http://*************:8091/api/clients', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': authHeader
        },
        body: JSON.stringify(clientData),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to create client: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Client created successfully:', data);
      setCreatedClient(data);
      
      toast({
        title: 'Client created successfully',
        description: `Client "${data.name}" has been created with BDM "${data.bdmName}"`,
        status: 'success',
        duration: 5000,
        isClosable: true
      });
    } catch (err) {
      console.error('Error creating client:', err);
      setError(err.message);
      
      toast({
        title: 'Error creating client',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleReset = () => {
    setCreatedClient(null);
    setSelectedBdmId(null);
    setSelectedBdm(null);
    setClientData({
      name: 'ABC Technologies Pvt Ltd',
      email: '<EMAIL>',
      phone: '+91-9876543210',
      contactPerson: 'Rahul Sharma',
      website: 'https://www.abctech.com',
      bdmId: null,
      commissionPercentage: '5',
      billingAddress: '123, Tech Park, Sector 45, Bangalore, Karnataka, India - 560045',
      shippingAddress: '456, Industrial Area, Sector 67, Noida, Uttar Pradesh, India - 201301',
      gstNumber: '29**********1Z5',
      panNumber: '**********',
      cinNumber: 'U12345KA2010PTC012345',
      notes: 'Preferred vendor for software development and consulting services.'
    });
  };
  
  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box>
          <Heading size="lg">Complete BDM Relationship Test</Heading>
          <Text mt={2} color="gray.600">
            This page demonstrates how to create a client with a complete BDM relationship and display all BDM data.
          </Text>
        </Box>
        
        {error && (
          <Alert status="error">
            <AlertIcon />
            <AlertTitle mr={2}>Error!</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {createdClient ? (
          <Card>
            <CardHeader>
              <Heading size="md">Client Created Successfully</Heading>
            </CardHeader>
            <CardBody>
              <Alert status="success" mb={4}>
                <AlertIcon />
                <AlertTitle mr={2}>Success!</AlertTitle>
                <AlertDescription>
                  Client "{createdClient.name}" has been created with BDM "{createdClient.bdmName}"
                </AlertDescription>
              </Alert>
              
              <Tabs variant="enclosed">
                <TabList>
                  <Tab>Client with BDM Details</Tab>
                  <Tab>Raw Response</Tab>
                </TabList>
                <TabPanels>
                  <TabPanel>
                    <ClientWithBdmDetails clientId={createdClient.id} />
                  </TabPanel>
                  <TabPanel>
                    <Box overflowX="auto">
                      <Heading size="sm" mb={2}>Client Creation Response:</Heading>
                      <Code p={2} display="block" whiteSpace="pre">
                        {JSON.stringify(createdClient, null, 2)}
                      </Code>
                    </Box>
                  </TabPanel>
                </TabPanels>
              </Tabs>
            </CardBody>
            <CardFooter>
              <Button colorScheme="blue" onClick={handleReset}>
                Create Another Client
              </Button>
            </CardFooter>
          </Card>
        ) : (
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
            {/* BDM Selection */}
            <Card>
              <CardHeader>
                <Heading size="md">BDM Selection</Heading>
              </CardHeader>
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <BdmDropdown 
                    value={selectedBdmId} 
                    onChange={handleBdmChange}
                    required={true}
                    label="Select a BDM"
                  />
                  
                  {bdmLoading ? (
                    <Box textAlign="center" py={4}>
                      <Spinner size="md" />
                      <Text mt={2}>Loading BDM details...</Text>
                    </Box>
                  ) : selectedBdm ? (
                    <BdmDetails bdm={selectedBdm} />
                  ) : null}
                </VStack>
              </CardBody>
            </Card>
            
            {/* Client Form */}
            <Card>
              <CardHeader>
                <Heading size="md">Create Client with BDM</Heading>
              </CardHeader>
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <FormControl isRequired>
                    <FormLabel>Company Name</FormLabel>
                    <Input 
                      name="name" 
                      value={clientData.name} 
                      onChange={handleInputChange} 
                    />
                  </FormControl>
                  
                  <SimpleGrid columns={2} spacing={4}>
                    <FormControl isRequired>
                      <FormLabel>Email</FormLabel>
                      <Input 
                        name="email" 
                        value={clientData.email} 
                        onChange={handleInputChange} 
                      />
                    </FormControl>
                    
                    <FormControl isRequired>
                      <FormLabel>Phone</FormLabel>
                      <Input 
                        name="phone" 
                        value={clientData.phone} 
                        onChange={handleInputChange} 
                      />
                    </FormControl>
                  </SimpleGrid>
                  
                  <SimpleGrid columns={2} spacing={4}>
                    <FormControl isRequired>
                      <FormLabel>Contact Person</FormLabel>
                      <Input 
                        name="contactPerson" 
                        value={clientData.contactPerson} 
                        onChange={handleInputChange} 
                      />
                    </FormControl>
                    
                    <FormControl>
                      <FormLabel>Commission Percentage</FormLabel>
                      <Input 
                        name="commissionPercentage" 
                        value={clientData.commissionPercentage} 
                        onChange={handleInputChange} 
                      />
                    </FormControl>
                  </SimpleGrid>
                  
                  <FormControl>
                    <FormLabel>Billing Address</FormLabel>
                    <Textarea 
                      name="billingAddress" 
                      value={clientData.billingAddress} 
                      onChange={handleInputChange} 
                      rows={2}
                    />
                  </FormControl>
                </VStack>
              </CardBody>
              <CardFooter>
                <Button 
                  colorScheme="blue" 
                  onClick={handleCreateClient}
                  isLoading={isSubmitting}
                  loadingText="Creating Client"
                  isDisabled={!selectedBdmId}
                >
                  Create Client with Complete BDM Relationship
                </Button>
              </CardFooter>
            </Card>
          </SimpleGrid>
        )}
      </VStack>
    </Container>
  );
};

export default CompleteBdmRelationTest;
