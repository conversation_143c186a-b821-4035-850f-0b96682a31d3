package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.TaxRateDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.TaxRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@Tag(name = "Tax Rate", description = "Tax Rate management API")
public class TaxRateController {

    @Autowired
    private TaxRateService taxRateService;

    @GetMapping("/tax-rates/getAll")
    @Operation(summary = "Get all tax rates", description = "Get all tax rates")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Tax rates found"),
            @ApiResponse(responseCode = "204", description = "No tax rates found", content = @Content)
    })
    public ResponseEntity<List<TaxRateDto>> getAllTaxRates() {
        try {
            List<TaxRateDto> taxRates = taxRateService.getAllTaxRates();
            return new ResponseEntity<>(taxRates, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/tax-rates/getById/{id}")
    @Operation(summary = "Get tax rate by ID", description = "Get tax rate by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Tax rate found"),
            @ApiResponse(responseCode = "404", description = "Tax rate not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    public ResponseEntity<TaxRateDto> getTaxRateById(@PathVariable Long id) {
        TaxRateDto taxRate = taxRateService.getTaxRateById(id);
        return new ResponseEntity<>(taxRate, HttpStatus.OK);
    }

    @GetMapping("/tax-rates/getByTaxTypeId/{taxTypeId}")
    @Operation(summary = "Get tax rates by tax type ID", description = "Get tax rates by tax type ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Tax rates found"),
            @ApiResponse(responseCode = "204", description = "No tax rates found for the given tax type", content = @Content),
            @ApiResponse(responseCode = "400", description = "Invalid tax type ID supplied"),
            @ApiResponse(responseCode = "404", description = "Tax type not found")
    })
    public ResponseEntity<List<TaxRateDto>> getTaxRatesByTaxTypeId(@PathVariable Long taxTypeId) {
        try {
            List<TaxRateDto> taxRates = taxRateService.getTaxRatesByTaxTypeId(taxTypeId);
            return new ResponseEntity<>(taxRates, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/tax-rates/getByEffectiveDate")
    @Operation(summary = "Get tax rates effective on a date", description = "Get tax rates effective on a date")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Tax rates found"),
            @ApiResponse(responseCode = "204", description = "No tax rates found for the given date", content = @Content),
            @ApiResponse(responseCode = "400", description = "Invalid date format")
    })
    public ResponseEntity<List<TaxRateDto>> getTaxRatesEffectiveOnDate(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        try {
            List<TaxRateDto> taxRates = taxRateService.getTaxRatesEffectiveOnDate(date);
            return new ResponseEntity<>(taxRates, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/tax-rates/create")
    @Operation(summary = "Create tax rate", description = "Create tax rate. Use one of the following formats:\n\n```json\n# Option 1: Simple ID as string\n{\n  \"taxTypeId\": \"1\",\n  \"rate\": 18.0,\n  \"effectiveFrom\": \"2025-04-17\",\n  \"effectiveTo\": \"2025-06-22\"\n}\n\n# Option 2: Simple ID as number\n{\n  \"taxTypeId\": 1,\n  \"rate\": 18.0,\n  \"effectiveFrom\": \"2025-04-17\",\n  \"effectiveTo\": \"2025-06-22\"\n}\n\n# Option 3: Nested object with ID\n{\n  \"taxTypeId\": {\n    \"id\": 1,\n    \"taxType\": \"GST\",\n    \"taxTypeDescription\": \"Goods and Services Tax\"\n  },\n  \"rate\": 18.0,\n  \"effectiveFrom\": \"2025-04-17\",\n  \"effectiveTo\": \"2025-06-22\"\n}\n```\n\nNote: The Tax Rate ID is automatically generated and should not be included in the request.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Tax rate created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation")
    })
    public ResponseEntity<TaxRateDto> createTaxRate(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "Tax Rate creation request", required = true, content = @io.swagger.v3.oas.annotations.media.Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = TaxRateDto.class), examples = {
                    @io.swagger.v3.oas.annotations.media.ExampleObject(name = "Simple Tax Rate Example", summary = "Simple Tax Rate Example", description = "Example of creating a tax rate with only the required fields", value = "{\n  \"taxTypeId\": \"1\",\n  \"rate\": 18.0,\n  \"effectiveFrom\": \"2025-04-17\",\n  \"effectiveTo\": \"2025-06-22\"\n}"),
                    @io.swagger.v3.oas.annotations.media.ExampleObject(name = "Nested Tax Type Example", summary = "Nested Tax Type Example", description = "Example of creating a tax rate with a nested tax type object", value = "{\n  \"taxTypeId\": {\n    \"id\": 1,\n    \"taxType\": \"GST\",\n    \"taxTypeDescription\": \"Goods and Services Tax\"\n  },\n  \"rate\": 18.0,\n  \"effectiveFrom\": \"2025-04-17\",\n  \"effectiveTo\": \"2025-06-22\"\n}")
            })) @Valid @RequestBody TaxRateDto taxRateDto) {
        TaxRateDto createdTaxRate = taxRateService.createTaxRate(taxRateDto);
        return new ResponseEntity<>(createdTaxRate, HttpStatus.CREATED);
    }

    @PutMapping("/tax-rates/update/{id}")
    @Operation(summary = "Update tax rate", description = "Update tax rate")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Tax rate updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "404", description = "Tax rate not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation")
    })
    public ResponseEntity<TaxRateDto> updateTaxRate(@PathVariable Long id, @Valid @RequestBody TaxRateDto taxRateDto) {
        TaxRateDto updatedTaxRate = taxRateService.updateTaxRate(id, taxRateDto);
        return new ResponseEntity<>(updatedTaxRate, HttpStatus.OK);
    }

    @DeleteMapping("/tax-rates/deleteById/{id}")
    @Operation(summary = "Delete tax rate", description = "Delete tax rate")
    public ResponseEntity<Void> deleteTaxRate(@PathVariable Long id) {
        taxRateService.deleteTaxRate(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
