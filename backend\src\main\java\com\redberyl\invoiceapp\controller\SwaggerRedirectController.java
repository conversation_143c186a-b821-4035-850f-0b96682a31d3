package com.redberyl.invoiceapp.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.servlet.view.RedirectView;

/**
 * Controller to provide easy access to Swagger UI through various URLs
 */
@Controller
public class SwaggerRedirectController {

    /**
     * Redirect /swagger to the Swagger UI
     */
    @GetMapping("/swagger")
    public RedirectView redirectToSwaggerUi() {
        return new RedirectView("/swagger-ui/index.html");
    }

    /**
     * Redirect /docs to the Swagger UI
     */
    @GetMapping("/docs")
    public RedirectView redirectToDocs() {
        return new RedirectView("/swagger-ui/index.html");
    }

    /**
     * Redirect /api-docs-ui to the Swagger UI
     */
    @GetMapping("/api-docs-ui")
    public RedirectView redirectToApiDocsUi() {
        return new RedirectView("/swagger-ui/index.html");
    }

    /**
     * Redirect /api-ui to the Swagger UI
     */
    @GetMapping("/api-ui")
    public RedirectView redirectToApiUi() {
        return new RedirectView("/swagger-ui/index.html");
    }

    /**
     * Redirect /api-docs to the Swagger UI
     */
    @GetMapping("/api-docs")
    public RedirectView redirectToApiDocs() {
        return new RedirectView("/swagger-ui/index.html");
    }
}
