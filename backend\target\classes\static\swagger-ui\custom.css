/* Custom styles for Swagger UI */

/* Main container */
.swagger-ui .wrapper {
    max-width: 1460px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.swagger-ui .topbar {
    background-color: #2c3e50;
    padding: 10px 0;
}

.swagger-ui .topbar .download-url-wrapper {
    display: flex;
    align-items: center;
}

.swagger-ui .topbar .download-url-wrapper input[type=text] {
    border: 2px solid #41444e;
    border-radius: 4px;
}

.swagger-ui .topbar .download-url-wrapper .download-url-button {
    background-color: #4990e2;
    color: white;
    border-color: #4990e2;
}

/* Info section */
.swagger-ui .info {
    margin: 20px 0;
}

.swagger-ui .info .title {
    font-size: 36px;
    color: #3b4151;
}

.swagger-ui .info .description {
    font-size: 16px;
    color: #3b4151;
}

/* Scheme container */
.swagger-ui .scheme-container {
    background-color: #f7f7f7;
    box-shadow: 0 1px 2px 0 rgba(0,0,0,.15);
}

/* Authorization button */
.swagger-ui .btn.authorize {
    background-color: #4990e2;
    color: white;
    border-color: #4990e2;
}

.swagger-ui .btn.authorize svg {
    fill: white;
}

/* Tags */
.swagger-ui .opblock-tag {
    font-size: 18px;
    font-weight: bold;
    color: #3b4151;
}

/* Operations */
.swagger-ui .opblock {
    margin-bottom: 15px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
}

.swagger-ui .opblock .opblock-summary {
    padding: 10px;
}

.swagger-ui .opblock .opblock-summary-method {
    font-weight: bold;
    border-radius: 3px;
    padding: 6px 12px;
}

/* GET operations */
.swagger-ui .opblock-get {
    background-color: rgba(97, 175, 254, 0.1);
    border-color: #61affe;
}

.swagger-ui .opblock-get .opblock-summary-method {
    background-color: #61affe;
}

/* POST operations */
.swagger-ui .opblock-post {
    background-color: rgba(73, 204, 144, 0.1);
    border-color: #49cc90;
}

.swagger-ui .opblock-post .opblock-summary-method {
    background-color: #49cc90;
}

/* PUT operations */
.swagger-ui .opblock-put {
    background-color: rgba(252, 161, 48, 0.1);
    border-color: #fca130;
}

.swagger-ui .opblock-put .opblock-summary-method {
    background-color: #fca130;
}

/* DELETE operations */
.swagger-ui .opblock-delete {
    background-color: rgba(249, 62, 62, 0.1);
    border-color: #f93e3e;
}

.swagger-ui .opblock-delete .opblock-summary-method {
    background-color: #f93e3e;
}

/* PATCH operations */
.swagger-ui .opblock-patch {
    background-color: rgba(80, 227, 194, 0.1);
    border-color: #50e3c2;
}

.swagger-ui .opblock-patch .opblock-summary-method {
    background-color: #50e3c2;
}

/* Operation sections */
.swagger-ui .opblock-tag-section {
    margin-bottom: 30px;
}

/* Models */
.swagger-ui .model-box {
    background-color: #f7f7f7;
    border-radius: 4px;
    padding: 10px;
}

.swagger-ui .model-title {
    font-size: 16px;
    font-weight: bold;
    color: #3b4151;
}

/* Tables */
.swagger-ui table {
    border-collapse: collapse;
    width: 100%;
}

.swagger-ui table thead tr {
    background-color: #f7f7f7;
}

.swagger-ui table th, .swagger-ui table td {
    padding: 10px;
    border: 1px solid #e0e0e0;
}

/* Buttons */
.swagger-ui .btn {
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}

.swagger-ui .btn-primary {
    background-color: #4990e2;
    color: white;
    border-color: #4990e2;
}

.swagger-ui .btn-secondary {
    background-color: #f7f7f7;
    color: #3b4151;
    border-color: #e0e0e0;
}

/* Try it out section */
.swagger-ui .try-out {
    margin-top: 10px;
}

.swagger-ui .try-out__btn {
    background-color: #4990e2;
    color: white;
    border-color: #4990e2;
}

/* Response section */
.swagger-ui .responses-table {
    margin-top: 20px;
}

.swagger-ui .response-col_status {
    font-weight: bold;
}

/* Code blocks */
.swagger-ui .highlight-code {
    background-color: #272822;
    color: #f8f8f2;
    border-radius: 4px;
    padding: 10px;
}

/* Footer */
.swagger-ui .footer {
    text-align: center;
    padding: 20px;
    background-color: #f7f7f7;
    margin-top: 50px;
    border-top: 1px solid #e0e0e0;
}

/* Responsive design */
@media (max-width: 768px) {
    .swagger-ui .wrapper {
        padding: 0 10px;
    }
    
    .swagger-ui .info .title {
        font-size: 24px;
    }
    
    .swagger-ui .opblock-tag {
        font-size: 16px;
    }
}
