import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  useToast,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Button,
  VStack,
  HStack,
  Spinner,
  Divider,
  Code
} from '@chakra-ui/react';
import { useParams, useNavigate } from 'react-router-dom';
import ClientForm from './ClientForm';

const EditClientPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const toast = useToast();
  
  const [client, setClient] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [updatedClient, setUpdatedClient] = useState(null);

  // Fetch client data
  useEffect(() => {
    const fetchClient = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Create basic auth header if needed
        const authHeader = 'Basic ' + btoa('admin:admin123');
        
        // Make the API call to get the client
        const response = await fetch(`http://*************:8091/api/clients/${id}`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          credentials: 'include'
        });
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || `Failed to fetch client: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Client fetched successfully:', data);
        
        setClient(data);
      } catch (err) {
        console.error('Error fetching client:', err);
        setError(err.message || 'An unexpected error occurred');
        
        toast({
          title: 'Error fetching client',
          description: err.message || 'An unexpected error occurred',
          status: 'error',
          duration: 5000,
          isClosable: true
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchClient();
  }, [id, toast]);

  const handleSubmit = async (clientData) => {
    try {
      setIsSubmitting(true);
      setError(null);
      
      console.log('Updating client with data:', clientData);
      
      // Create basic auth header if needed
      const authHeader = 'Basic ' + btoa('admin:admin123');
      
      // Make the API call to update the client
      const response = await fetch(`http://*************:8091/api/clients/${id}`, {
        method: 'PUT',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': authHeader
        },
        body: JSON.stringify(clientData),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to update client: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Client updated successfully:', data);
      
      // Store the updated client
      setUpdatedClient(data);
      
      // Show success toast
      toast({
        title: 'Client updated successfully',
        description: `Client "${data.name}" has been updated`,
        status: 'success',
        duration: 5000,
        isClosable: true
      });
      
      return data;
    } catch (err) {
      console.error('Error updating client:', err);
      setError(err.message || 'An unexpected error occurred');
      
      // Show error toast
      toast({
        title: 'Error updating client',
        description: err.message || 'An unexpected error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true
      });
      
      throw err;
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleEditAgain = () => {
    setUpdatedClient(null);
    // Refresh client data
    setClient(updatedClient);
  };

  if (isLoading) {
    return (
      <Container maxW="container.xl" py={8}>
        <VStack spacing={8} align="center" justify="center" minH="300px">
          <Spinner size="xl" />
          <Text>Loading client data...</Text>
        </VStack>
      </Container>
    );
  }

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box>
          <Heading size="lg">Edit Client</Heading>
          <Text mt={2} color="gray.600">
            Update the client details below.
          </Text>
        </Box>
        
        {error && (
          <Alert status="error">
            <AlertIcon />
            <AlertTitle mr={2}>Error!</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {updatedClient ? (
          <Box>
            <Alert status="success" mb={4}>
              <AlertIcon />
              <AlertTitle mr={2}>Success!</AlertTitle>
              <AlertDescription>
                Client "{updatedClient.name}" has been updated successfully.
              </AlertDescription>
            </Alert>
            
            <Box p={4} borderWidth="1px" borderRadius="md" bg="gray.50">
              <Heading size="sm" mb={2}>Updated Client Details:</Heading>
              <VStack align="stretch" spacing={2}>
                <HStack>
                  <Text fontWeight="bold" width="150px">ID:</Text>
                  <Text>{updatedClient.id}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold" width="150px">Name:</Text>
                  <Text>{updatedClient.name}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold" width="150px">Email:</Text>
                  <Text>{updatedClient.email}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold" width="150px">Phone:</Text>
                  <Text>{updatedClient.phone}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold" width="150px">Contact Person:</Text>
                  <Text>{updatedClient.contactPerson}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold" width="150px">BDM ID:</Text>
                  <Text>{updatedClient.bdmId}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold" width="150px">BDM Name:</Text>
                  <Text>{updatedClient.bdmName}</Text>
                </HStack>
              </VStack>
              
              <Divider my={4} />
              
              <Heading size="sm" mb={2}>Full Response:</Heading>
              <Box overflowX="auto">
                <Code p={2} display="block" whiteSpace="pre">
                  {JSON.stringify(updatedClient, null, 2)}
                </Code>
              </Box>
            </Box>
            
            <HStack mt={4} spacing={4}>
              <Button colorScheme="blue" onClick={handleEditAgain}>
                Edit Again
              </Button>
              <Button variant="outline" onClick={handleGoBack}>
                Go Back
              </Button>
            </HStack>
          </Box>
        ) : client ? (
          <Box p={6} borderWidth="1px" borderRadius="md" bg="white">
            <ClientForm initialData={client} onSubmit={handleSubmit} isEdit={true} />
          </Box>
        ) : (
          <Alert status="error">
            <AlertIcon />
            <AlertTitle mr={2}>Client Not Found</AlertTitle>
            <AlertDescription>
              The client with ID {id} could not be found.
            </AlertDescription>
          </Alert>
        )}
      </VStack>
    </Container>
  );
};

export default EditClientPage;
