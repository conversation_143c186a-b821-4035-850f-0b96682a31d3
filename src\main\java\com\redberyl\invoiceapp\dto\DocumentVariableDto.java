package com.redberyl.invoiceapp.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class DocumentVariableDto extends BaseDto {
    private Long id;
    
    @NotNull(message = "Template version ID is required")
    private Long templateVersionId;
    
    @NotBlank(message = "Variable name is required")
    private String variableName;
    
    private String description;
    private String sampleValue;
}
