package com.redberyl.invoiceapp.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.redberyl.invoiceapp.entity.TaxRate;
import org.springframework.boot.jackson.JsonComponent;

import java.io.IOException;

@JsonComponent
public class CustomTaxRateSerializer extends JsonSerializer<TaxRate> {

    @Override
    public void serialize(TaxRate taxRate, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        jsonGenerator.writeStartObject();
        
        // Write the tax rate properties
        jsonGenerator.writeNumberField("id", taxRate.getId());
        
        if (taxRate.getRate() != null) {
            jsonGenerator.writeNumberField("rate", taxRate.getRate());
        }
        
        if (taxRate.getEffectiveFrom() != null) {
            jsonGenerator.writeStringField("effectiveFrom", taxRate.getEffectiveFrom().toString());
        }
        
        if (taxRate.getEffectiveTo() != null) {
            jsonGenerator.writeStringField("effectiveTo", taxRate.getEffectiveTo().toString());
        }
        
        // Write the tax type as a nested object
        if (taxRate.getTaxType() != null) {
            jsonGenerator.writeObjectFieldStart("taxType");
            jsonGenerator.writeNumberField("id", taxRate.getTaxType().getId());
            jsonGenerator.writeStringField("taxType", taxRate.getTaxType().getTaxType());
            jsonGenerator.writeStringField("taxTypeDescription", taxRate.getTaxType().getTaxTypeDescription());
            jsonGenerator.writeEndObject();
        }
        
        // Write the audit fields
        if (taxRate.getCreatedAt() != null) {
            jsonGenerator.writeStringField("created_at", taxRate.getCreatedAt().toString());
        }
        
        jsonGenerator.writeEndObject();
    }
}
