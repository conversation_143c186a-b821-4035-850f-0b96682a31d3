
import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
// Dropdown menu components are used in other components
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Search,
  Plus,
  Loader2,
  RefreshCcw
} from "lucide-react";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useEntityData } from "@/hooks/useEntityData";
import ClientFormDialog from "@/components/clients/ClientFormDialog";
import DynamicProjectFormDialog from "@/components/projects/DynamicProjectFormDialog";
import StatusDropdown from "@/components/common/StatusDropdown";
import ActionMenuSimple from "@/components/common/ActionMenuSimple";
import ClientActionMenu from "@/components/common/ClientActionMenu";
import { toast } from "sonner";
import { projectService } from "@/services/projectService";



const Clients = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isClientDialogOpen, setIsClientDialogOpen] = useState(false);
  const [isProjectDialogOpen, setIsProjectDialogOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("clients");
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [selectedClientForEdit, setSelectedClientForEdit] = useState<any>(null);

  // Fetch clients and projects data with aggressive caching
  const {
    data: clientsData,
    loading: clientsLoading,
    error: clientsError,
    refetch: refetchClients
  } = useEntityData({
    entityType: 'clients',
    useMockData: false, // Set to false to use real API
    cacheTime: 3600000, // Cache for 1 hour
    refetchInterval: 0 // Disable automatic refetching
  });

  const {
    data: projectsData,
    loading: projectsLoading,
    error: projectsError,
    refetch: refetchProjects
  } = useEntityData({
    entityType: 'projects',
    useMockData: false, // Set to false to use real API
    cacheTime: 3600000, // Cache for 1 hour
    refetchInterval: 0 // Disable automatic refetching
  });

  // Filtered data states
  const [filteredClients, setFilteredClients] = useState<any[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<any[]>([]);

  // Local loading state for direct API calls
  const [directLoading, setDirectLoading] = useState<boolean>(false);
  const [directError, setDirectError] = useState<Error | null>(null);

  // Debug data loading - only log on initial load
  useEffect(() => {
    // Only log once when data is loaded
    if (!clientsLoading && clientsData && !projectsLoading && projectsData) {
      console.log('Initial data loaded - clients:', clientsData.length, 'projects:', projectsData.length);
    }
  }, [clientsLoading, projectsLoading]);

  // Fetch projects directly when component mounts
  useEffect(() => {
    console.log("Clients component mounted, fetching projects directly...");

    // Set loading state
    setDirectLoading(true);
    setDirectError(null);

    // Create a timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      console.warn("Project fetch timeout reached - stopping loading state");
      setDirectLoading(false);

      // If we still don't have data, show an error
      if (filteredProjects.length === 0) {
        setDirectError(new Error("Timeout reached while fetching projects"));
        toast.error("Failed to load projects: Timeout reached", {
          description: "Please try refreshing the page or check your network connection."
        });
      }
    }, 10000); // 10 second timeout

    // Make a direct fetch to the specific endpoint
    fetch('http://*************:8091/projects/getAll', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': 'Basic ' + btoa('admin:admin123')
      },
      credentials: 'omit'
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`Failed to fetch projects: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        // Clear the timeout since we got a response
        clearTimeout(timeoutId);

        console.log("Projects fetched on component mount:", data);

        // Reset loading state
        setDirectLoading(false);

        if (Array.isArray(data) && data.length > 0) {
          // Map the projects to ensure they have the correct format
          const mappedProjects = data.map(project => {
            return {
              id: project.id,
              name: project.name || "Unnamed Project",
              client: project.client || { id: project.clientId, name: "Unknown Client" },
              description: project.description || "",
              startDate: project.startDate || project.created_at || new Date().toISOString(),
              endDate: project.endDate || project.updated_at || new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString(),
              status: project.status || "Not Started",
              value: project.value ? (project.value.toString().startsWith('$') ? project.value : `$${project.value}`) : "$0.00",
              hsnCode: project.hsnCode,
              bdm: project.bdm,
              commissionPercentage: project.commissionPercentage,
              commissionAmount: project.commissionAmount
            };
          });

          console.log("Mapped projects on component mount:", mappedProjects);

          // Set the filtered projects directly
          setFilteredProjects(mappedProjects);

          // Show success toast
          toast.success(`Loaded ${mappedProjects.length} projects`);
        } else {
          console.warn("No projects found or invalid data format:", data);

          // Show info toast
          toast.info("No projects found", {
            description: "There are no projects available. You can create a new project using the Add Project button."
          });

          // Set empty array
          setFilteredProjects([]);
        }
      })
      .catch(error => {
        // Clear the timeout since we got a response
        clearTimeout(timeoutId);

        console.error("Error fetching projects on component mount:", error);

        // Reset loading state and set error
        setDirectLoading(false);
        setDirectError(error);

        // Show error toast
        toast.error("Failed to load projects", {
          description: error.message
        });
      });

    // Cleanup function to clear the timeout if the component unmounts
    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  // Update filtered data when API data changes - with minimal logging
  useEffect(() => {
    if (clientsData) {
      setFilteredClients(clientsData);
    }
  }, [clientsData]);

  useEffect(() => {
    console.log("Projects data updated in Clients page:", projectsData);

    // Check if we have real project data
    if (projectsData && projectsData.length > 0) {
      console.log("Using real project data from API:", JSON.stringify(projectsData, null, 2));

      try {
        // Map the projects to ensure they have the correct format
        const mappedProjects = projectsData.map(project => {
          // Create a properly formatted project object with appropriate fields from API response
          return {
            id: project.id,
            name: project.name || "Unnamed Project",
            client: project.client || { id: project.clientId, name: "Unknown Client" },
            description: project.description || "",
            startDate: project.startDate || project.created_at || new Date().toISOString(),
            endDate: project.endDate || project.updated_at || new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString(),
            status: project.status || "Not Started",
            value: project.value ? (project.value.toString().startsWith('$') ? project.value : `$${project.value}`) : "$0.00",
            // Preserve other fields from the API response
            hsnCode: project.hsnCode,
            bdm: project.bdm,
            commissionPercentage: project.commissionPercentage,
            commissionAmount: project.commissionAmount,
            gstNumber: project.gstNumber,
            billingAddress: project.billingAddress,
            shippingAddress: project.shippingAddress,
            engagementCode: project.engagementCode,
            clientPartnerName: project.clientPartnerName,
            clientPartnerEmail: project.clientPartnerEmail,
            clientPartnerPhone: project.clientPartnerPhone
          };
        });

        console.log("Mapped projects in Clients page:", JSON.stringify(mappedProjects, null, 2));

        // Filter by selected client if needed
        if (selectedClient) {
          const filtered = mappedProjects.filter(project => {
            const projectClientName = typeof project.client === 'string'
              ? project.client
              : (project.client?.name || '');
            return projectClientName === selectedClient;
          });

          console.log("Filtered projects for client:", selectedClient, JSON.stringify(filtered, null, 2));

          // If we have filtered results, use them
          if (filtered.length > 0) {
            setFilteredProjects(filtered);
          } else {
            // If no projects match the selected client, show empty list
            setFilteredProjects([]);
          }
        } else {
          // No client selected, show all projects
          setFilteredProjects(mappedProjects);
        }
      } catch (error) {
        console.error("Error processing project data:", error);

        // As a fallback, try to use the raw data directly
        console.warn("Using raw project data as fallback");

        if (selectedClient) {
          const filtered = projectsData.filter(project => {
            try {
              const projectClientName = typeof project.client === 'string'
                ? project.client
                : (project.client?.name || '');
              return projectClientName === selectedClient;
            } catch (e) {
              console.error("Error filtering project:", e);
              return false;
            }
          });

          setFilteredProjects(filtered);
        } else {
          setFilteredProjects(projectsData);
        }
      }
    } else {
      // No data available, set empty array
      console.warn("No project data available from API");
      setFilteredProjects([]);

      // If we're on the projects tab, try to fetch data directly
      if (activeTab === "projects") {
        console.log("On projects tab with no data, fetching directly...");

        // Make a direct fetch to the specific endpoint
        fetch('http://*************:8091/projects/getAll', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': 'Basic ' + btoa('admin:admin123')
          },
          credentials: 'omit'
        })
          .then(response => {
            if (!response.ok) {
              throw new Error(`Failed to fetch projects: ${response.status}`);
            }
            return response.json();
          })
          .then(data => {
            console.log("Projects fetched directly:", data);

            if (Array.isArray(data) && data.length > 0) {
              setFilteredProjects(data);
            }
          })
          .catch(error => {
            console.error("Error fetching projects directly:", error);
          });
      }
    }
  }, [projectsData, selectedClient, activeTab]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value.toLowerCase();
    setSearchTerm(term);

    // Filter clients
    if (clientsData && clientsData.length > 0) {
      const clients = clientsData.filter(
        (client) => client.name.toLowerCase().includes(term)
      );
      setFilteredClients(clients);
    }

    // Filter projects
    if (projectsData && projectsData.length > 0) {
      const projects = selectedClient
        ? projectsData.filter(project => {
            const clientName = typeof project.client === 'string'
              ? project.client
              : (project.client?.name || '');
            return clientName === selectedClient;
          })
        : projectsData.filter(
            (project) =>
              project.name.toLowerCase().includes(term) ||
              (typeof project.client === 'string'
                ? project.client.toLowerCase().includes(term)
                : (project.client?.name || '').toLowerCase().includes(term)
              )
          );
      setFilteredProjects(projects);
    }
  };

  const handleClientClick = (clientName: string) => {
    if (selectedClient === clientName) {
      setSelectedClient(null);
      setFilteredProjects(projectsData || []);
    } else {
      setSelectedClient(clientName);
      if (projectsData && projectsData.length > 0) {
        const projects = projectsData.filter(project => {
          const projectClientName = typeof project.client === 'string'
            ? project.client
            : (project.client?.name || '');
          return projectClientName === clientName;
        });
        setFilteredProjects(projects);
      }
    }
  };

  const handleAddClient = () => {
    setSelectedClient(null);
    setIsClientDialogOpen(true);
  };

  const handleAddProject = () => {
    setSelectedProject(null);
    setIsProjectDialogOpen(true);
  };

  const handleRefreshData = () => {
    // Show loading toast
    const loadingToast = toast.loading("Refreshing projects data...");

    // Set loading state
    setDirectLoading(true);
    setDirectError(null);

    // Create a timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      console.warn("Project fetch timeout reached - stopping loading state");
      setDirectLoading(false);

      // Update the toast
      toast.error("Failed to load projects: Timeout reached", {
        id: loadingToast,
        description: "Please try again or check your network connection."
      });

      // Set error
      setDirectError(new Error("Timeout reached while fetching projects"));
    }, 15000); // 15 second timeout

    // Try direct fetch first with multiple endpoints
    const fetchDirectly = async () => {
      // Create basic auth header
      const authHeader = 'Basic ' + btoa('admin:admin123');

      // Try multiple endpoints in order of preference
      const endpoints = [
        'http://*************:8091/projects/getAll',
        'http://*************:8091/api/projects',
        'http://*************:8091/api/v1/projects',
        'http://*************:8091/api/noauth/getProjects'
      ];

      let fetchSuccess = false;
      let fetchError = null;

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying to fetch projects from endpoint: ${endpoint}`);

          const response = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            credentials: 'omit'
          });

          // Handle 204 No Content response
          if (response.status === 204) {
            console.log('Server returned 204 No Content - no projects available');
            return []; // Return empty array for 204 responses
          }

          if (!response.ok) {
            console.warn(`Endpoint ${endpoint} returned status ${response.status}`);
            continue;
          }

          // Check if response is empty
          const text = await response.text();
          if (!text || text.trim() === '') {
            console.log('Empty response received from projects API');
            return []; // Return empty array for empty responses
          }

          try {
            const data = JSON.parse(text);
            console.log(`Successfully fetched projects from ${endpoint}:`, data);

            if (Array.isArray(data)) {
              fetchSuccess = true;
              return data;
            } else if (data && typeof data === 'object') {
              // Check for common response formats
              if (Array.isArray(data.data)) {
                fetchSuccess = true;
                return data.data;
              } else if (Array.isArray(data.content)) {
                fetchSuccess = true;
                return data.content;
              } else if (data.data && Array.isArray(data.data.content)) {
                fetchSuccess = true;
                return data.data.content;
              }

              // Try to find any array property
              for (const key in data) {
                if (Array.isArray(data[key])) {
                  fetchSuccess = true;
                  return data[key];
                }
              }
            }
          } catch (jsonError) {
            console.error('Error parsing JSON response:', jsonError, 'Response text:', text);
            fetchError = jsonError;
          }
        } catch (endpointError) {
          console.error(`Error fetching from ${endpoint}:`, endpointError);
          fetchError = endpointError;
        }
      }

      // If all endpoints fail, throw the last error
      if (!fetchSuccess) {
        throw fetchError || new Error('Failed to fetch projects from any endpoint');
      }

      return [];
    };

    // First try direct fetch, then fall back to refetchProjects
    fetchDirectly()
      .then(data => {
        // Clear the timeout since we got a response
        clearTimeout(timeoutId);

        console.log("Projects fetched directly:", data);

        // Process the data
        if (Array.isArray(data) && data.length > 0) {
          // Map the projects to ensure they have the correct format
          const mappedProjects = data.map(project => {
            return {
              id: project.id,
              name: project.name || "Unnamed Project",
              client: project.client || { id: project.clientId, name: "Unknown Client" },
              description: project.description || "",
              startDate: project.startDate || project.created_at || new Date().toISOString(),
              endDate: project.endDate || project.updated_at || new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString(),
              status: project.status || "Not Started",
              value: project.value ? (project.value.toString().startsWith('$') ? project.value : `$${project.value}`) : "$0.00",
              // Preserve other fields from the API response
              hsnCode: project.hsnCode,
              bdm: project.bdm,
              commissionPercentage: project.commissionPercentage,
              commissionAmount: project.commissionAmount,
              gstNumber: project.gstNumber,
              billingAddress: project.billingAddress,
              shippingAddress: project.shippingAddress,
              engagementCode: project.engagementCode,
              clientPartnerName: project.clientPartnerName,
              clientPartnerEmail: project.clientPartnerEmail,
              clientPartnerPhone: project.clientPartnerPhone
            };
          });

          console.log("Mapped projects:", mappedProjects);

          // Set the filtered projects directly
          setFilteredProjects(mappedProjects);

          // Reset loading state
          setDirectLoading(false);

          // Update toast
          toast.success(`Loaded ${mappedProjects.length} projects`, {
            id: loadingToast
          });

          // Also try to refresh through the hook
          refetchProjects(true).catch(hookError => {
            console.warn("Hook-based refresh failed, but direct fetch succeeded:", hookError);
          });
        } else {
          // If direct fetch returned no data, try the hook-based approach
          console.log("Direct fetch returned no data, trying hook-based approach");

          // Use the refetchProjects function to refresh the data
          refetchProjects(true)
            .then(() => {
              console.log("Projects refreshed through hook");

              // Reset loading state
              setDirectLoading(false);

              // Update toast
              toast.success("Projects data refreshed successfully", {
                id: loadingToast
              });
            })
            .catch(error => {
              console.error("Error refreshing projects through hook:", error);

              // Reset loading state
              setDirectLoading(false);

              // Set error
              setDirectError(error);

              // Update toast with more helpful message
              toast.error("Failed to refresh projects", {
                id: loadingToast,
                description: "Please check your network connection and try again."
              });
            });
        }
      })
      .catch(error => {
        console.error("Error in direct fetch:", error);

        // Clear the timeout since we got a response
        clearTimeout(timeoutId);

        // Try the hook-based approach as fallback
        console.log("Direct fetch failed, trying hook-based approach");

        // Use the refetchProjects function to refresh the data
        refetchProjects(true)
          .then(() => {
            console.log("Projects refreshed through hook after direct fetch failed");

            // Reset loading state
            setDirectLoading(false);

            // Update toast
            toast.success("Projects data refreshed successfully", {
              id: loadingToast
            });
          })
          .catch(hookError => {
            console.error("Both direct fetch and hook-based refresh failed:", hookError);

            // Reset loading state
            setDirectLoading(false);

            // Set error
            setDirectError(hookError);

            // Update toast with more helpful message
            toast.error("Failed to refresh projects", {
              id: loadingToast,
              description: "Server error occurred. Please try again later or contact support."
            });
          });
      });
  };

  const handleDeleteClient = async (id: string) => {
    // Find the client to get the name
    const client = clientsData.find(c => c.id === id);
    const clientName = client ? client.name : "";

    // Show confirmation dialog using toast
    toast.warning(`WARNING: Deleting client "${clientName}" will also delete all associated projects and invoices!`, {
      duration: 10000, // 10 seconds
      action: {
        label: "Cancel",
        onClick: () => {
          toast.success("Delete operation cancelled");
        }
      }
    });

    // Do not proceed with deletion - this is just a UI update
    // The actual deletion should happen through the ClientActionMenu component
    // which has proper confirmation dialogs

    // Log the attempted deletion for debugging
    console.log(`Client deletion attempted for ID: ${id}, Name: ${clientName}`);
    console.warn("⚠️ IMPORTANT: Client deletion was prevented to avoid data loss");
    console.warn("To delete a client, use the action menu which has proper confirmation");

    // Do not update the UI state or show success message
    // This prevents accidental data loss
  };

  const handleEditClient = async (id: string) => {
    try {
      console.log("Attempting to edit client with ID:", id);
      console.log("Available clients:", clientsData);

      // First, set the dialog to open - this ensures the dialog opens even if there's a delay in finding the client
      setIsClientDialogOpen(true);

      // Try multiple approaches to find the client
      let clientToEdit = null;

      // First try exact match
      clientToEdit = clientsData.find(c => c.id === id);

      // If not found, try string comparison
      if (!clientToEdit) {
        clientToEdit = clientsData.find(c => c.id?.toString() === id);
      }

      // If still not found, try numeric comparison
      if (!clientToEdit) {
        const numericId = parseInt(id, 10);
        if (!isNaN(numericId)) {
          clientToEdit = clientsData.find(c => parseInt(c.id?.toString(), 10) === numericId);
        }
      }

      // If still not found, try finding by name (fallback)
      if (!clientToEdit) {
        // Try to find the client in the filtered clients (which might be more up-to-date)
        clientToEdit = filteredClients.find(c => c.id === id || c.id?.toString() === id);
      }

      if (clientToEdit) {
        console.log("Found client to edit:", clientToEdit);

        // Set the client data for the form
        setSelectedClientForEdit({
          id: clientToEdit.id,
          name: clientToEdit.name
        });

        // Open the dialog
        setIsClientDialogOpen(true);

        toast.success(`Editing client: ${clientToEdit.name}`);
      } else {
        console.error(`Client with ID ${id} not found in available data`);

        // Fetch the client directly from the API as a last resort
        const loadingToast = toast.loading(`Loading client data...`);

        // Create basic auth header
        const authHeader = 'Basic ' + btoa('admin:admin123');

        // Try endpoints, prioritizing the one without /v1
        const endpoints = [
          `http://*************:8091/api/clients/${id}`,
          `http://*************:8091/api/v1/clients/${id}`,
          `http://*************:8091/clients/${id}`
        ];

        let fetchSuccess = false;

        for (const endpoint of endpoints) {
          try {
            console.log(`Trying to fetch client from endpoint: ${endpoint}`);

            const response = await fetch(endpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': authHeader
              },
              credentials: 'omit'
            });

            if (!response.ok) {
              console.warn(`Endpoint ${endpoint} returned status ${response.status}`);
              continue;
            }

            const data = await response.json();
            console.log(`Successfully fetched client from ${endpoint}:`, data);

            // Set the client data for the form
            setSelectedClientForEdit({
              id: data.id,
              name: data.name
            });

            // Open the dialog
            setIsClientDialogOpen(true);

            // Update the toast
            toast.success(`Editing client: ${data.name}`, { id: loadingToast });

            fetchSuccess = true;
            break;
          } catch (endpointError) {
            console.error(`Error fetching from ${endpoint}:`, endpointError);
          }
        }

        if (!fetchSuccess) {
          toast.error(`Client with ID ${id} not found`, { id: loadingToast });

          // Force refresh the clients data
          refetchClients(true);
        }
      }
    } catch (error) {
      console.error('Error in handleEditClient:', error);
      toast.error(`Failed to edit client: ${error instanceof Error ? error.message : 'Unknown error'}`);

      // Force refresh the clients data
      refetchClients(true);
    }
  };

  const handleViewProjects = (clientName: string) => {
    handleClientClick(clientName);
    // Switch to projects tab
    setActiveTab("projects");
    toast.success(`Viewing projects for ${clientName}`);
  };

  const handleEditProject = (id: string) => {
    // Find the project to edit
    const project = projectsData.find(p => p.id === id) || filteredProjects.find(p => p.id === id);
    if (project) {
      console.log("Editing project:", project);

      // Use the actual project data from the API
      setSelectedProject(project);
      setIsProjectDialogOpen(true);
      toast.success(`Editing project: ${project.name}`);
    } else {
      // If project not found in local data, fetch it directly from the API
      const loadingToast = toast.loading(`Loading project data...`);

      // Fetch the project data from the API
      fetch(`http://*************:8091/projects/getById/${id}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Basic ' + btoa('admin:admin123')
        },
        credentials: 'omit'
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`Failed to fetch project: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          console.log("Project fetched for editing:", data);

          // Update the toast
          toast.success(`Editing project: ${data.name}`, { id: loadingToast });

          // Set the selected project and open the dialog
          setSelectedProject(data);
          setIsProjectDialogOpen(true);
        })
        .catch(error => {
          console.error("Error fetching project for editing:", error);
          toast.error(`Error loading project data: ${error.message}`, { id: loadingToast });
        });
    }
  };

  const handleViewInvoices = (id: string) => {
    toast.success(`Viewing invoices for project with ID: ${id}`);
    // In a real app, you would navigate to the invoices page for this project
  };

  const handleDeleteProject = (id: string) => {
    // Find the project to get the name
    const project = filteredProjects.find(p => p.id === id);
    const projectName = project ? project.name : "Unknown Project";

    // Show a confirmation dialog first
    if (!window.confirm(`Are you sure you want to delete project "${projectName}"? This action cannot be undone and may affect related data.`)) {
      toast.info("Project deletion cancelled");
      return;
    }

    // Show confirmation toast
    toast.promise(
      // Create a promise that resolves when the project is deleted
      new Promise((resolve, reject) => {
        // Log the deletion attempt
        console.log(`Attempting to delete project: ${projectName} (ID: ${id})`);

        // Make the API call to delete the project
        fetch(`http://*************:8091/projects/delete/${id}`, {
          method: 'DELETE',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': 'Basic ' + btoa('admin:admin123')
          },
          credentials: 'omit'
        })
          .then(response => {
            if (!response.ok) {
              throw new Error(`Failed to delete project: ${response.status}`);
            }

            // Remove project from the local list
            const updatedProjects = filteredProjects.filter(project => project.id !== id);
            setFilteredProjects(updatedProjects);

            // Resolve the promise
            resolve(`Project "${projectName}" deleted successfully`);

            // Refresh the projects data with force refresh
            refetchProjects(true);
          })
          .catch(error => {
            console.error("Error deleting project:", error);
            reject(error);
          });
      }),
      {
        loading: `Deleting project "${projectName}"...`,
        success: (message) => `${message}`,
        error: (error: Error) => `Failed to delete project: ${error.message}`
      }
    );
  };

  const handleStatusChange = (projectId: string, newStatus: string) => {
    // Find the project to update
    const project = filteredProjects.find(p => p.id === projectId);

    if (!project) {
      toast.error("Project not found");
      return;
    }

    // Update the project status in the local state first for immediate feedback
    const updatedProjects = filteredProjects.map(p =>
      p.id === projectId ? { ...p, status: newStatus } : p
    );
    setFilteredProjects(updatedProjects);

    // Show loading toast
    const loadingToast = toast.loading(`Updating project status to: ${newStatus}...`);

    // Create the update payload
    const updatePayload = {
      ...project,
      status: newStatus
    };

    // Make the API call to update the project
    fetch(`http://*************:8091/projects/update/${projectId}`, {
      method: 'PUT',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': 'Basic ' + btoa('admin:admin123')
      },
      body: JSON.stringify(updatePayload),
      credentials: 'omit'
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`Failed to update project status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        console.log("Project status updated successfully:", data);

        // Update the toast
        toast.success(`Project status updated to: ${newStatus}`, { id: loadingToast });

        // Refresh the projects data with force refresh
        refetchProjects(true);
      })
      .catch(error => {
        console.error("Error updating project status:", error);

        // Update the toast
        toast.error(`Failed to update project status: ${error.message}`, { id: loadingToast });

        // Revert the local state change
        setFilteredProjects(filteredProjects);
      });
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Clients & Projects</h2>
        <p className="text-muted-foreground">Manage your clients and their associated projects.</p>
      </div>

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search clients or projects..."
            className="pl-8 w-full md:w-[300px]"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        <Button onClick={handleAddClient}>
          <Plus className="mr-2 h-4 w-4" /> Add Client
        </Button>
      </div>

      <Tabs
        className="relative tabs-container"
        value={activeTab}
        onValueChange={(value) => {
          setActiveTab(value);

          // If switching to projects tab, fetch fresh data
          if (value === "projects") {
            console.log("Switching to projects tab, fetching fresh data...");

            // Show loading toast
            const loadingToast = toast.loading("Loading projects...");

            // Set loading state
            setDirectLoading(true);
            setDirectError(null);

            // Create a timeout to prevent infinite loading
            const timeoutId = setTimeout(() => {
              console.warn("Project fetch timeout reached in tab handler - stopping loading state");
              setDirectLoading(false);

              // Update the toast
              toast.error("Failed to load projects: Timeout reached", {
                id: loadingToast,
                description: "Please try refreshing the page or check your network connection."
              });

              // Set error
              setDirectError(new Error("Timeout reached while fetching projects"));
            }, 10000); // 10 second timeout

            // Make a direct fetch to the specific endpoint
            fetch('http://*************:8091/projects/getAll', {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': 'Basic ' + btoa('admin:admin123')
              },
              credentials: 'omit'
            })
              .then(response => {
                // Handle 204 No Content response
                if (response.status === 204) {
                  console.log('Server returned 204 No Content - no projects available');
                  return []; // Return empty array for 204 responses
                }

                if (!response.ok) {
                  throw new Error(`Failed to fetch projects: ${response.status}`);
                }

                // Check if response is empty
                return response.text().then(text => {
                  if (!text || text.trim() === '') {
                    console.log('Empty response received from projects API');
                    return []; // Return empty array for empty responses
                  }

                  try {
                    return JSON.parse(text);
                  } catch (error) {
                    console.error('Error parsing JSON response:', error, 'Response text:', text);
                    throw new Error('Invalid JSON response from server');
                  }
                });
              })
              .then(data => {
                // Clear the timeout since we got a response
                clearTimeout(timeoutId);

                console.log("Projects fetched from specific endpoint:", data);

                // Reset loading state
                setDirectLoading(false);

                // Process the data directly
                if (Array.isArray(data)) {
                  // Map the projects to ensure they have the correct format
                  const mappedProjects = data.map(project => {
                    // Create a properly formatted project object with appropriate fields from API response
                    return {
                      id: project.id,
                      name: project.name || "Unnamed Project",
                      client: project.client || { id: project.clientId, name: "Unknown Client" },
                      description: project.description || "",
                      startDate: project.startDate || project.created_at || new Date().toISOString(),
                      endDate: project.endDate || project.updated_at || new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString(),
                      status: project.status || "Not Started",
                      value: project.value ? (project.value.toString().startsWith('$') ? project.value : `$${project.value}`) : "$0.00",
                      // Preserve other fields from the API response
                      hsnCode: project.hsnCode,
                      bdm: project.bdm,
                      commissionPercentage: project.commissionPercentage,
                      commissionAmount: project.commissionAmount,
                      gstNumber: project.gstNumber,
                      billingAddress: project.billingAddress,
                      shippingAddress: project.shippingAddress,
                      engagementCode: project.engagementCode,
                      clientPartnerName: project.clientPartnerName,
                      clientPartnerEmail: project.clientPartnerEmail,
                      clientPartnerPhone: project.clientPartnerPhone
                    };
                  });

                  console.log("Mapped projects:", mappedProjects);

                  // Set the filtered projects directly
                  setFilteredProjects(mappedProjects);

                  // Update the toast
                  toast.success(`Loaded ${data.length} projects`, { id: loadingToast });
                } else {
                  console.warn("API returned non-array data:", data);
                  toast.success("Projects loaded successfully", { id: loadingToast });
                  setFilteredProjects([]);
                }

                // Refresh the projects data through the hook as well
                refetchProjects();
              })
              .catch(error => {
                // Clear the timeout since we got a response
                clearTimeout(timeoutId);

                console.error("Error fetching projects:", error);

                // Reset loading state and set error
                setDirectLoading(false);
                setDirectError(error);

                toast.error(`Failed to load projects: ${error.message}`, { id: loadingToast });
              });

              // Return a cleanup function to clear the timeout if the component unmounts or tab changes
              return () => {
                clearTimeout(timeoutId);
              };
          }
        }}>
        <TabsList>
          <TabsTrigger value="clients">Clients</TabsTrigger>
          <TabsTrigger
            value="projects"
            onClick={() => {
              // This is handled by the onValueChange above
              // No need for duplicate code here
            }}
          >
            Projects
          </TabsTrigger>
        </TabsList>
        <TabsContent value="clients" className="space-y-4 relative z-10">
          <Card>
            <CardHeader>
              <CardTitle>Clients</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-hidden">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {clientsLoading ? (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center py-10">
                            <div className="flex flex-col items-center justify-center">
                              <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                              <span className="text-muted-foreground">Loading clients...</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : clientsError ? (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center py-10 text-red-500">
                            <div className="flex flex-col items-center justify-center">
                              <span className="font-bold mb-2">Error loading clients</span>
                              <span className="text-sm mb-4">
                                {clientsError instanceof Error
                                  ? clientsError.message
                                  : 'An unexpected error occurred. Please try again.'}
                              </span>
                              <div className="flex gap-2">
                                <Button
                                  variant="outline"
                                  onClick={() => refetchClients()}
                                >
                                  Retry
                                </Button>

                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : filteredClients.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center py-6 text-muted-foreground">
                            No clients found
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredClients.map((client) => (
                          <TableRow key={client.id} className={client.name === selectedClient ? "bg-muted/50" : ""}>
                            <TableCell className="font-medium">{client.name}</TableCell>
                            <TableCell>
                              {client.createdAt || client.created_at ?
                                new Date(client.createdAt || client.created_at).toLocaleDateString() :
                                'N/A'}
                            </TableCell>
                            <TableCell className="text-right">
                              <ClientActionMenu
                                clientId={client.id}
                                clientName={client.name}
                                onEdit={handleEditClient}
                                onViewProjects={handleViewProjects}
                                onDelete={handleDeleteClient}
                                refetchClients={() => refetchClients(true)}
                              />
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="projects" className="space-y-4 relative z-10">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>
                  Projects
                  {selectedClient && (
                    <span className="ml-2 text-muted-foreground text-sm font-normal">
                      for {selectedClient}
                    </span>
                  )}
                </CardTitle>
                <Button onClick={handleAddProject} size="sm">
                  <Plus className="mr-2 h-4 w-4" /> Add Project
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-hidden">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Client</TableHead>
                        <TableHead>Timeline</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Value</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {(projectsLoading || directLoading) ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-10">
                            <div className="flex flex-col items-center justify-center">
                              <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                              <span className="text-muted-foreground">Loading projects...</span>
                              {/* Add a refresh button if loading takes too long */}
                              {directLoading && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="mt-4"
                                  onClick={() => {
                                    // Force stop loading and refresh
                                    setDirectLoading(false);
                                    window.location.reload();
                                  }}
                                >
                                  <RefreshCcw className="mr-2 h-4 w-4" />
                                  Refresh Page
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (projectsError || directError) ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-10 text-red-500">
                            <div className="flex flex-col items-center justify-center">
                              <span className="font-bold mb-2">Error loading projects</span>
                              <span className="text-sm mb-4">
                                {(projectsError instanceof Error || directError instanceof Error)
                                  ? (projectsError instanceof Error ? projectsError.message : directError?.message)
                                  : 'An unexpected error occurred. Please try again.'}
                              </span>
                              <div className="flex gap-2">
                                <Button
                                  variant="outline"
                                  onClick={() => {
                                    // Clear direct error
                                    setDirectError(null);

                                    // Retry fetching projects with force refresh
                                    refetchProjects(true);

                                    // Also make a direct fetch
                                    setDirectLoading(true);
                                    fetch('http://*************:8091/projects/getAll', {
                                      method: 'GET',
                                      headers: {
                                        'Accept': 'application/json',
                                        'Content-Type': 'application/json',
                                        'Authorization': 'Basic ' + btoa('admin:admin123')
                                      },
                                      credentials: 'omit'
                                    })
                                      .then(response => {
                                        // Handle 204 No Content response
                                        if (response.status === 204) {
                                          console.log('Server returned 204 No Content - no projects available');
                                          return []; // Return empty array for 204 responses
                                        }

                                        if (!response.ok) {
                                          throw new Error(`Failed to fetch projects: ${response.status}`);
                                        }

                                        // Check if response is empty
                                        return response.text().then(text => {
                                          if (!text || text.trim() === '') {
                                            console.log('Empty response received from projects API');
                                            return []; // Return empty array for empty responses
                                          }

                                          try {
                                            return JSON.parse(text);
                                          } catch (error) {
                                            console.error('Error parsing JSON response:', error, 'Response text:', text);
                                            throw new Error('Invalid JSON response from server');
                                          }
                                        });
                                      })
                                      .then(data => {
                                        console.log("Projects fetched on retry:", data);
                                        setDirectLoading(false);

                                        if (Array.isArray(data) && data.length > 0) {
                                          setFilteredProjects(data);
                                          toast.success(`Loaded ${data.length} projects`);
                                        }
                                      })
                                      .catch(error => {
                                        console.error("Error fetching projects on retry:", error);
                                        setDirectLoading(false);
                                        setDirectError(error);
                                        toast.error(`Failed to load projects: ${error.message}`);
                                      });
                                  }}
                                >
                                  Retry
                                </Button>

                                <Button
                                  variant="default"
                                  onClick={() => {
                                    // Refresh the page
                                    window.location.reload();
                                  }}
                                >
                                  Refresh Page
                                </Button>
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : filteredProjects.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-6">
                            <div className="flex flex-col items-center justify-center space-y-2">
                              <span className="text-muted-foreground">
                                {selectedClient
                                  ? `No projects found for ${selectedClient}`
                                  : 'No projects found'}
                              </span>
                              <span className="text-sm text-muted-foreground">
                                {directError ?
                                  `Error: ${directError.message}` :
                                  'Click the "Add Project" button above or refresh the data'}
                              </span>
                              <div className="flex gap-2 mt-4">
                                <Button
                                  variant="default"
                                  size="sm"
                                  onClick={() => handleRefreshData()}
                                  className="bg-blue-500 hover:bg-blue-600 text-white"
                                >
                                  <RefreshCcw className="mr-2 h-4 w-4" />
                                  Refresh Data
                                </Button>
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredProjects.map((project) => (
                          <TableRow key={project.id}>
                            <TableCell className="font-medium">{project.name}</TableCell>
                            <TableCell>
                              {typeof project.client === 'string'
                                ? project.client
                                : (project.client?.name || 'Unknown client')}
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-col">
                                <span className="text-sm">
                                  Start: {project.startDate ? new Date(project.startDate).toLocaleDateString() : 'Not set'}
                                </span>
                                <span className="text-xs text-muted-foreground">
                                  End: {project.endDate ? new Date(project.endDate).toLocaleDateString() : 'Not set'}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <StatusDropdown
                                currentStatus={project.status || 'Not set'}
                                onStatusChange={(newStatus) => handleStatusChange(project.id, newStatus)}
                              />
                            </TableCell>
                            <TableCell>
                              {typeof project.value === 'number'
                                ? `$${project.value.toFixed(2)}`
                                : (project.value
                                    ? (project.value.toString().startsWith('$')
                                        ? project.value
                                        : `$${project.value}`)
                                    : "$0.00")}
                            </TableCell>
                            <TableCell className="text-right">
                              <ActionMenuSimple
                                projectId={project.id}
                                onEdit={handleEditProject}
                                onView={handleViewInvoices}
                                onDelete={handleDeleteProject}
                              />
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>



      <ClientFormDialog
        open={isClientDialogOpen}
        onOpenChange={(open) => {
          setIsClientDialogOpen(open);
          if (!open) {
            // Clear the selected client when the dialog is closed
            console.log("Dialog closed, clearing selectedClientForEdit");
            setSelectedClientForEdit(null);
          }
        }}
        client={selectedClientForEdit}
        onSave={async (clientData) => {
          console.log("Saving client data:", clientData);
          try {
            // Create a loading toast
            const loadingToast = toast.loading(selectedClientForEdit ? "Updating client..." : "Creating client...");

            // Determine if we're creating or updating
            const isEditing = !!selectedClientForEdit?.id;

            if (isEditing) {
              console.log(`Updating client with ID: ${selectedClientForEdit.id}`);

              // Make the API call to update the client
              const response = await fetch(`http://*************:8091/api/clients/${selectedClientForEdit.id}`, {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                  'Authorization': 'Basic ' + btoa('admin:admin123')
                },
                body: JSON.stringify({
                  ...clientData,
                  id: selectedClientForEdit.id
                })
              });

              if (!response.ok) {
                let errorMessage = "";
                try {
                  // Try to parse the error as JSON
                  const errorJson = await response.json();
                  console.error("Error updating client (JSON):", errorJson);
                  errorMessage = errorJson.message || errorJson.error || JSON.stringify(errorJson);
                } catch (e) {
                  // If not JSON, get as text
                  const errorText = await response.text();
                  console.error("Error updating client (Text):", errorText);
                  errorMessage = errorText;
                }

                toast.error("Failed to update client", {
                  description: `Server returned ${response.status}: ${errorMessage}`,
                  id: loadingToast
                });
                return;
              }

              let savedClient: any;
              try {
                savedClient = await response.json();
              } catch (e) {
                // Some APIs might not return JSON for updates
                console.log("No JSON response for update, using client data");
                savedClient = {
                  ...clientData,
                  id: selectedClientForEdit.id
                };
              }

              console.log("Client updated successfully:", savedClient);

              // Update the toast
              toast.success("Client updated successfully", {
                id: loadingToast
              });
            } else {
              // Make the API call to create a new client
              console.log("Creating new client");
              console.log("Sending client data to API:", JSON.stringify(clientData, null, 2));

              const response = await fetch('http://*************:8091/api/clients', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                  'Authorization': 'Basic ' + btoa('admin:admin123')
                },
                body: JSON.stringify(clientData)
              });

              if (!response.ok) {
                let errorMessage = "";
                try {
                  // Try to parse the error as JSON
                  const errorJson = await response.json();
                  console.error("Error saving client (JSON):", errorJson);
                  errorMessage = errorJson.message || errorJson.error || JSON.stringify(errorJson);
                } catch (e) {
                  // If not JSON, get as text
                  const errorText = await response.text();
                  console.error("Error saving client (Text):", errorText);
                  errorMessage = errorText;
                }

                toast.error("Failed to save client", {
                  description: `Server returned ${response.status}: ${errorMessage}`,
                  id: loadingToast
                });
                return;
              }

              const savedClient = await response.json();
              console.log("Client saved successfully:", savedClient);

              // Update the toast
              toast.success("Client created successfully", {
                id: loadingToast
              });
            }

            // Close the dialog
            setIsClientDialogOpen(false);

            // Clear the selected client for edit
            setSelectedClientForEdit(null);

            // Refresh the clients list with force refresh
            refetchClients(true);
          } catch (error) {
            console.error("Error saving client:", error);
            toast.error("Failed to save client", {
              description: error instanceof Error ? error.message : "Unknown error"
            });
          }
        }}
      />

      <DynamicProjectFormDialog
        open={isProjectDialogOpen}
        onOpenChange={(open) => {
          setIsProjectDialogOpen(open);
          // Only clear selectedProject when dialog is closed
          if (!open) {
            setSelectedProject(null);
          }
        }}
        projectId={selectedProject?.id}
        defaultValues={selectedProject}
        clients={clientsData.map(client => ({ id: client.id, name: client.name }))}
        onSave={async (projectData) => {
          try {
            // Determine if we're creating or updating
            const isEditing = !!selectedProject?.id;

            // Create a loading toast
            const loadingToast = toast.loading(isEditing ? "Updating project..." : "Creating project...");

            // Log the project data
            console.log(`Original ${isEditing ? 'update' : 'create'} project data:`, JSON.stringify(projectData, null, 2));

            let savedProject: any;

            if (isEditing) {
              // Use the project service to update the project
              console.log(`Updating project with ID: ${selectedProject.id} (type: ${typeof selectedProject.id})`);

              // Ensure we have a valid ID
              if (!selectedProject.id) {
                throw new Error("Cannot update project: Missing project ID");
              }

              // Make sure projectData includes the ID
              const projectDataWithId = {
                ...projectData,
                id: selectedProject.id
              };

              // Update the project
              savedProject = await projectService.updateProject(selectedProject.id, projectDataWithId);
              console.log("Project updated successfully:", savedProject);

              // Also fetch the specific project to ensure we have the latest data
              try {
                const response = await fetch(`http://*************:8091/projects/getById/${selectedProject.id}`, {
                  method: 'GET',
                  headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'Authorization': 'Basic ' + btoa('admin:admin123')
                  },
                  credentials: 'omit'
                });

                if (response.ok) {
                  const updatedProject = await response.json();
                  console.log("Fetched updated project:", updatedProject);

                  // Update the selected project with the latest data
                  setSelectedProject(updatedProject);

                  // Also update the project in the filtered projects list
                  setFilteredProjects(prev =>
                    prev.map(p => p.id === updatedProject.id ? updatedProject : p)
                  );
                }
              } catch (fetchError) {
                console.error("Error fetching updated project:", fetchError);
              }

              // Update the toast
              toast.success("Project updated successfully", {
                id: loadingToast
              });
            } else {
              // Use the project service to create the project
              savedProject = await projectService.createProject(projectData);
              console.log("Project created successfully:", savedProject);

              // Update the toast
              toast.success("Project created successfully", {
                id: loadingToast
              });
            }

            // Close the dialog
            setIsProjectDialogOpen(false);

            // Clear the project cache to force a fresh fetch
            // This is a workaround to clear the cache in useEntityData
            localStorage.removeItem('projects_cache');

            // Refresh the projects list with a forced refresh
            await refetchProjects(true); // Pass true to force refresh

            // Force a direct fetch to ensure we have the latest data
            setDirectLoading(true);
            try {
              const response = await fetch('http://*************:8091/projects/getAll', {
                method: 'GET',
                headers: {
                  'Accept': 'application/json',
                  'Content-Type': 'application/json',
                  'Authorization': 'Basic ' + btoa('admin:admin123')
                },
                credentials: 'omit'
              });

              // Handle 204 No Content response
              if (response.status === 204) {
                console.log('Server returned 204 No Content - no projects available');
                setFilteredProjects([]);
                setDirectLoading(false);
                return;
              }

              if (!response.ok) {
                throw new Error(`Failed to fetch projects: ${response.status}`);
              }

              // Check if response is empty
              const text = await response.text();
              if (!text || text.trim() === '') {
                console.log('Empty response received from projects API');
                setFilteredProjects([]);
                setDirectLoading(false);
                return;
              }

              let data: any;
              try {
                data = JSON.parse(text);
              } catch (error) {
                console.error('Error parsing JSON response:', error, 'Response text:', text);
                setDirectError(new Error('Invalid JSON response from server'));
                setDirectLoading(false);
                return;
              }
              console.log("Projects refreshed after save:", data);

              if (Array.isArray(data) && data.length > 0) {
                // Map the projects to ensure they have the correct format
                const mappedProjects = data.map(project => {
                  return {
                    id: project.id,
                    name: project.name || "Unnamed Project",
                    client: project.client || { id: project.clientId, name: "Unknown Client" },
                    description: project.description || "",
                    startDate: project.startDate || project.created_at || new Date().toISOString(),
                    endDate: project.endDate || project.updated_at || new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString(),
                    status: project.status || "Not Started",
                    value: project.value ? (project.value.toString().startsWith('$') ? project.value : `$${project.value}`) : "$0.00",
                    hsnCode: project.hsnCode,
                    bdm: project.bdm,
                    commissionPercentage: project.commissionPercentage,
                    commissionAmount: project.commissionAmount
                  };
                });

                // Set the filtered projects directly
                setFilteredProjects(mappedProjects);

                // Also update the projectsData directly to ensure it's in sync
                // This is a workaround since we can't directly modify the hook's state
                const projectsDataCopy = [...mappedProjects];
                (window as any).__projectsData = projectsDataCopy;

                // Force a re-render by setting a state
                setDirectLoading(false);
              }
            } catch (error) {
              console.error("Error refreshing projects after save:", error);
              setDirectLoading(false);
            }
          } catch (error) {
            console.error("Error saving project:", error);

            // Get detailed error message
            const errorMessage = error instanceof Error ? error.message : "Unknown error";

            // Show error toast with more details
            toast.error("Failed to save project", {
              description: errorMessage
            });

            // If it's an update error, show additional guidance
            if (selectedProject?.id && errorMessage.includes("Could not update project with ID")) {
              console.log("Providing additional guidance for update error");

              // Show a second toast with more guidance
              setTimeout(() => {
                toast.error("Update Error Guidance", {
                  description: "Please check that the project ID is valid and all required fields are filled correctly."
                });
              }, 1000);
            }
          }
        }}
      />
    </div>
  );
};

export default Clients;
