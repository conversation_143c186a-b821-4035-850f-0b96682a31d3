package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.GeneratedDocumentDto;
import com.redberyl.invoiceapp.service.GeneratedDocumentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/generated-documents")
@Tag(name = "Generated Document", description = "Generated Document management API")
public class GeneratedDocumentController {

    @Autowired
    private GeneratedDocumentService generatedDocumentService;

    @GetMapping
    @Operation(summary = "Get all generated documents", description = "Retrieve a list of all generated documents")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<GeneratedDocumentDto>> getAllGeneratedDocuments() {
        List<GeneratedDocumentDto> generatedDocuments = generatedDocumentService.getAllGeneratedDocuments();
        return new ResponseEntity<>(generatedDocuments, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get generated document by ID", description = "Retrieve a generated document by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<GeneratedDocumentDto> getGeneratedDocumentById(@PathVariable Long id) {
        GeneratedDocumentDto generatedDocument = generatedDocumentService.getGeneratedDocumentById(id);
        return new ResponseEntity<>(generatedDocument, HttpStatus.OK);
    }

    @GetMapping("/template/{templateId}")
    @Operation(summary = "Get generated documents by template ID", description = "Retrieve all generated documents for a specific template")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<GeneratedDocumentDto>> getGeneratedDocumentsByTemplateId(@PathVariable Long templateId) {
        List<GeneratedDocumentDto> generatedDocuments = generatedDocumentService.getGeneratedDocumentsByTemplateId(templateId);
        return new ResponseEntity<>(generatedDocuments, HttpStatus.OK);
    }

    @GetMapping("/client/{clientId}")
    @Operation(summary = "Get generated documents by client ID", description = "Retrieve all generated documents for a specific client")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<GeneratedDocumentDto>> getGeneratedDocumentsByClientId(@PathVariable Long clientId) {
        List<GeneratedDocumentDto> generatedDocuments = generatedDocumentService.getGeneratedDocumentsByClientId(clientId);
        return new ResponseEntity<>(generatedDocuments, HttpStatus.OK);
    }

    @GetMapping("/deal/{dealId}")
    @Operation(summary = "Get generated documents by deal ID", description = "Retrieve all generated documents for a specific deal")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<GeneratedDocumentDto>> getGeneratedDocumentsByDealId(@PathVariable Long dealId) {
        List<GeneratedDocumentDto> generatedDocuments = generatedDocumentService.getGeneratedDocumentsByDealId(dealId);
        return new ResponseEntity<>(generatedDocuments, HttpStatus.OK);
    }

    @GetMapping("/status/{status}")
    @Operation(summary = "Get generated documents by status", description = "Retrieve all generated documents with a specific status")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<GeneratedDocumentDto>> getGeneratedDocumentsByStatus(@PathVariable String status) {
        List<GeneratedDocumentDto> generatedDocuments = generatedDocumentService.getGeneratedDocumentsByStatus(status);
        return new ResponseEntity<>(generatedDocuments, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create generated document", description = "Create a new generated document")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<GeneratedDocumentDto> createGeneratedDocument(@Valid @RequestBody GeneratedDocumentDto generatedDocumentDto) {
        GeneratedDocumentDto createdGeneratedDocument = generatedDocumentService.createGeneratedDocument(generatedDocumentDto);
        return new ResponseEntity<>(createdGeneratedDocument, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update generated document", description = "Update an existing generated document")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<GeneratedDocumentDto> updateGeneratedDocument(@PathVariable Long id, @Valid @RequestBody GeneratedDocumentDto generatedDocumentDto) {
        GeneratedDocumentDto updatedGeneratedDocument = generatedDocumentService.updateGeneratedDocument(id, generatedDocumentDto);
        return new ResponseEntity<>(updatedGeneratedDocument, HttpStatus.OK);
    }

    @PutMapping("/{id}/status/{status}")
    @Operation(summary = "Update generated document status", description = "Update the status of an existing generated document")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<GeneratedDocumentDto> updateGeneratedDocumentStatus(@PathVariable Long id, @PathVariable String status) {
        GeneratedDocumentDto updatedGeneratedDocument = generatedDocumentService.updateGeneratedDocumentStatus(id, status);
        return new ResponseEntity<>(updatedGeneratedDocument, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete generated document", description = "Delete a generated document by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteGeneratedDocument(@PathVariable Long id) {
        generatedDocumentService.deleteGeneratedDocument(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
