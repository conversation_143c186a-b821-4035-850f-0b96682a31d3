import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronDown } from "lucide-react";

export interface StatusOption {
  value: string;
  label: string;
  color: string;
}

interface StatusDropdownProps {
  currentStatus: string;
  onStatusChange: (newStatus: string) => void;
  statusOptions: StatusOption[];
}

const StatusDropdown: React.FC<StatusDropdownProps> = ({
  currentStatus,
  onStatusChange,
  statusOptions,
}) => {
  const getCurrentStatusColor = () => {
    const option = statusOptions.find(opt => opt.value.toLowerCase() === currentStatus.toLowerCase());
    return option ? option.color : "bg-gray-100 text-gray-800 border-gray-200";
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 p-0 flex items-center gap-1 hover:bg-transparent">
          <Badge className={`${getCurrentStatusColor()} px-2 py-1`} variant="outline">
            {currentStatus}
          </Badge>
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[120px]">
        {statusOptions.map((option) => (
          <DropdownMenuItem 
            key={option.value}
            onClick={() => onStatusChange(option.value)}
            className="cursor-pointer"
          >
            <Badge className={`${option.color} w-full justify-center`} variant="outline">
              {option.label}
            </Badge>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default StatusDropdown;
