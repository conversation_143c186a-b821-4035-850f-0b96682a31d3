import React, { useState } from 'react';
import {
  MoreH<PERSON>zon<PERSON>,
  Eye,
  Edit,
  Trash2,
  FileText,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';

interface ActionMenuProps {
  itemId: string;
  onView?: (id: string) => void;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  onGenerate?: (id: string) => void;
  viewLabel?: string;
  editLabel?: string;
  deleteLabel?: string;
  generateLabel?: string;
  deleteDialogTitle?: string;
  deleteDialogDescription?: string;
  showGenerate?: boolean;
}

const ActionMenu: React.FC<ActionMenuProps> = ({
  itemId,
  onView,
  onEdit,
  onDelete,
  onGenerate,
  viewLabel = 'View',
  editLabel = 'Edit',
  deleteLabel = 'Delete',
  generateLabel = 'Generate',
  deleteDialogTitle = 'Delete Item',
  deleteDialogDescription = 'Are you sure you want to delete this item? This action cannot be undone.',
  showGenerate = false,
}) => {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const handleView = () => {
    if (onView) {
      console.log("ActionMenu: View clicked for item", itemId);
      onView(itemId);
    } else {
      console.warn("ActionMenu: onView handler is not defined");
    }
    setIsDropdownOpen(false);
  };

  const handleEdit = () => {
    if (onEdit) {
      console.log("ActionMenu: Edit clicked for item", itemId);
      onEdit(itemId);
    } else {
      console.warn("ActionMenu: onEdit handler is not defined");
    }
    setIsDropdownOpen(false);
  };

  const handleDeleteClick = () => {
    console.log("ActionMenu: Delete clicked for item", itemId);
    setIsDeleteDialogOpen(true);
    setIsDropdownOpen(false);
  };

  const handleDeleteConfirm = async () => {
    if (onDelete) {
      try {
        console.log("ActionMenu: Confirming delete for item", itemId);
        await onDelete(itemId);
      } catch (error) {
        console.error("ActionMenu: Error in delete handler:", error);
      }
    } else {
      console.warn("ActionMenu: onDelete handler is not defined");
    }
    setIsDeleteDialogOpen(false);
  };

  const handleGenerate = () => {
    if (onGenerate) {
      console.log("ActionMenu: Generate clicked for item", itemId);
      onGenerate(itemId);
    } else {
      console.warn("ActionMenu: onGenerate handler is not defined");
    }
    setIsDropdownOpen(false);
  };

  return (
    <>
      <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0 focus-visible:ring-0 hover:bg-gray-100">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-5 w-5" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[160px]">
          {onView && (
            <DropdownMenuItem onClick={handleView}>
              <Eye className="mr-2 h-4 w-4" />
              <span>{viewLabel}</span>
            </DropdownMenuItem>
          )}
          {onEdit && (
            <DropdownMenuItem onClick={handleEdit}>
              <Edit className="mr-2 h-4 w-4" />
              <span>{editLabel}</span>
            </DropdownMenuItem>
          )}
          {showGenerate && onGenerate && (
            <DropdownMenuItem onClick={handleGenerate}>
              <FileText className="mr-2 h-4 w-4" />
              <span>{generateLabel}</span>
            </DropdownMenuItem>
          )}
          {onDelete && (
            <DropdownMenuItem
              onClick={handleDeleteClick}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              <span>{deleteLabel}</span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{deleteDialogTitle}</AlertDialogTitle>
            <AlertDialogDescription>
              {deleteDialogDescription}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default ActionMenu;
