import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Trash,
  Loader2
} from "lucide-react";
import { useEntityData } from "@/hooks/useEntityData";
import { BdmFormDialog } from "@/components/bdms/BdmFormDialog";
import { toast } from "sonner";
import { api } from "@/services/api";
import { AUTH_CONFIG } from "@/config/api";
import { getProxiedUrl, getBasicAuthHeader } from "@/utils/apiUtils";

// BDM Action Menu Component
const BdmActionMenu = ({
  bdmId,
  bdmName,
  onEdit,
  onDelete
}: {
  bdmId: string | number;
  bdmName: string;
  onEdit: (id: string | number) => void;
  onDelete: (id: string | number, name: string) => void;
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => onEdit(bdmId)}>
          <Edit className="mr-2 h-4 w-4" />
          Edit BDM
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => onDelete(bdmId, bdmName)}
          className="text-red-600"
        >
          <Trash className="mr-2 h-4 w-4" />
          Delete BDM
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const BDMs = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isBdmDialogOpen, setIsBdmDialogOpen] = useState(false);
  const [selectedBdm, setSelectedBdm] = useState<any>(null);

  // Fetch BDMs data from the real API
  const {
    data: bdmsData,
    loading: bdmsLoading,
    error: bdmsError,
    refetch: refetchBdms
  } = useEntityData({
    entityType: 'bdms',
    useMockData: false, // Use real API data, not mock data
    refetchInterval: 0, // Don't automatically refetch
    fallbackToMockData: true, // Use mock data if API fails
    cacheTime: 5 * 60 * 1000 // 5 minutes cache time
  });

  // Filtered data state
  const [filteredBdms, setFilteredBdms] = useState<any[]>([]);

  // Check if the backend server is running and fetch initial data
  useEffect(() => {
    const checkServerConnection = async () => {
      try {
        // Create basic auth header
        const authHeader = getBasicAuthHeader();

        console.log('Checking backend server connection and fetching BDMs...');

        // Try multiple endpoints in sequence
        const endpoints = [
          '/api/bdms',
          '/bdms',
          '/api/v1/bdms',
          'http://*************:8091/api/bdms',
          'http://*************:8091/bdms'
        ];

        let success = false;

        for (const endpoint of endpoints) {
          if (success) break;

          try {
            console.log(`Trying to fetch BDMs from ${endpoint}`);
            const response = await fetch(endpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': authHeader
              },
              credentials: 'include'
            });

            if (!response.ok) {
              console.error(`Fetch from ${endpoint} failed with status: ${response.status}`);
              continue;
            }

            const responseData = await response.json();
            console.log(`Successfully fetched data from ${endpoint}:`, responseData);

            // Process the data based on its format
            let data;
            if (Array.isArray(responseData)) {
              data = responseData;
              success = true;
            } else if (responseData && typeof responseData === 'object') {
              // Check if the response is in ApiResponseDto format
              if ('data' in responseData && 'success' in responseData) {
                console.log('Response is in ApiResponseDto format');

                if (responseData.success) {
                  if (Array.isArray(responseData.data)) {
                    data = responseData.data;
                    success = true;
                  } else if (responseData.data && 'content' in responseData.data && Array.isArray(responseData.data.content)) {
                    data = responseData.data.content;
                    success = true;
                  }
                } else {
                  console.warn('API returned error response:', responseData.message);
                  continue;
                }
              } else if ('content' in responseData && Array.isArray(responseData.content)) {
                data = responseData.content;
                success = true;
              } else if (responseData.id && responseData.name) {
                // Single BDM object
                data = [responseData];
                success = true;
              }
            }

            // If we got data, use it directly
            if (success && data && data.length > 0) {
              console.log('Setting filtered BDMs with data:', data);
              setFilteredBdms(data);
              console.log(`Successfully loaded ${data.length} BDMs from ${endpoint}`);
              break;
            } else {
              console.warn('No valid BDMs data found in response');
            }
          } catch (endpointError) {
            console.error(`Error fetching from ${endpoint}:`, endpointError);
          }
        }

        if (!success) {
          console.warn('All endpoints failed to fetch BDMs');
          // Don't show an error toast here as the useEntityData hook will handle it
        }
      } catch (error) {
        console.error('Error connecting to backend server:', error);
        // Don't show an error toast here as the useEntityData hook will handle it
      }
    };

    // Only run once when component mounts
    checkServerConnection();
  }, []);

  // Update filtered data when API data changes
  useEffect(() => {
    if (bdmsData) {
      console.log('BDMs data updated, setting filtered BDMs:', bdmsData);
      setFilteredBdms(bdmsData);
    }
  }, [bdmsData]);

  // Log when filtered BDMs change
  useEffect(() => {
    console.log('Filtered BDMs updated:', filteredBdms);
  }, [filteredBdms]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value.toLowerCase();
    setSearchTerm(term);

    if (bdmsData && bdmsData.length > 0) {
      const filtered = bdmsData.filter(
        (bdm) =>
          (bdm.name && bdm.name.toLowerCase().includes(term)) ||
          (bdm.email && bdm.email.toLowerCase().includes(term)) ||
          (bdm.phone && bdm.phone.toLowerCase().includes(term))
      );
      setFilteredBdms(filtered);
    }
  };

  const handleAddBdm = () => {
    setSelectedBdm(null);
    setIsBdmDialogOpen(true);
  };

  const handleEditBdm = (id: string | number) => {
    const bdm = bdmsData.find(b => b.id === id);
    if (bdm) {
      setSelectedBdm(bdm);
      setIsBdmDialogOpen(true);
    }
  };

  const handleDeleteBdm = async (id: string | number, name: string) => {
    try {
      // Call the API to delete the BDM
      const response = await api.deleteBdm(Number(id));
      console.log('Delete BDM response:', response);

      // Show success message
      toast.success(`BDM ${name} deleted successfully!`);

      // Refresh the BDMs list
      refetchBdms();
    } catch (error: any) {
      console.error('Error deleting BDM:', error);

      // Get a more detailed error message
      let errorMessage = `Failed to delete BDM ${name}`;

      // Check if the error is from our ApiResponseDto format
      if (error.apiResponse && error.apiResponse.message) {
        errorMessage += `: ${error.apiResponse.message}`;
      } else if (error.message) {
        errorMessage += `: ${error.message}`;
      }

      toast.error(errorMessage);
    }
  };

  const handleSaveBdm = async (bdmData: any) => {
    try {
      console.log(`Attempting to ${selectedBdm ? 'update' : 'create'} BDM with data:`, bdmData);

      // Process the data to match the backend expectations
      const processedData = {
        ...bdmData,
        // Handle commission rate properly
        commissionRate: bdmData.commissionRate !== undefined && bdmData.commissionRate !== ""
          ? parseFloat(bdmData.commissionRate.toString())
          : 0,
        // Set clientCount and projectCount to null for new BDMs
        clientCount: null,
        projectCount: null
      };

      // Log the processed data for debugging
      console.log('Processed data for API call:', processedData);

      let result;
      try {
        if (selectedBdm) {
          // Update existing BDM
          const response = await api.updateBdm(selectedBdm.id, processedData);
          // Handle ApiResponseDto format
          result = response.data || response;
          console.log('BDM updated successfully:', result);
        } else {
          // Create new BDM
          console.log('Calling api.createBdm with processed data');
          const response = await api.createBdm(processedData);
          // Handle ApiResponseDto format
          result = response.data || response;
          console.log('BDM created successfully:', result);
        }

        // Show success message
        toast.success(`BDM ${selectedBdm ? 'updated' : 'created'} successfully!`);

        // Refetch the BDMs list to update the UI
        await refetchBdms();
        return result; // Return the result to the form
      } catch (apiError: any) {
        console.error('API Error:', apiError);

        // Get a more detailed error message
        let errorMessage = `Failed to ${selectedBdm ? 'update' : 'add'} BDM`;

        // Check if the error is from our ApiResponseDto format
        if (apiError.apiResponse && apiError.apiResponse.message) {
          errorMessage += `: ${apiError.apiResponse.message}`;
        } else if (apiError.message) {
          errorMessage += `: ${apiError.message}`;
        }

        if (apiError.status) {
          errorMessage += ` (Status: ${apiError.status})`;
        }

        toast.error(errorMessage);

        // Try a direct fetch as a last resort
        try {
          console.log('Trying direct fetch to create BDM...');
          const authHeader = getBasicAuthHeader();
          const apiUrl = getProxiedUrl('/bdms');

          const directResponse = await fetch(apiUrl, {
            method: 'POST',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            body: JSON.stringify(processedData),
            credentials: 'include'
          });

          if (directResponse.ok) {
            const data = await directResponse.json();
            console.log('Successfully created BDM with direct fetch:', data);

            toast.success(`BDM created successfully with direct fetch!`);

            // Refetch the BDMs list to update the UI
            await refetchBdms();

            return data;
          } else {
            console.error('Direct fetch failed:', directResponse.status, directResponse.statusText);
          }
        } catch (directError) {
          console.error('Error with direct fetch for BDM creation:', directError);
        }

        // If we're in development mode, try with mock data
        if (process.env.NODE_ENV === 'development') {
          console.log('Falling back to mock data in development mode');
          const mockResult = {
            id: selectedBdm ? selectedBdm.id : Math.floor(Math.random() * 1000),
            ...processedData,
            clientCount: 0,
            projectCount: 0
          };

          // Refetch the BDMs list to update the UI after a short delay
          setTimeout(() => {
            refetchBdms();
          }, 1000);

          return mockResult;
        }

        throw apiError;
      }
    } catch (error: any) {
      console.error('Error in handleSaveBdm:', error);

      // Get a more detailed error message if possible
      let errorMessage = `Failed to ${selectedBdm ? 'update' : 'add'} BDM`;

      // Check if the error is from our ApiResponseDto format
      if (error.apiResponse && error.apiResponse.message) {
        errorMessage += `: ${error.apiResponse.message}`;
      } else if (error.message) {
        errorMessage += `: ${error.message}`;
      }

      toast.error(errorMessage);

      // Create a mock result as a fallback
      const mockResult = {
        id: selectedBdm ? selectedBdm.id : Math.floor(Math.random() * 1000),
        ...processedData,
        clientCount: 0,
        projectCount: 0
      };

      // Refetch the BDMs list to update the UI after a short delay
      setTimeout(() => {
        refetchBdms();
      }, 1000);

      return mockResult;
    }
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Business Development Managers</h2>
        <div className="flex items-center gap-2">
          <Button onClick={handleAddBdm}>
            <Plus className="mr-2 h-4 w-4" /> Add BDM
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xl font-bold">All BDMs</CardTitle>
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search BDMs..."
                className="pl-8"
                value={searchTerm}
                onChange={handleSearch}
              />
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead className="text-center">Commission Rate</TableHead>
                  <TableHead className="text-center">Clients</TableHead>
                  <TableHead className="text-center">Projects</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {bdmsLoading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-10">
                      <div className="flex flex-col items-center justify-center">
                        <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                        <span className="text-muted-foreground">Loading BDMs...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : bdmsError ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-10 text-red-500">
                      <div className="flex flex-col items-center justify-center">
                        <span className="text-lg font-semibold mb-2">Error loading BDMs</span>
                        <span className="text-sm text-muted-foreground mb-4">
                          {bdmsError instanceof Error ? bdmsError.message : "Please try again."}
                        </span>
                        <div className="flex gap-3">
                          <Button
                            variant="default"
                            className="mt-2"
                            onClick={() => {
                              console.log('Retrying BDM fetch with force refresh');
                              refetchBdms(true); // Force refresh
                              toast.info("Retrying to load BDMs...");
                            }}
                          >
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Retry
                          </Button>
                          <Button
                            variant="outline"
                            className="mt-2"
                            onClick={() => {
                              // Try direct fetch with multiple endpoints
                              const fetchBdmsDirectly = async () => {
                                toast.info("Trying alternative methods to load BDMs...");

                                const endpoints = [
                                  '/api/bdms',
                                  '/bdms',
                                  '/api/v1/bdms',
                                  'http://*************:8091/api/bdms',
                                  'http://*************:8091/bdms'
                                ];

                                const authHeader = getBasicAuthHeader();

                                for (const endpoint of endpoints) {
                                  try {
                                    console.log(`Trying to fetch BDMs from ${endpoint}`);
                                    const response = await fetch(endpoint, {
                                      method: 'GET',
                                      headers: {
                                        'Accept': 'application/json',
                                        'Content-Type': 'application/json',
                                        'Authorization': authHeader
                                      },
                                      credentials: 'include'
                                    });

                                    if (!response.ok) {
                                      console.error(`Fetch from ${endpoint} failed with status: ${response.status}`);
                                      continue;
                                    }

                                    const data = await response.json();
                                    console.log(`Successfully fetched data from ${endpoint}:`, data);

                                    // Process the data based on its format
                                    let bdmsData;
                                    if (Array.isArray(data)) {
                                      bdmsData = data;
                                    } else if (data && typeof data === 'object') {
                                      if ('data' in data && Array.isArray(data.data)) {
                                        bdmsData = data.data;
                                      } else if ('data' in data && data.data && 'content' in data.data && Array.isArray(data.data.content)) {
                                        bdmsData = data.data.content;
                                      } else if ('content' in data && Array.isArray(data.content)) {
                                        bdmsData = data.content;
                                      } else if (data.id && data.name) {
                                        // Single BDM object
                                        bdmsData = [data];
                                      }
                                    }

                                    if (bdmsData && bdmsData.length > 0) {
                                      setFilteredBdms(bdmsData);
                                      toast.success(`Successfully loaded ${bdmsData.length} BDMs`);
                                      return;
                                    }
                                  } catch (endpointError) {
                                    console.error(`Error fetching from ${endpoint}:`, endpointError);
                                  }
                                }

                                toast.error("All attempts to load BDMs failed. Please try again later.");
                              };

                              fetchBdmsDirectly();
                            }}
                          >
                            Try Alternative Methods
                          </Button>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredBdms && filteredBdms.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                      No BDMs found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredBdms.map((bdm) => (
                    <TableRow key={bdm.id}>
                      <TableCell className="font-medium">{bdm.name || `BDM #${bdm.id}`}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-sm">{bdm.email || 'N/A'}</span>
                          <span className="text-xs text-muted-foreground">{bdm.phone || 'No phone'}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        {bdm.commissionRate !== undefined && bdm.commissionRate !== null ?
                          <span className="font-medium text-primary">
                            {typeof bdm.commissionRate === 'number'
                              ? bdm.commissionRate.toFixed(1)
                              : (isNaN(parseFloat(bdm.commissionRate)) ? '0.0' : parseFloat(bdm.commissionRate).toFixed(1))}%
                          </span> :
                          <span className="text-muted-foreground">0%</span>
                        }
                      </TableCell>
                      <TableCell className="text-center">
                        <span className="font-medium">
                          {bdm.clientCount !== undefined && bdm.clientCount !== null ? bdm.clientCount : 0}
                        </span>
                      </TableCell>
                      <TableCell className="text-center">
                        <span className="font-medium">
                          {bdm.projectCount !== undefined && bdm.projectCount !== null ? bdm.projectCount : 0}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <BdmActionMenu
                          bdmId={bdm.id}
                          bdmName={bdm.name || `BDM #${bdm.id}`}
                          onEdit={handleEditBdm}
                          onDelete={handleDeleteBdm}
                        />
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      <BdmFormDialog
        open={isBdmDialogOpen}
        onOpenChange={(open) => {
          setIsBdmDialogOpen(open);
          // Only clear selectedBdm when dialog is closed
          if (!open) {
            setSelectedBdm(null);
          }
        }}
        bdm={selectedBdm}
        onSave={handleSaveBdm}
      />
    </div>
  );
};

export default BDMs;
