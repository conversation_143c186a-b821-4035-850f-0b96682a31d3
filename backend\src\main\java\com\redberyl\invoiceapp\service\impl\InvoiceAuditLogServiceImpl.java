package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.InvoiceAuditLogDto;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.InvoiceAuditLog;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ForeignKeyViolationException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.InvoiceAuditLogRepository;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.service.InvoiceAuditLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class InvoiceAuditLogServiceImpl implements InvoiceAuditLogService {

    @Autowired
    private InvoiceAuditLogRepository invoiceAuditLogRepository;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Override
    public List<InvoiceAuditLogDto> getAllInvoiceAuditLogs() {
        List<InvoiceAuditLog> invoiceAuditLogs = invoiceAuditLogRepository.findAll();
        if (invoiceAuditLogs.isEmpty()) {
            throw new NoContentException("No invoice audit logs found");
        }
        return invoiceAuditLogs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public InvoiceAuditLogDto getInvoiceAuditLogById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice audit log ID cannot be null");
        }

        InvoiceAuditLog invoiceAuditLog = invoiceAuditLogRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice Audit Log not found with id: " + id));
        return convertToDto(invoiceAuditLog);
    }

    @Override
    public List<InvoiceAuditLogDto> getInvoiceAuditLogsByInvoiceId(Long invoiceId) {
        if (invoiceId == null) {
            throw new NullConstraintViolationException("invoiceId", "Invoice ID cannot be null");
        }

        // Check if invoice exists
        if (!invoiceRepository.existsById(invoiceId)) {
            throw new ResourceNotFoundException("Invoice not found with id: " + invoiceId);
        }

        List<InvoiceAuditLog> invoiceAuditLogs = invoiceAuditLogRepository.findByInvoiceId(invoiceId);
        if (invoiceAuditLogs.isEmpty()) {
            throw new NoContentException("No invoice audit logs found for invoice with id: " + invoiceId);
        }

        return invoiceAuditLogs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private void validateInvoiceAuditLogDto(InvoiceAuditLogDto invoiceAuditLogDto) {
        if (invoiceAuditLogDto == null) {
            throw new NullConstraintViolationException("invoiceAuditLogDto", "Invoice audit log data cannot be null");
        }

        if (invoiceAuditLogDto.getInvoiceId() == null) {
            throw new NullConstraintViolationException("invoiceId", "Invoice ID cannot be null");
        }

        if (!invoiceRepository.existsById(invoiceAuditLogDto.getInvoiceId())) {
            throw new ForeignKeyViolationException("invoiceId",
                    "Invoice not found with id: " + invoiceAuditLogDto.getInvoiceId());
        }

        if (!StringUtils.hasText(invoiceAuditLogDto.getAction())) {
            throw new NullConstraintViolationException("action", "Action cannot be empty");
        }
    }

    @Override
    @Transactional
    public InvoiceAuditLogDto createInvoiceAuditLog(InvoiceAuditLogDto invoiceAuditLogDto) {
        validateInvoiceAuditLogDto(invoiceAuditLogDto);

        try {
            InvoiceAuditLog invoiceAuditLog = convertToEntity(invoiceAuditLogDto);
            InvoiceAuditLog savedInvoiceAuditLog = invoiceAuditLogRepository.save(invoiceAuditLog);
            return convertToDto(savedInvoiceAuditLog);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error creating invoice audit log: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating invoice audit log", e);
        }
    }

    @Override
    @Transactional
    public void deleteInvoiceAuditLog(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Invoice audit log ID cannot be null");
        }

        if (!invoiceAuditLogRepository.existsById(id)) {
            throw new ResourceNotFoundException("Invoice Audit Log not found with id: " + id);
        }

        try {
            invoiceAuditLogRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete invoice audit log because it is referenced by other entities",
                        e);
            } else {
                throw new CustomException("Error deleting invoice audit log: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting invoice audit log", e);
        }
    }

    private InvoiceAuditLogDto convertToDto(InvoiceAuditLog invoiceAuditLog) {
        return InvoiceAuditLogDto.builder()
                .id(invoiceAuditLog.getId())
                .invoiceId(invoiceAuditLog.getInvoice() != null ? invoiceAuditLog.getInvoice().getId() : null)
                .action(invoiceAuditLog.getAction())
                .performedBy(invoiceAuditLog.getPerformedBy())
                .build();
    }

    private InvoiceAuditLog convertToEntity(InvoiceAuditLogDto invoiceAuditLogDto) {
        InvoiceAuditLog invoiceAuditLog = new InvoiceAuditLog();
        invoiceAuditLog.setId(invoiceAuditLogDto.getId());

        if (invoiceAuditLogDto.getInvoiceId() != null) {
            Invoice invoice = invoiceRepository.findById(invoiceAuditLogDto.getInvoiceId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Invoice not found with id: " + invoiceAuditLogDto.getInvoiceId()));
            invoiceAuditLog.setInvoice(invoice);
        }

        invoiceAuditLog.setAction(invoiceAuditLogDto.getAction());
        invoiceAuditLog.setPerformedBy(invoiceAuditLogDto.getPerformedBy());

        return invoiceAuditLog;
    }
}
