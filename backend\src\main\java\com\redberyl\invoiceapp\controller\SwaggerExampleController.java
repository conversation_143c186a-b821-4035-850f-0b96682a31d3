package com.redberyl.invoiceapp.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/swagger-examples")
@Tag(name = "Swagger Examples", description = "Examples for Swagger documentation")
public class SwaggerExampleController {

    @GetMapping("/tax-rate-create")
    @Operation(summary = "Example for creating a tax rate", 
               description = "This is just an example of the JSON format to use when creating a tax rate")
    public ResponseEntity<Map<String, Object>> getTaxRateCreateExample() {
        Map<String, Object> example = new HashMap<>();
        example.put("taxTypeId", 1);
        example.put("rate", 18.0);
        example.put("effectiveFrom", "2025-04-17");
        example.put("effectiveTo", "2025-06-22");
        
        return ResponseEntity.ok(example);
    }
}
