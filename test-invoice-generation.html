<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Invoice Generation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>Invoice Generation API Test</h1>
    
    <div class="test-section">
        <h3>1. Test Backend Connection</h3>
        <button onclick="testBackend()">Test Backend API</button>
        <div id="backend-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. Test Invoice Generation</h3>
        <button onclick="testInvoiceGeneration()">Test Invoice Generation</button>
        <div id="invoice-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. Test Sample PDF Generation</h3>
        <button onclick="testSamplePdf()">Generate Sample PDF</button>
        <div id="pdf-result" class="result"></div>
    </div>

    <script>
        async function testBackend() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.innerHTML = '<div class="info">Testing backend connection...</div>';
            
            try {
                const response = await fetch('http://*************:8091/api/invoice-generation/test', {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/plain',
                    }
                });
                
                if (response.ok) {
                    const text = await response.text();
                    resultDiv.innerHTML = `<div class="success">✅ Backend is working! Response: ${text}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Backend error: ${response.status} - ${response.statusText}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Connection failed: ${error.message}</div>`;
            }
        }

        async function testInvoiceGeneration() {
            const resultDiv = document.getElementById('invoice-result');
            resultDiv.innerHTML = '<div class="info">Testing invoice generation...</div>';
            
            const sampleInvoiceData = {
                invoiceNumber: "TEST-001",
                billingAmount: 30000,
                taxAmount: 5400,
                totalAmount: 35400,
                clientId: 1,
                projectId: 1,
                candidateId: 1,
                invoiceTypeId: 1,
                hsnCodeId: 1,
                redberylAccountId: 1
            };
            
            try {
                const response = await fetch('http://*************:8091/api/invoice-generation/pdf/preview', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/pdf'
                    },
                    body: JSON.stringify(sampleInvoiceData)
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = URL.createObjectURL(blob);
                    window.open(url, '_blank');
                    resultDiv.innerHTML = '<div class="success">✅ Invoice generated successfully! PDF opened in new tab.</div>';
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `<div class="error">❌ Invoice generation failed: ${response.status} - ${errorText}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Invoice generation error: ${error.message}</div>`;
            }
        }

        async function testSamplePdf() {
            const resultDiv = document.getElementById('pdf-result');
            resultDiv.innerHTML = '<div class="info">Testing sample PDF generation...</div>';
            
            try {
                const response = await fetch('http://*************:8091/api/invoice-generation/pdf/preview/minimal', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/pdf'
                    },
                    body: JSON.stringify({})
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = URL.createObjectURL(blob);
                    window.open(url, '_blank');
                    resultDiv.innerHTML = '<div class="success">✅ Sample PDF generated successfully! PDF opened in new tab.</div>';
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `<div class="error">❌ Sample PDF generation failed: ${response.status} - ${errorText}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Sample PDF error: ${error.message}</div>`;
            }
        }

        // Auto-test backend on page load
        window.onload = function() {
            setTimeout(testBackend, 1000);
        };
    </script>
</body>
</html>
