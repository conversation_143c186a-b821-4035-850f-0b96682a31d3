package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.DocumentTemplateDto;
import com.redberyl.invoiceapp.dto.DocumentTemplateVersionDto;
import com.redberyl.invoiceapp.entity.DocumentTemplate;
import com.redberyl.invoiceapp.entity.DocumentTemplateVersion;
import com.redberyl.invoiceapp.repository.DocumentTemplateRepository;
import com.redberyl.invoiceapp.repository.DocumentTemplateVersionRepository;
import com.redberyl.invoiceapp.service.DocumentTemplateVersionService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class DocumentTemplateVersionServiceImpl implements DocumentTemplateVersionService {

    @Autowired
    private DocumentTemplateVersionRepository documentTemplateVersionRepository;

    @Autowired
    private DocumentTemplateRepository documentTemplateRepository;

    @Override
    public List<DocumentTemplateVersionDto> getAllDocumentTemplateVersions() {
        return documentTemplateVersionRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public DocumentTemplateVersionDto getDocumentTemplateVersionById(Long id) {
        DocumentTemplateVersion documentTemplateVersion = documentTemplateVersionRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Document Template Version not found with id: " + id));
        return convertToDto(documentTemplateVersion);
    }

    @Override
    public List<DocumentTemplateVersionDto> getDocumentTemplateVersionsByTemplateId(Long templateId) {
        return documentTemplateVersionRepository.findByTemplateId(templateId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public Optional<DocumentTemplateVersionDto> getDocumentTemplateVersionByTemplateIdAndVersionNumber(Long templateId,
            Integer versionNumber) {
        return documentTemplateVersionRepository.findByTemplateIdAndVersionNumber(templateId, versionNumber)
                .map(this::convertToDto);
    }

    @Override
    public List<DocumentTemplateVersionDto> getActiveDocumentTemplateVersionsByTemplateId(Long templateId) {
        return documentTemplateVersionRepository.findByTemplateIdAndIsActiveTrue(templateId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public DocumentTemplateVersionDto createDocumentTemplateVersion(
            DocumentTemplateVersionDto documentTemplateVersionDto) {
        DocumentTemplateVersion documentTemplateVersion = convertToEntity(documentTemplateVersionDto);
        DocumentTemplateVersion savedDocumentTemplateVersion = documentTemplateVersionRepository
                .save(documentTemplateVersion);
        return convertToDto(savedDocumentTemplateVersion);
    }

    @Override
    @Transactional
    public DocumentTemplateVersionDto updateDocumentTemplateVersion(Long id,
            DocumentTemplateVersionDto documentTemplateVersionDto) {
        DocumentTemplateVersion existingDocumentTemplateVersion = documentTemplateVersionRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Document Template Version not found with id: " + id));

        existingDocumentTemplateVersion.setVersionNumber(documentTemplateVersionDto.getVersionNumber());
        existingDocumentTemplateVersion.setContent(documentTemplateVersionDto.getContent());
        existingDocumentTemplateVersion.setCreatedBy(documentTemplateVersionDto.getCreatedBy());
        existingDocumentTemplateVersion.setIsActive(documentTemplateVersionDto.getIsActive());

        DocumentTemplateVersion updatedDocumentTemplateVersion = documentTemplateVersionRepository
                .save(existingDocumentTemplateVersion);
        return convertToDto(updatedDocumentTemplateVersion);
    }

    @Override
    @Transactional
    public DocumentTemplateVersionDto activateDocumentTemplateVersion(Long id) {
        DocumentTemplateVersion documentTemplateVersion = documentTemplateVersionRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Document Template Version not found with id: " + id));

        documentTemplateVersion.setIsActive(true);
        DocumentTemplateVersion updatedDocumentTemplateVersion = documentTemplateVersionRepository
                .save(documentTemplateVersion);
        return convertToDto(updatedDocumentTemplateVersion);
    }

    @Override
    @Transactional
    public DocumentTemplateVersionDto deactivateDocumentTemplateVersion(Long id) {
        DocumentTemplateVersion documentTemplateVersion = documentTemplateVersionRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Document Template Version not found with id: " + id));

        documentTemplateVersion.setIsActive(false);
        DocumentTemplateVersion updatedDocumentTemplateVersion = documentTemplateVersionRepository
                .save(documentTemplateVersion);
        return convertToDto(updatedDocumentTemplateVersion);
    }

    @Override
    @Transactional
    public void deleteDocumentTemplateVersion(Long id) {
        if (!documentTemplateVersionRepository.existsById(id)) {
            throw new EntityNotFoundException("Document Template Version not found with id: " + id);
        }
        documentTemplateVersionRepository.deleteById(id);
    }

    private DocumentTemplateVersionDto convertToDto(DocumentTemplateVersion documentTemplateVersion) {
        // Create a DocumentTemplateDto from the template entity
        DocumentTemplateDto templateDto = null;
        if (documentTemplateVersion.getTemplate() != null) {
            templateDto = DocumentTemplateDto.builder()
                    .id(documentTemplateVersion.getTemplate().getId())
                    .name(documentTemplateVersion.getTemplate().getName())
                    .templateType(documentTemplateVersion.getTemplate().getTemplateType())
                    .filePath(documentTemplateVersion.getTemplate().getFilePath())
                    .build();
        }
                
        // Build the DocumentTemplateVersionDto with the template object
        DocumentTemplateVersionDto.DocumentTemplateVersionDtoBuilder builder = DocumentTemplateVersionDto.builder()
                .id(documentTemplateVersion.getId())
                .versionNumber(documentTemplateVersion.getVersionNumber())
                .content(documentTemplateVersion.getContent())
                .createdBy(documentTemplateVersion.getCreatedBy())
                .isActive(documentTemplateVersion.getIsActive());
                
        if (documentTemplateVersion.getTemplate() != null) {
            builder.templateId(documentTemplateVersion.getTemplate().getId());
            builder.template(templateDto); // Include the full template object
        }
        
        return builder.build();
    }

    private DocumentTemplateVersion convertToEntity(DocumentTemplateVersionDto documentTemplateVersionDto) {
        DocumentTemplateVersion documentTemplateVersion;

        if (documentTemplateVersionDto.getId() != null) {
            // If updating an existing entity, fetch it first to preserve relationships
            documentTemplateVersion = documentTemplateVersionRepository.findById(documentTemplateVersionDto.getId())
                    .orElse(new DocumentTemplateVersion());
        } else {
            documentTemplateVersion = new DocumentTemplateVersion();
        }

        if (documentTemplateVersionDto.getTemplateId() != null) {
            DocumentTemplate documentTemplate = documentTemplateRepository
                    .findById(documentTemplateVersionDto.getTemplateId())
                    .orElseThrow(() -> new EntityNotFoundException(
                            "Document Template not found with id: " + documentTemplateVersionDto.getTemplateId()));

            // Properly maintain bidirectional relationship
            documentTemplateVersion.setTemplate(documentTemplate);

            // Add this version to the template's versions collection if it's not already
            // there
            if (documentTemplateVersion.getId() == null) {
                documentTemplate.getVersions().add(documentTemplateVersion);
            }
        }

        documentTemplateVersion.setVersionNumber(documentTemplateVersionDto.getVersionNumber());
        documentTemplateVersion.setContent(documentTemplateVersionDto.getContent());
        documentTemplateVersion.setCreatedBy(documentTemplateVersionDto.getCreatedBy());
        documentTemplateVersion.setIsActive(documentTemplateVersionDto.getIsActive());

        return documentTemplateVersion;
    }
}
