import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Eye } from "lucide-react";

interface ViewButtonProps {
  onClick: () => void;
}

const ViewButton: React.FC<ViewButtonProps> = ({ onClick }) => {
  return (
    <Button
      variant="ghost"
      size="sm"
      className="flex items-center text-blue-600 hover:bg-blue-50"
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        onClick();
      }}
    >
      <Eye className="h-4 w-4 mr-1" />
      <span>View Details</span>
    </Button>
  );
};

export default ViewButton;
