package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.BdmDto;
import com.redberyl.invoiceapp.dto.ClientDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * Controller to provide examples of client-related JSON structures
 */
@RestController
@RequestMapping("/api/examples/clients")
@Tag(name = "Client Examples", description = "Examples for client-related JSON structures")
public class ClientExampleController {

    @GetMapping("/client-create-simple")
    @Operation(
        summary = "Example for creating a client (simple format)",
        description = "This is an example of the JSON format to use when creating a client"
    )
    public ResponseEntity<ClientDto> getClientCreateSimpleExample() {
        ClientDto example = ClientDto.builder()
            .name("ABC Technologies Pvt Ltd")
            .build();

        return ResponseEntity.ok(example);
    }

    @GetMapping("/client-response-example")
    @Operation(
        summary = "Example of client response format",
        description = "This is an example of how client data is returned from the API"
    )
    public ResponseEntity<ClientDto> getClientResponseExample() {
        ClientDto example = ClientDto.builder()
            .id(1L)
            .name("ABC Technologies Pvt Ltd")
            .build();

        // Set the audit fields
        example.setCreatedAt(java.time.LocalDateTime.now());
        example.setUpdatedAt(java.time.LocalDateTime.now());

        return ResponseEntity.ok(example);
    }

    @GetMapping("/client-with-bdm-object")
    @Operation(
        summary = "Example of client with additional object (NOT SUPPORTED)",
        description = "This format is NOT supported for creating or updating clients. It's shown here only as an example of what NOT to do."
    )
    public ResponseEntity<Map<String, Object>> getClientWithBdmObjectExample() {
        // Create a BDM object
        BdmDto bdm = BdmDto.builder()
            .id(1L)
            .name("John Doe")
            .build();

        // Create a client
        ClientDto client = ClientDto.builder()
            .id(1L)
            .name("ABC Technologies Pvt Ltd")
            .build();

        // Create a map to represent the incorrect format
        Map<String, Object> incorrectFormat = new HashMap<>();
        incorrectFormat.put("id", client.getId());
        incorrectFormat.put("name", client.getName());
        incorrectFormat.put("bdm", bdm);  // This is the incorrect format - sending the entire BDM object

        return ResponseEntity.ok(incorrectFormat);
    }
}
