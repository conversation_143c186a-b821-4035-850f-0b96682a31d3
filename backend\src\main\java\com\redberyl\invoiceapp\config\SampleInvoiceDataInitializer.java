package com.redberyl.invoiceapp.config;

import com.redberyl.invoiceapp.entity.*;
import com.redberyl.invoiceapp.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Initializes sample invoice data if the table is empty
 */
@Configuration
public class SampleInvoiceDataInitializer {

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private CandidateRepository candidateRepository;

    @Autowired
    private InvoiceTypeRepository invoiceTypeRepository;

    @Autowired
    private StaffingTypeRepository staffingTypeRepository;

    @Autowired
    private HsnCodeRepository hsnCodeRepository;

    @Autowired
    private RedberylAccountRepository redberylAccountRepository;

    @Bean
    @Order(10) // Run after other initializers
    public CommandLineRunner initSampleInvoiceData() {
        return args -> {
            try {
                // Only add sample data if the invoices table is empty
                if (invoiceRepository.count() == 0) {
                    System.out.println("Initializing sample invoice data...");

                    // Get required entities
                    List<Client> clients = clientRepository.findAll();
                    List<Project> projects = projectRepository.findAll();
                    List<Candidate> candidates = candidateRepository.findAll();
                    List<InvoiceType> invoiceTypes = invoiceTypeRepository.findAll();
                    List<StaffingType> staffingTypes = staffingTypeRepository.findAll();
                    List<HsnCode> hsnCodes = hsnCodeRepository.findAll();
                    List<RedberylAccount> redberylAccounts = redberylAccountRepository.findAll();

                    // Create sample invoices if we have the required data
                    if (!clients.isEmpty() && !projects.isEmpty() && !candidates.isEmpty() && !invoiceTypes.isEmpty()) {
                        createSampleInvoice("INV-001", clients.get(0), projects.get(0),
                                          candidates.get(0), invoiceTypes.get(0),
                                          staffingTypes.isEmpty() ? null : staffingTypes.get(0),
                                          hsnCodes.isEmpty() ? null : hsnCodes.get(0),
                                          redberylAccounts.isEmpty() ? null : redberylAccounts.get(0),
                                          new BigDecimal("30600.00"), new BigDecimal("5400.00"), new BigDecimal("35400.00"));

                        if (clients.size() > 1 && projects.size() > 1 && candidates.size() > 1) {
                            createSampleInvoice("INV-004", clients.get(0), projects.get(0),
                                              candidates.get(0), invoiceTypes.get(0),
                                              staffingTypes.isEmpty() ? null : staffingTypes.get(0),
                                              hsnCodes.isEmpty() ? null : hsnCodes.get(0),
                                              redberylAccounts.isEmpty() ? null : redberylAccounts.get(0),
                                              new BigDecimal("30600.00"), new BigDecimal("5400.00"), new BigDecimal("35400.00"));
                        }

                        System.out.println("Sample invoice data initialized successfully!");
                    } else {
                        System.out.println("Cannot create sample invoices: missing required data (clients, projects, candidates, or invoice types)");
                    }
                } else {
                    System.out.println("Invoices already exist in the database. Skipping sample invoice initialization.");
                }

                // Print existing invoices for verification
                System.out.println("Existing invoices in database:");
                invoiceRepository.findAll().forEach(invoice ->
                    System.out.println("ID: " + invoice.getId() +
                                     ", Number: " + invoice.getInvoiceNumber() +
                                     ", Client: " + (invoice.getClient() != null ? invoice.getClient().getName() : "N/A") +
                                     ", Total: " + invoice.getTotalAmount()));

            } catch (Exception e) {
                System.err.println("Error initializing sample invoice data: " + e.getMessage());
                e.printStackTrace();
            }
        };
    }

    /**
     * Helper method to create a sample invoice
     */
    private void createSampleInvoice(String invoiceNumber, Client client, Project project,
                                   Candidate candidate, InvoiceType invoiceType, StaffingType staffingType,
                                   HsnCode hsnCode, RedberylAccount redberylAccount,
                                   BigDecimal billingAmount, BigDecimal taxAmount, BigDecimal totalAmount) {
        Invoice invoice = new Invoice();
        invoice.setInvoiceNumber(invoiceNumber);
        invoice.setClient(client);
        invoice.setProject(project);
        invoice.setCandidate(candidate);
        invoice.setInvoiceType(invoiceType);
        invoice.setStaffingType(staffingType);
        invoice.setHsnCode(hsnCode);
        invoice.setRedberylAccount(redberylAccount);
        invoice.setBillingAmount(billingAmount);
        invoice.setTaxAmount(taxAmount);
        invoice.setTotalAmount(totalAmount);
        invoice.setInvoiceDate(LocalDate.now().minusDays(10));
        invoice.setDueDate(LocalDate.now().plusDays(20));
        invoice.setIsRecurring(false);
        invoice.setPublishedToFinance(false);

        invoiceRepository.save(invoice);
    }
}
