package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.InvoiceTypeDto;
import com.redberyl.invoiceapp.service.InvoiceTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/invoice-types")
@Tag(name = "Invoice Type", description = "Invoice Type management API")
public class InvoiceTypeController {

    @Autowired
    private InvoiceTypeService invoiceTypeService;

    @GetMapping
    @Operation(summary = "Get all invoice types", description = "Retrieve a list of all invoice types")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceTypeDto>> getAllInvoiceTypes() {
        List<InvoiceTypeDto> invoiceTypes = invoiceTypeService.getAllInvoiceTypes();
        return new ResponseEntity<>(invoiceTypes, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get invoice type by ID", description = "Retrieve an invoice type by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceTypeDto> getInvoiceTypeById(@PathVariable Long id) {
        InvoiceTypeDto invoiceType = invoiceTypeService.getInvoiceTypeById(id);
        return new ResponseEntity<>(invoiceType, HttpStatus.OK);
    }

    @GetMapping("/type/{invoiceType}")
    @Operation(summary = "Get invoice type by type", description = "Retrieve an invoice type by its type")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceTypeDto> getInvoiceTypeByType(@PathVariable String invoiceType) {
        InvoiceTypeDto type = invoiceTypeService.getInvoiceTypeByType(invoiceType);
        return new ResponseEntity<>(type, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create invoice type", description = "Create a new invoice type")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceTypeDto> createInvoiceType(@Valid @RequestBody InvoiceTypeDto invoiceTypeDto) {
        InvoiceTypeDto createdInvoiceType = invoiceTypeService.createInvoiceType(invoiceTypeDto);
        return new ResponseEntity<>(createdInvoiceType, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update invoice type", description = "Update an existing invoice type")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceTypeDto> updateInvoiceType(@PathVariable Long id, @Valid @RequestBody InvoiceTypeDto invoiceTypeDto) {
        InvoiceTypeDto updatedInvoiceType = invoiceTypeService.updateInvoiceType(id, invoiceTypeDto);
        return new ResponseEntity<>(updatedInvoiceType, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete invoice type", description = "Delete an invoice type by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteInvoiceType(@PathVariable Long id) {
        invoiceTypeService.deleteInvoiceType(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
