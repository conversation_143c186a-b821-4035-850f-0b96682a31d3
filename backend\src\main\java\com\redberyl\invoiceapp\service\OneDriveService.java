package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.OneDriveUploadResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * Service interface for OneDrive operations
 */
public interface OneDriveService {
    
    /**
     * Get authorization URL for OneDrive access
     */
    String getAuthorizationUrl();
    
    /**
     * Handle OAuth callback and get access token
     */
    String handleCallback(String code, String state);
    
    /**
     * Upload file to OneDrive
     */
    OneDriveUploadResponse uploadFile(String accessToken, byte[] fileContent, String fileName, String folderPath);
    
    /**
     * Upload PDF invoice to OneDrive
     */
    OneDriveUploadResponse uploadInvoicePdf(String accessToken, byte[] pdfContent, String invoiceNumber);
    
    /**
     * Check if user is authenticated with OneDrive
     */
    boolean isAuthenticated(String accessToken);
    
    /**
     * Refresh access token
     */
    String refreshAccessToken(String refreshToken);
}
